import type { SVGGroup } from '../../diagram-api/types.js';
import type { DrawableElem, XYChartConfig, XYChartData, XYChartThemeConfig } from './chartBuilder/interfaces.js';
interface NormalTextType {
    type: 'text';
    text: string;
}
declare function setTmpSVGG(SVGG: SVGGroup): void;
declare function setOrientation(orientation: string): void;
declare function setXAxisTitle(title: NormalTextType): void;
declare function setXAxisRangeData(min: number, max: number): void;
declare function setXAxisBand(categories: NormalTextType[]): void;
declare function setYAxisTitle(title: NormalTextType): void;
declare function setYAxisRangeData(min: number, max: number): void;
declare function setLineData(title: NormalTextType, data: number[]): void;
declare function setBarData(title: NormalTextType, data: number[]): void;
declare function getDrawableElem(): DrawableElem[];
declare function getChartThemeConfig(): XYChartThemeConfig;
declare function getChartConfig(): XYChartConfig;
declare function getXYChartData(): XYChartData;
declare const _default: {
    getDrawableElem: typeof getDrawableElem;
    clear: () => void;
    setAccTitle: (txt: string) => void;
    getAccTitle: () => string;
    setDiagramTitle: (txt: string) => void;
    getDiagramTitle: () => string;
    getAccDescription: () => string;
    setAccDescription: (txt: string) => void;
    setOrientation: typeof setOrientation;
    setXAxisTitle: typeof setXAxisTitle;
    setXAxisRangeData: typeof setXAxisRangeData;
    setXAxisBand: typeof setXAxisBand;
    setYAxisTitle: typeof setYAxisTitle;
    setYAxisRangeData: typeof setYAxisRangeData;
    setLineData: typeof setLineData;
    setBarData: typeof setBarData;
    setTmpSVGG: typeof setTmpSVGG;
    getChartThemeConfig: typeof getChartThemeConfig;
    getChartConfig: typeof getChartConfig;
    getXYChartData: typeof getXYChartData;
};
export default _default;
