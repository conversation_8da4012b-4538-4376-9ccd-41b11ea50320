{"version": 3, "sources": ["../../../src/diagrams/user-journey/parser/journey.jison", "../../../src/diagrams/user-journey/journeyDb.js", "../../../src/diagrams/user-journey/styles.js", "../../../src/diagrams/user-journey/svgDraw.js", "../../../src/diagrams/user-journey/journeyRenderer.ts", "../../../src/diagrams/user-journey/journeyDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[6,8,10,11,12,14,16,17,18],$V1=[1,9],$V2=[1,10],$V3=[1,11],$V4=[1,12],$V5=[1,13],$V6=[1,14];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"journey\":4,\"document\":5,\"EOF\":6,\"line\":7,\"SPACE\":8,\"statement\":9,\"NEWLINE\":10,\"title\":11,\"acc_title\":12,\"acc_title_value\":13,\"acc_descr\":14,\"acc_descr_value\":15,\"acc_descr_multiline_value\":16,\"section\":17,\"taskName\":18,\"taskData\":19,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"journey\",6:\"EOF\",8:\"SPACE\",10:\"NEWLINE\",11:\"title\",12:\"acc_title\",13:\"acc_title_value\",14:\"acc_descr\",15:\"acc_descr_value\",16:\"acc_descr_multiline_value\",17:\"section\",18:\"taskName\",19:\"taskData\"},\nproductions_: [0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,2]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 1:\n return $$[$0-1]; \nbreak;\ncase 2:\n this.$ = [] \nbreak;\ncase 3:\n$$[$0-1].push($$[$0]);this.$ = $$[$0-1]\nbreak;\ncase 4: case 5:\n this.$ = $$[$0] \nbreak;\ncase 6: case 7:\n this.$=[];\nbreak;\ncase 8:\nyy.setDiagramTitle($$[$0].substr(6));this.$=$$[$0].substr(6);\nbreak;\ncase 9:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 10: case 11:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 12:\nyy.addSection($$[$0].substr(8));this.$=$$[$0].substr(8);\nbreak;\ncase 13:\nyy.addTask($$[$0-1], $$[$0]);this.$='task';\nbreak;\n}\n},\ntable: [{3:1,4:[1,2]},{1:[3]},o($V0,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:$V1,12:$V2,14:$V3,16:$V4,17:$V5,18:$V6},o($V0,[2,7],{1:[2,1]}),o($V0,[2,3]),{9:15,11:$V1,12:$V2,14:$V3,16:$V4,17:$V5,18:$V6},o($V0,[2,5]),o($V0,[2,6]),o($V0,[2,8]),{13:[1,16]},{15:[1,17]},o($V0,[2,11]),o($V0,[2,12]),{19:[1,18]},o($V0,[2,4]),o($V0,[2,9]),o($V0,[2,10]),o($V0,[2,13])],\ndefaultActions: {},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:/* skip comments */\nbreak;\ncase 1:/* skip comments */\nbreak;\ncase 2:return 10;\nbreak;\ncase 3:/* skip whitespace */\nbreak;\ncase 4:/* skip comments */\nbreak;\ncase 5:return 4;\nbreak;\ncase 6:return 11;\nbreak;\ncase 7: this.begin(\"acc_title\");return 12; \nbreak;\ncase 8: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 9: this.begin(\"acc_descr\");return 14; \nbreak;\ncase 10: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 11: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 12: this.popState(); \nbreak;\ncase 13:return \"acc_descr_multiline_value\";\nbreak;\ncase 14:return 17;\nbreak;\ncase 15:return 18;\nbreak;\ncase 16:return 19;\nbreak;\ncase 17:return ':';\nbreak;\ncase 18:return 6;\nbreak;\ncase 19:return 'INVALID';\nbreak;\n}\n},\nrules: [/^(?:%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:[\\n]+)/i,/^(?:\\s+)/i,/^(?:#[^\\n]*)/i,/^(?:journey\\b)/i,/^(?:title\\s[^#\\n;]+)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:section\\s[^#:\\n;]+)/i,/^(?:[^#:\\n;]+)/i,/^(?::[^#\\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],\nconditions: {\"acc_descr_multiline\":{\"rules\":[12,13],\"inclusive\":false},\"acc_descr\":{\"rules\":[10],\"inclusive\":false},\"acc_title\":{\"rules\":[8],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18,19],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  clear as commonClear,\n} from '../common/commonDb.js';\n\nlet currentSection = '';\n\nconst sections = [];\nconst tasks = [];\nconst rawTasks = [];\n\nexport const clear = function () {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = '';\n  rawTasks.length = 0;\n  commonClear();\n};\n\nexport const addSection = function (txt) {\n  currentSection = txt;\n  sections.push(txt);\n};\n\nexport const getSections = function () {\n  return sections;\n};\n\nexport const getTasks = function () {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n\n  tasks.push(...rawTasks);\n\n  return tasks;\n};\n\nconst updateActors = function () {\n  const tempActors = [];\n  tasks.forEach((task) => {\n    if (task.people) {\n      tempActors.push(...task.people);\n    }\n  });\n\n  const unique = new Set(tempActors);\n  return [...unique].sort();\n};\n\nexport const addTask = function (descr, taskData) {\n  const pieces = taskData.substr(1).split(':');\n\n  let score = 0;\n  let peeps = [];\n  if (pieces.length === 1) {\n    score = Number(pieces[0]);\n    peeps = [];\n  } else {\n    score = Number(pieces[0]);\n    peeps = pieces[1].split(',');\n  }\n  const peopleList = peeps.map((s) => s.trim());\n\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    people: peopleList,\n    task: descr,\n    score,\n  };\n\n  rawTasks.push(rawTask);\n};\n\nexport const addTaskOrg = function (descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: [],\n  };\n  tasks.push(newTask);\n};\n\nconst compileTasks = function () {\n  const compileTask = function (pos) {\n    return rawTasks[pos].processed;\n  };\n\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n};\n\nconst getActors = function () {\n  return updateActors();\n};\n\nexport default {\n  getConfig: () => getConfig().journey,\n  clear,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  getActors,\n};\n", "import { getIconStyles } from '../globalStyles.js';\n\nconst getStyles = (options) =>\n  `.label {\n    font-family: ${options.fontFamily};\n    color: ${options.textColor};\n  }\n  .mouth {\n    stroke: #666;\n  }\n\n  line {\n    stroke: ${options.textColor}\n  }\n\n  .legend {\n    fill: ${options.textColor};\n    font-family: ${options.fontFamily};\n  }\n\n  .label text {\n    fill: #333;\n  }\n  .label {\n    color: ${options.textColor}\n  }\n\n  .face {\n    ${options.faceColor ? `fill: ${options.faceColor}` : 'fill: #FFF8DC'};\n    stroke: #999;\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 1.5px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n    }\n    text-align: center;\n  }\n\n  .cluster rect {\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${options.fontFamily};\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .task-type-0, .section-type-0  {\n    ${options.fillType0 ? `fill: ${options.fillType0}` : ''};\n  }\n  .task-type-1, .section-type-1  {\n    ${options.fillType0 ? `fill: ${options.fillType1}` : ''};\n  }\n  .task-type-2, .section-type-2  {\n    ${options.fillType0 ? `fill: ${options.fillType2}` : ''};\n  }\n  .task-type-3, .section-type-3  {\n    ${options.fillType0 ? `fill: ${options.fillType3}` : ''};\n  }\n  .task-type-4, .section-type-4  {\n    ${options.fillType0 ? `fill: ${options.fillType4}` : ''};\n  }\n  .task-type-5, .section-type-5  {\n    ${options.fillType0 ? `fill: ${options.fillType5}` : ''};\n  }\n  .task-type-6, .section-type-6  {\n    ${options.fillType0 ? `fill: ${options.fillType6}` : ''};\n  }\n  .task-type-7, .section-type-7  {\n    ${options.fillType0 ? `fill: ${options.fillType7}` : ''};\n  }\n\n  .actor-0 {\n    ${options.actor0 ? `fill: ${options.actor0}` : ''};\n  }\n  .actor-1 {\n    ${options.actor1 ? `fill: ${options.actor1}` : ''};\n  }\n  .actor-2 {\n    ${options.actor2 ? `fill: ${options.actor2}` : ''};\n  }\n  .actor-3 {\n    ${options.actor3 ? `fill: ${options.actor3}` : ''};\n  }\n  .actor-4 {\n    ${options.actor4 ? `fill: ${options.actor4}` : ''};\n  }\n  .actor-5 {\n    ${options.actor5 ? `fill: ${options.actor5}` : ''};\n  }\n  ${getIconStyles()}\n`;\n\nexport default getStyles;\n", "import { arc as d3arc } from 'd3';\nimport * as svgDrawCommon from '../common/svgDrawCommon.js';\n\nexport const drawRect = function (elem, rectData) {\n  return svgDrawCommon.drawRect(elem, rectData);\n};\n\nexport const drawFace = function (element, faceData) {\n  const radius = 15;\n  const circleElement = element\n    .append('circle')\n    .attr('cx', faceData.cx)\n    .attr('cy', faceData.cy)\n    .attr('class', 'face')\n    .attr('r', radius)\n    .attr('stroke-width', 2)\n    .attr('overflow', 'visible');\n\n  const face = element.append('g');\n\n  //left eye\n  face\n    .append('circle')\n    .attr('cx', faceData.cx - radius / 3)\n    .attr('cy', faceData.cy - radius / 3)\n    .attr('r', 1.5)\n    .attr('stroke-width', 2)\n    .attr('fill', '#666')\n    .attr('stroke', '#666');\n\n  //right eye\n  face\n    .append('circle')\n    .attr('cx', faceData.cx + radius / 3)\n    .attr('cy', faceData.cy - radius / 3)\n    .attr('r', 1.5)\n    .attr('stroke-width', 2)\n    .attr('fill', '#666')\n    .attr('stroke', '#666');\n\n  /** @param {any} face */\n  function smile(face) {\n    const arc = d3arc()\n      .startAngle(Math.PI / 2)\n      .endAngle(3 * (Math.PI / 2))\n      .innerRadius(radius / 2)\n      .outerRadius(radius / 2.2);\n    //mouth\n    face\n      .append('path')\n      .attr('class', 'mouth')\n      .attr('d', arc)\n      .attr('transform', 'translate(' + faceData.cx + ',' + (faceData.cy + 2) + ')');\n  }\n\n  /** @param {any} face */\n  function sad(face) {\n    const arc = d3arc()\n      .startAngle((3 * Math.PI) / 2)\n      .endAngle(5 * (Math.PI / 2))\n      .innerRadius(radius / 2)\n      .outerRadius(radius / 2.2);\n    //mouth\n    face\n      .append('path')\n      .attr('class', 'mouth')\n      .attr('d', arc)\n      .attr('transform', 'translate(' + faceData.cx + ',' + (faceData.cy + 7) + ')');\n  }\n\n  /** @param {any} face */\n  function ambivalent(face) {\n    face\n      .append('line')\n      .attr('class', 'mouth')\n      .attr('stroke', 2)\n      .attr('x1', faceData.cx - 5)\n      .attr('y1', faceData.cy + 7)\n      .attr('x2', faceData.cx + 5)\n      .attr('y2', faceData.cy + 7)\n      .attr('class', 'mouth')\n      .attr('stroke-width', '1px')\n      .attr('stroke', '#666');\n  }\n\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n\n  return circleElement;\n};\n\nexport const drawCircle = function (element, circleData) {\n  const circleElement = element.append('circle');\n  circleElement.attr('cx', circleData.cx);\n  circleElement.attr('cy', circleData.cy);\n  circleElement.attr('class', 'actor-' + circleData.pos);\n  circleElement.attr('fill', circleData.fill);\n  circleElement.attr('stroke', circleData.stroke);\n  circleElement.attr('r', circleData.r);\n\n  if (circleElement.class !== undefined) {\n    circleElement.attr('class', circleElement.class);\n  }\n\n  if (circleData.title !== undefined) {\n    circleElement.append('title').text(circleData.title);\n  }\n\n  return circleElement;\n};\n\nexport const drawText = function (elem, textData) {\n  return svgDrawCommon.drawText(elem, textData);\n};\n\nexport const drawLabel = function (elem, txtObject) {\n  /**\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} cut\n   */\n  function genPoints(x, y, width, height, cut) {\n    return (\n      x +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      (y + height - cut) +\n      ' ' +\n      (x + width - cut * 1.2) +\n      ',' +\n      (y + height) +\n      ' ' +\n      x +\n      ',' +\n      (y + height)\n    );\n  }\n  const polygon = elem.append('polygon');\n  polygon.attr('points', genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr('class', 'labelBox');\n\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText(elem, txtObject);\n};\n\nexport const drawSection = function (elem, section, conf) {\n  const g = elem.append('g');\n\n  const rect = svgDrawCommon.getNoteRect();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  // section width covers all nested tasks\n  rect.width =\n    conf.width * section.taskCount + // width of the tasks\n    conf.diagramMarginX * (section.taskCount - 1); // width of space between tasks\n  rect.height = conf.height;\n  rect.class = 'journey-section section-type-' + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n\n  _drawTextCandidateFunc(conf)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: 'journey-section section-type-' + section.num },\n    conf,\n    section.colour\n  );\n};\n\nlet taskCount = -1;\n/**\n * Draws an actor in the diagram with the attached line\n *\n * @param {any} elem The HTML element\n * @param {any} task The task to render\n * @param {any} conf The global configuration\n */\nexport const drawTask = function (elem, task, conf) {\n  const center = task.x + conf.width / 2;\n  const g = elem.append('g');\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append('line')\n    .attr('id', 'task' + taskCount)\n    .attr('x1', center)\n    .attr('y1', task.y)\n    .attr('x2', center)\n    .attr('y2', maxHeight)\n    .attr('class', 'task-line')\n    .attr('stroke-width', '1px')\n    .attr('stroke-dasharray', '4 2')\n    .attr('stroke', '#666');\n\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score,\n  });\n\n  const rect = svgDrawCommon.getNoteRect();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = 'task task-type-' + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n\n  let xPos = task.x + 14;\n  task.people.forEach((person) => {\n    const colour = task.actors[person].color;\n\n    const circle = {\n      cx: xPos,\n      cy: task.y,\n      r: 7,\n      fill: colour,\n      stroke: '#000',\n      title: person,\n      pos: task.actors[person].position,\n    };\n\n    drawCircle(g, circle);\n    xPos += 10;\n  });\n\n  _drawTextCandidateFunc(conf)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: 'task' },\n    conf,\n    task.colour\n  );\n};\n\n/**\n * Draws a background rectangle\n *\n * @param {any} elem The html element\n * @param {any} bounds The bounds of the drawing\n */\nexport const drawBackgroundRect = function (elem, bounds) {\n  svgDrawCommon.drawBackgroundRect(elem, bounds);\n};\n\nconst _drawTextCandidateFunc = (function () {\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} colour\n   */\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g\n      .append('text')\n      .attr('x', x + width / 2)\n      .attr('y', y + height / 2 + 5)\n      .style('font-color', colour)\n      .style('text-anchor', 'middle')\n      .text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   * @param {any} colour\n   */\n  function byTspan(content, g, x, y, width, height, textAttrs, conf, colour) {\n    const { taskFontSize, taskFontFamily } = conf;\n\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - (taskFontSize * (lines.length - 1)) / 2;\n      const text = g\n        .append('text')\n        .attr('x', x + width / 2)\n        .attr('y', y)\n        .attr('fill', colour)\n        .style('text-anchor', 'middle')\n        .style('font-size', taskFontSize)\n        .style('font-family', taskFontFamily);\n      text\n        .append('tspan')\n        .attr('x', x + width / 2)\n        .attr('dy', dy)\n        .text(lines[i]);\n\n      text\n        .attr('y', y + height / 2.0)\n        .attr('dominant-baseline', 'central')\n        .attr('alignment-baseline', 'central');\n\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const body = g.append('switch');\n    const f = body\n      .append('foreignObject')\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height)\n      .attr('position', 'fixed');\n\n    const text = f\n      .append('xhtml:div')\n      .style('display', 'table')\n      .style('height', '100%')\n      .style('width', '100%');\n\n    text\n      .append('div')\n      .attr('class', 'label')\n      .style('display', 'table-cell')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .text(content);\n\n    byTspan(content, body, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} toText\n   * @param {any} fromTextAttrsDict\n   */\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        // noinspection JSUnfilteredForInLoop\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n\n  return function (conf) {\n    return conf.textPlacement === 'fo' ? byFo : conf.textPlacement === 'old' ? byText : byTspan;\n  };\n})();\n\nconst initGraphics = function (graphics) {\n  graphics\n    .append('defs')\n    .append('marker')\n    .attr('id', 'arrowhead')\n    .attr('refX', 5)\n    .attr('refY', 2)\n    .attr('markerWidth', 6)\n    .attr('markerHeight', 4)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0,0 V 4 L6,2 Z'); // this is actual shape for arrowhead\n};\n\nexport default {\n  drawRect,\n  drawCircle,\n  drawSection,\n  drawText,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect,\n  initGraphics,\n};\n", "// @ts-nocheck TODO: fix file\nimport { select } from 'd3';\nimport svgDraw from './svgDraw.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\nexport const setConf = function (cnf) {\n  const keys = Object.keys(cnf);\n\n  keys.forEach(function (key) {\n    conf[key] = cnf[key];\n  });\n};\n\nconst actors = {};\nlet maxWidth = 0;\n\n/** @param diagram - The diagram to draw to. */\nfunction drawActorLegend(diagram) {\n  const conf = getConfig().journey;\n  const maxLabelWidth = conf.maxLabelWidth;\n  maxWidth = 0;\n  let yPos = 60;\n\n  Object.keys(actors).forEach((person) => {\n    const colour = actors[person].color;\n    const circleData = {\n      cx: 20,\n      cy: yPos,\n      r: 7,\n      fill: colour,\n      stroke: '#000',\n      pos: actors[person].position,\n    };\n    svgDraw.drawCircle(diagram, circleData);\n\n    // First, measure the full text width without wrapping.\n    let measureText = diagram.append('text').attr('visibility', 'hidden').text(person);\n    const fullTextWidth = measureText.node().getBoundingClientRect().width;\n    measureText.remove();\n\n    let lines = [];\n\n    // If the text is naturally within the max width, use it as a single line.\n    if (fullTextWidth <= maxLabelWidth) {\n      lines = [person];\n    } else {\n      // Otherwise, wrap the text using the knuth-plass algorithm.\n      const words = person.split(' '); // Split the text into words.\n      let currentLine = '';\n      measureText = diagram.append('text').attr('visibility', 'hidden');\n\n      words.forEach((word) => {\n        // check the width of the line with the new word.\n        const testLine = currentLine ? `${currentLine} ${word}` : word;\n        measureText.text(testLine);\n        const textWidth = measureText.node().getBoundingClientRect().width;\n\n        if (textWidth > maxLabelWidth) {\n          // If adding the new word exceeds max width, push the current line.\n          if (currentLine) {\n            lines.push(currentLine);\n          }\n          currentLine = word; // Start a new line with the current word.\n\n          // If the word itself is too long, break it with a hyphen.\n          measureText.text(word);\n          if (measureText.node().getBoundingClientRect().width > maxLabelWidth) {\n            let brokenWord = '';\n            for (const char of word) {\n              brokenWord += char;\n              measureText.text(brokenWord + '-');\n              if (measureText.node().getBoundingClientRect().width > maxLabelWidth) {\n                // Push the broken part with a hyphen.\n                lines.push(brokenWord.slice(0, -1) + '-');\n                brokenWord = char;\n              }\n            }\n            currentLine = brokenWord;\n          }\n        } else {\n          // If the line with the new word fits, add the new word to the current line.\n          currentLine = testLine;\n        }\n      });\n\n      // Push the last line.\n      if (currentLine) {\n        lines.push(currentLine);\n      }\n      measureText.remove(); // Remove the text element used for measuring.\n    }\n\n    lines.forEach((line, index) => {\n      const labelData = {\n        x: 40,\n        y: yPos + 7 + index * 20,\n        fill: '#666',\n        text: line,\n        textMargin: conf.boxTextMargin ?? 5,\n      };\n\n      // Draw the text and measure the width.\n      const textElement = svgDraw.drawText(diagram, labelData);\n      const lineWidth = textElement.node().getBoundingClientRect().width;\n\n      // Use conf.leftMargin as the initial spacing baseline,\n      // but expand maxWidth if the line is wider.\n      if (lineWidth > maxWidth && lineWidth > conf.leftMargin - lineWidth) {\n        maxWidth = lineWidth;\n      }\n    });\n\n    yPos += Math.max(20, lines.length * 20);\n  });\n}\n\n// TODO: Cleanup?\nconst conf = getConfig().journey;\nlet leftMargin = 0;\nexport const draw = function (text, id, version, diagObj) {\n  const configObject = getConfig();\n  const titleColor = configObject.journey.titleColor;\n  const titleFontSize = configObject.journey.titleFontSize;\n  const titleFontFamily = configObject.journey.titleFontFamily;\n\n  const securityLevel = configObject.securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n  // const doc = securityLevel === 'sandbox' ? sandboxElement.nodes()[0].contentDocument : document;\n\n  bounds.init();\n  const diagram = root.select('#' + id);\n\n  svgDraw.initGraphics(diagram);\n\n  const tasks = diagObj.db.getTasks();\n  const title = diagObj.db.getDiagramTitle();\n\n  const actorNames = diagObj.db.getActors();\n  for (const member in actors) {\n    delete actors[member];\n  }\n  let actorPos = 0;\n  actorNames.forEach((actorName) => {\n    actors[actorName] = {\n      color: conf.actorColours[actorPos % conf.actorColours.length],\n      position: actorPos,\n    };\n    actorPos++;\n  });\n\n  drawActorLegend(diagram);\n  leftMargin = conf.leftMargin + maxWidth;\n  bounds.insert(0, 0, leftMargin, Object.keys(actors).length * 50);\n  drawTasks(diagram, tasks, 0);\n\n  const box = bounds.getBounds();\n  if (title) {\n    diagram\n      .append('text')\n      .text(title)\n      .attr('x', leftMargin)\n      .attr('font-size', titleFontSize)\n      .attr('font-weight', 'bold')\n      .attr('y', 25)\n      .attr('fill', titleColor)\n      .attr('font-family', titleFontFamily);\n  }\n\n  const height = box.stopy - box.starty + 2 * conf.diagramMarginY;\n  const width = leftMargin + box.stopx + 2 * conf.diagramMarginX;\n\n  configureSvgSize(diagram, height, width, conf.useMaxWidth);\n\n  // Draw activity line\n  diagram\n    .append('line')\n    .attr('x1', leftMargin)\n    .attr('y1', conf.height * 4) // One section head + one task + margins\n    .attr('x2', width - leftMargin - 4) // Subtract stroke width so arrow point is retained\n    .attr('y2', conf.height * 4)\n    .attr('stroke-width', 4)\n    .attr('stroke', 'black')\n    .attr('marker-end', 'url(#arrowhead)');\n\n  const extraVertForTitle = title ? 70 : 0;\n  diagram.attr('viewBox', `${box.startx} -25 ${width} ${height + extraVertForTitle}`);\n  diagram.attr('preserveAspectRatio', 'xMinYMin meet');\n  diagram.attr('height', height + extraVertForTitle + 25);\n};\n\nexport const bounds = {\n  data: {\n    startx: undefined,\n    stopx: undefined,\n    starty: undefined,\n    stopy: undefined,\n  },\n  verticalPos: 0,\n\n  sequenceItems: [],\n  init: function () {\n    this.sequenceItems = [];\n    this.data = {\n      startx: undefined,\n      stopx: undefined,\n      starty: undefined,\n      stopy: undefined,\n    };\n    this.verticalPos = 0;\n  },\n  updateVal: function (obj, key, val, fun) {\n    if (obj[key] === undefined) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  },\n  updateBounds: function (startx, starty, stopx, stopy) {\n    const conf = getConfig().journey;\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const _self = this;\n    let cnt = 0;\n    /** @param type - Set to `activation` if activation */\n    function updateFn(type?: 'activation') {\n      return function updateItemBounds(item) {\n        cnt++;\n        // The loop sequenceItems is a stack so the biggest margins in the beginning of the sequenceItems\n        const n = _self.sequenceItems.length - cnt + 1;\n        _self.updateVal(item, 'starty', starty - n * conf.boxMargin, Math.min);\n        _self.updateVal(item, 'stopy', stopy + n * conf.boxMargin, Math.max);\n\n        _self.updateVal(bounds.data, 'startx', startx - n * conf.boxMargin, Math.min);\n        _self.updateVal(bounds.data, 'stopx', stopx + n * conf.boxMargin, Math.max);\n\n        if (!(type === 'activation')) {\n          _self.updateVal(item, 'startx', startx - n * conf.boxMargin, Math.min);\n          _self.updateVal(item, 'stopx', stopx + n * conf.boxMargin, Math.max);\n\n          _self.updateVal(bounds.data, 'starty', starty - n * conf.boxMargin, Math.min);\n          _self.updateVal(bounds.data, 'stopy', stopy + n * conf.boxMargin, Math.max);\n        }\n      };\n    }\n\n    this.sequenceItems.forEach(updateFn());\n  },\n  insert: function (startx, starty, stopx, stopy) {\n    const _startx = Math.min(startx, stopx);\n    const _stopx = Math.max(startx, stopx);\n    const _starty = Math.min(starty, stopy);\n    const _stopy = Math.max(starty, stopy);\n\n    this.updateVal(bounds.data, 'startx', _startx, Math.min);\n    this.updateVal(bounds.data, 'starty', _starty, Math.min);\n    this.updateVal(bounds.data, 'stopx', _stopx, Math.max);\n    this.updateVal(bounds.data, 'stopy', _stopy, Math.max);\n\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  },\n  bumpVerticalPos: function (bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = this.verticalPos;\n  },\n  getVerticalPos: function () {\n    return this.verticalPos;\n  },\n  getBounds: function () {\n    return this.data;\n  },\n};\n\nconst fills = conf.sectionFills;\nconst textColours = conf.sectionColours;\n\nexport const drawTasks = function (diagram, tasks, verticalPos) {\n  const conf = getConfig().journey;\n  let lastSection = '';\n  const sectionVHeight = conf.height * 2 + conf.diagramMarginY;\n  const taskPos = verticalPos + sectionVHeight;\n\n  let sectionNumber = 0;\n  let fill = '#CCC';\n  let colour = 'black';\n  let num = 0;\n\n  // Draw the tasks\n  for (const [i, task] of tasks.entries()) {\n    if (lastSection !== task.section) {\n      fill = fills[sectionNumber % fills.length];\n      num = sectionNumber % fills.length;\n      colour = textColours[sectionNumber % textColours.length];\n\n      // count how many consecutive tasks have the same section\n      let taskInSectionCount = 0;\n      const currentSection = task.section;\n      for (let taskIndex = i; taskIndex < tasks.length; taskIndex++) {\n        if (tasks[taskIndex].section == currentSection) {\n          taskInSectionCount = taskInSectionCount + 1;\n        } else {\n          break;\n        }\n      }\n\n      const section = {\n        x: i * conf.taskMargin + i * conf.width + leftMargin,\n        y: 50,\n        text: task.section,\n        fill,\n        num,\n        colour,\n        taskCount: taskInSectionCount,\n      };\n\n      svgDraw.drawSection(diagram, section, conf);\n      lastSection = task.section;\n      sectionNumber++;\n    }\n\n    // Collect the actors involved in the task\n    const taskActors = task.people.reduce((acc, actorName) => {\n      if (actors[actorName]) {\n        acc[actorName] = actors[actorName];\n      }\n\n      return acc;\n    }, {});\n\n    // Add some rendering data to the object\n    task.x = i * conf.taskMargin + i * conf.width + leftMargin;\n    task.y = taskPos;\n    task.width = conf.diagramMarginX;\n    task.height = conf.diagramMarginY;\n    task.colour = colour;\n    task.fill = fill;\n    task.num = num;\n    task.actors = taskActors;\n\n    // Draw the box with the attached line\n    svgDraw.drawTask(diagram, task, conf);\n    bounds.insert(task.x, task.y, task.x + task.width + conf.taskMargin, 300 + 5 * 30); // stopY is the length of the descenders.\n  }\n};\n\nexport default {\n  setConf,\n  draw,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: JISON doesn't support types\nimport parser from './parser/journey.jison';\nimport db from './journeyDb.js';\nimport styles from './styles.js';\nimport renderer from './journeyRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n  init: (cnf) => {\n    renderer.setConf(cnf.journey);\n    db.clear();\n  },\n};\n"], "mappings": "0SAyEA,IAAIA,EAAU,UAAU,CACxB,IAAIC,EAAEC,EAAA,SAASC,EAAEC,EAAEH,EAAEI,EAAE,CAAC,IAAIJ,EAAEA,GAAG,CAAC,EAAEI,EAAEF,EAAE,OAAOE,IAAIJ,EAAEE,EAAEE,CAAC,CAAC,EAAED,EAAE,CAAC,OAAOH,CAAC,EAAhE,KAAkEK,EAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAClKZ,EAAS,CAAC,MAAOE,EAAA,UAAkB,CAAE,EAApB,SACrB,GAAI,CAAC,EACL,SAAU,CAAC,MAAQ,EAAE,MAAQ,EAAE,QAAU,EAAE,SAAW,EAAE,IAAM,EAAE,KAAO,EAAE,MAAQ,EAAE,UAAY,EAAE,QAAU,GAAG,MAAQ,GAAG,UAAY,GAAG,gBAAkB,GAAG,UAAY,GAAG,gBAAkB,GAAG,0BAA4B,GAAG,QAAU,GAAG,SAAW,GAAG,SAAW,GAAG,QAAU,EAAE,KAAO,CAAC,EAC7R,WAAY,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,GAAG,UAAU,GAAG,QAAQ,GAAG,YAAY,GAAG,kBAAkB,GAAG,YAAY,GAAG,kBAAkB,GAAG,4BAA4B,GAAG,UAAU,GAAG,WAAW,GAAG,UAAU,EAC5N,aAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAC9F,cAAeA,EAAA,SAAmBW,EAAQC,EAAQC,EAAUC,EAAIC,EAAyBC,EAAiBC,EAAiB,CAG3H,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACjB,IAAK,GACJ,OAAOC,EAAGE,EAAG,CAAC,EAEf,IAAK,GACJ,KAAK,EAAI,CAAC,EACX,MACA,IAAK,GACLF,EAAGE,EAAG,CAAC,EAAE,KAAKF,EAAGE,CAAE,CAAC,EAAE,KAAK,EAAIF,EAAGE,EAAG,CAAC,EACtC,MACA,IAAK,GAAG,IAAK,GACZ,KAAK,EAAIF,EAAGE,CAAE,EACf,MACA,IAAK,GAAG,IAAK,GACZ,KAAK,EAAE,CAAC,EACT,MACA,IAAK,GACLJ,EAAG,gBAAgBE,EAAGE,CAAE,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAEF,EAAGE,CAAE,EAAE,OAAO,CAAC,EAC3D,MACA,IAAK,GACJ,KAAK,EAAEF,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,YAAY,KAAK,CAAC,EAC3C,MACA,IAAK,IAAI,IAAK,IACb,KAAK,EAAEE,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,kBAAkB,KAAK,CAAC,EACjD,MACA,IAAK,IACLA,EAAG,WAAWE,EAAGE,CAAE,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAEF,EAAGE,CAAE,EAAE,OAAO,CAAC,EACtD,MACA,IAAK,IACLJ,EAAG,QAAQE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAAE,KAAK,EAAE,OACpC,KACA,CACA,EApCe,aAqCf,MAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEnB,EAAEK,EAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAEX,EAAEK,EAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAEX,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAC/W,eAAgB,CAAC,EACjB,WAAYJ,EAAA,SAAqBmB,EAAKC,EAAM,CACxC,GAAIA,EAAK,YACL,KAAK,MAAMD,CAAG,MACX,CACH,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACV,CACJ,EARY,cASZ,MAAOrB,EAAA,SAAesB,EAAO,CACzB,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,EAAQ,KAAK,MAAOjB,EAAS,GAAIE,EAAW,EAAGD,GAAS,EAAGiB,GAAa,EAAGC,GAAS,EAAGC,GAAM,EAClKC,GAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAQ,OAAO,OAAO,KAAK,KAAK,EAChCC,EAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAASjC,KAAK,KAAK,GACX,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,CAAC,IAC/CiC,EAAY,GAAGjC,CAAC,EAAI,KAAK,GAAGA,CAAC,GAGrCgC,EAAM,SAASX,EAAOY,EAAY,EAAE,EACpCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAM,OAAU,MACvBA,EAAM,OAAS,CAAC,GAEpB,IAAIE,EAAQF,EAAM,OAClBN,EAAO,KAAKQ,CAAK,EACjB,IAAIC,GAASH,EAAM,SAAWA,EAAM,QAAQ,OACxC,OAAOC,EAAY,GAAG,YAAe,WACrC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAElD,SAASG,GAASC,EAAG,CACjBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CACpC,CAJStC,EAAAqC,GAAA,YAKD,SAASE,IAAM,CACf,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAM,IAAI,GAAKF,GACnC,OAAOS,GAAU,WACbA,aAAiB,QACjBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAEvBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE7BA,CACX,CAXaxC,EAAAuC,GAAA,OAajB,QADIE,EAAQC,EAAgBC,EAAOC,EAAQC,GAAGC,EAAGC,EAAQ,CAAC,EAAGC,EAAGC,EAAKC,GAAUC,IAClE,CAUT,GATAR,EAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,CAAK,EACzBC,EAAS,KAAK,eAAeD,CAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACpCA,EAASF,GAAI,GAEjBK,EAAShB,EAAMe,CAAK,GAAKf,EAAMe,CAAK,EAAEF,CAAM,GAE5C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CAC/D,IAAIQ,EAAS,GACbD,EAAW,CAAC,EACZ,IAAKH,KAAKpB,EAAMe,CAAK,EACb,KAAK,WAAWK,CAAC,GAAKA,EAAIlB,IAC1BqB,EAAS,KAAK,IAAO,KAAK,WAAWH,CAAC,EAAI,GAAI,EAGlDf,EAAM,aACNmB,EAAS,wBAA0BvC,EAAW,GAAK;AAAA,EAAQoB,EAAM,aAAa,EAAI;AAAA,YAAiBkB,EAAS,KAAK,IAAI,EAAI,WAAc,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,EAAS,wBAA0BvC,EAAW,GAAK,iBAAmB4B,GAAUV,GAAM,eAAiB,KAAQ,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAExJ,KAAK,WAAWW,EAAQ,CACpB,KAAMnB,EAAM,MACZ,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAM,SACZ,IAAKE,EACL,SAAUgB,CACd,CAAC,CACL,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAC9C,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcF,CAAM,EAEtG,OAAQG,EAAO,CAAC,EAAG,CACnB,IAAK,GACDpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAM,MAAM,EACxBN,EAAO,KAAKM,EAAM,MAAM,EACxBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,GASDD,EAASC,EACTA,EAAiB,OATjB9B,GAASqB,EAAM,OACftB,EAASsB,EAAM,OACfpB,EAAWoB,EAAM,SACjBE,EAAQF,EAAM,OACVJ,GAAa,GACbA,MAMR,MACJ,IAAK,GAwBD,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,EAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,EAAM,GAAK,CACP,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WAC3C,EACIS,KACAW,EAAM,GAAG,MAAQ,CACbpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACrC,GAEJmB,EAAI,KAAK,cAAc,MAAMC,EAAO,CAChCpC,EACAC,GACAC,EACAqB,EAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACJ,EAAE,OAAOK,EAAI,CAAC,EACV,OAAOc,EAAM,IACb,OAAOA,EAEPG,IACAzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAErCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,EAAM,CAAC,EACnBpB,EAAO,KAAKoB,EAAM,EAAE,EACpBG,GAAWtB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACJ,IAAK,GACD,MAAO,EACX,CACJ,CACA,MAAO,EACX,EA3IO,QA2IN,EAGGjB,EAAS,UAAU,CACvB,IAAIA,EAAS,CAEb,IAAI,EAEJ,WAAWjC,EAAA,SAAoBmB,EAAKC,EAAM,CAClC,GAAI,KAAK,GAAG,OACR,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAE3B,EANO,cASX,SAASnB,EAAA,SAAUsB,EAAOR,EAAI,CACtB,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASQ,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACV,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACjB,EACI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,EAAE,CAAC,GAE5B,KAAK,OAAS,EACP,IACX,EAlBK,YAqBT,MAAMtB,EAAA,UAAY,CACV,IAAIqD,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACA,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEZ,KAAK,QAAQ,QACb,KAAK,OAAO,MAAM,CAAC,IAGvB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACX,EApBE,SAuBN,MAAMrD,EAAA,SAAUqD,EAAI,CACZ,IAAIJ,EAAMI,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EAEpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASJ,CAAG,EAE5D,KAAK,QAAUA,EACf,IAAIM,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EAEzDD,EAAM,OAAS,IACf,KAAK,UAAYA,EAAM,OAAS,GAEpC,IAAIR,EAAI,KAAK,OAAO,MAEpB,YAAK,OAAS,CACV,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaQ,GACRA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAC5DA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAChE,KAAK,OAAO,aAAeL,CACjC,EAEI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAEvD,KAAK,OAAS,KAAK,OAAO,OACnB,IACX,EAhCE,SAmCN,KAAKjD,EAAA,UAAY,CACT,YAAK,MAAQ,GACN,IACX,EAHC,QAML,OAAOA,EAAA,UAAY,CACX,GAAI,KAAK,QAAQ,gBACb,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAC9N,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,EAGL,OAAO,IACX,EAZG,UAeP,KAAKA,EAAA,SAAU,EAAG,CACV,KAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAClC,EAFC,QAKL,UAAUA,EAAA,UAAY,CACd,IAAIwD,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAM,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAHM,aAMV,cAAcxD,EAAA,UAAY,CAClB,IAAIyD,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KACdA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAGA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAE,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAClF,EANU,iBASd,aAAazD,EAAA,UAAY,CACjB,IAAI0D,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACnD,EAJS,gBAOb,WAAW3D,EAAA,SAAS4D,EAAOC,EAAc,CACjC,IAAIrB,EACAc,EACAQ,EAwDJ,GAtDI,KAAK,QAAQ,kBAEbA,EAAS,CACL,SAAU,KAAK,SACf,OAAQ,CACJ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC7B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACf,EACI,KAAK,QAAQ,SACbA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAIvDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACA,KAAK,UAAYA,EAAM,QAE3B,KAAK,OAAS,CACV,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EACAA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAC5E,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACpD,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAEhE,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBpB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMqB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SAClB,KAAK,KAAO,IAEZrB,EACA,OAAOA,EACJ,GAAI,KAAK,WAAY,CAExB,QAASvC,KAAK6D,EACV,KAAK7D,CAAC,EAAI6D,EAAO7D,CAAC,EAEtB,MAAO,EACX,CACA,MAAO,EACX,EArEO,cAwEX,KAAKD,EAAA,UAAY,CACT,GAAI,KAAK,KACL,OAAO,KAAK,IAEX,KAAK,SACN,KAAK,KAAO,IAGhB,IAAIwC,EACAoB,EACAG,EACAC,EACC,KAAK,QACN,KAAK,OAAS,GACd,KAAK,MAAQ,IAGjB,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAE9B,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGvD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAE9B,GADA1B,EAAQ,KAAK,WAAWuB,EAAWE,EAAMC,CAAC,CAAC,EACvC1B,IAAU,GACV,OAAOA,EACJ,GAAI,KAAK,WAAY,CACxBoB,EAAQ,GACR,QACJ,KAEI,OAAO,EAEf,SAAW,CAAC,KAAK,QAAQ,KACrB,MAIZ,OAAIA,GACApB,EAAQ,KAAK,WAAWoB,EAAOK,EAAMD,CAAK,CAAC,EACvCxB,IAAU,GACHA,EAGJ,IAEP,KAAK,SAAW,GACT,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACpH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,CAET,EAvDC,QA0DL,IAAIxC,EAAA,UAAgB,CACZ,IAAI8C,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGO,KAAK,IAAI,CAExB,EAPA,OAUJ,MAAM9C,EAAA,SAAgBmE,EAAW,CACzB,KAAK,eAAe,KAAKA,CAAS,CACtC,EAFE,SAKN,SAASnE,EAAA,UAAqB,CACtB,IAAIsC,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACG,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEpC,EAPK,YAUT,cAActC,EAAA,UAA0B,CAChC,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EACzE,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAE1C,EANU,iBASd,SAASA,EAAA,SAAmBsC,EAAG,CAEvB,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACE,KAAK,eAAeA,CAAC,EAErB,SAEf,EAPK,YAUT,UAAUtC,EAAA,SAAoBmE,EAAW,CACjC,KAAK,MAAMA,CAAS,CACxB,EAFM,aAKV,eAAenE,EAAA,UAA0B,CACjC,OAAO,KAAK,eAAe,MAC/B,EAFW,kBAGf,QAAS,CAAC,mBAAmB,EAAI,EACjC,cAAeA,EAAA,SAAmBc,EAAGsD,EAAIC,EAA0BC,EAAU,CAC7E,IAAIC,EAAQD,EACZ,OAAOD,EAA2B,CAClC,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,GAAE,MAAO,IAEd,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,GAAE,MAAO,GAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GAAG,YAAK,MAAM,WAAW,EAAS,GACvC,MACA,IAAK,GAAG,YAAK,SAAS,EAAU,kBAChC,MACA,IAAK,GAAG,YAAK,MAAM,WAAW,EAAS,GACvC,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,kBACjC,MACA,IAAK,IAAI,KAAK,MAAM,qBAAqB,EACzC,MACA,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAG,MAAO,4BAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,GAEf,IAAK,IAAG,MAAO,SAEf,CACA,EA5Ce,aA6Cf,MAAO,CAAC,sBAAsB,sBAAsB,cAAc,YAAY,gBAAgB,kBAAkB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,yBAAyB,aAAa,eAAe,2BAA2B,kBAAkB,kBAAkB,UAAU,UAAU,SAAS,EACzX,WAAY,CAAC,oBAAsB,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,CAAC,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,CAAC,CAC7O,EACA,OAAOpC,CACP,EAAG,EACHnC,EAAO,MAAQmC,EACf,SAASuC,GAAU,CACjB,KAAK,GAAK,CAAC,CACb,CAFS,OAAAxE,EAAAwE,EAAA,UAGTA,EAAO,UAAY1E,EAAOA,EAAO,OAAS0E,EACnC,IAAIA,CACX,EAAG,EACF1E,EAAO,OAASA,EAEhB,IAAO2E,GAAQC,ECloBhB,IAAIC,EAAiB,GAEfC,EAAW,CAAC,EACZC,EAAQ,CAAC,EACTC,EAAW,CAAC,EAELC,GAAQC,EAAA,UAAY,CAC/BJ,EAAS,OAAS,EAClBC,EAAM,OAAS,EACfF,EAAiB,GACjBG,EAAS,OAAS,EAClBC,GAAY,CACd,EANqB,SAQRE,GAAaD,EAAA,SAAUE,EAAK,CACvCP,EAAiBO,EACjBN,EAAS,KAAKM,CAAG,CACnB,EAH0B,cAKbC,GAAcH,EAAA,UAAY,CACrC,OAAOJ,CACT,EAF2B,eAIdQ,GAAWJ,EAAA,UAAY,CAClC,IAAIK,EAAoBC,GAAa,EAC/BC,EAAW,IACbC,EAAiB,EACrB,KAAO,CAACH,GAAqBG,EAAiBD,GAC5CF,EAAoBC,GAAa,EACjCE,IAGF,OAAAX,EAAM,KAAK,GAAGC,CAAQ,EAEfD,CACT,EAZwB,YAclBY,GAAeT,EAAA,UAAY,CAC/B,IAAMU,EAAa,CAAC,EACpB,OAAAb,EAAM,QAASc,GAAS,CAClBA,EAAK,QACPD,EAAW,KAAK,GAAGC,EAAK,MAAM,CAElC,CAAC,EAGM,CAAC,GADO,IAAI,IAAID,CAAU,CAChB,EAAE,KAAK,CAC1B,EAVqB,gBAYRE,GAAUZ,EAAA,SAAUa,EAAOC,EAAU,CAChD,IAAMC,EAASD,EAAS,OAAO,CAAC,EAAE,MAAM,GAAG,EAEvCE,EAAQ,EACRC,EAAQ,CAAC,EACTF,EAAO,SAAW,GACpBC,EAAQ,OAAOD,EAAO,CAAC,CAAC,EACxBE,EAAQ,CAAC,IAETD,EAAQ,OAAOD,EAAO,CAAC,CAAC,EACxBE,EAAQF,EAAO,CAAC,EAAE,MAAM,GAAG,GAE7B,IAAMG,EAAaD,EAAM,IAAKE,GAAMA,EAAE,KAAK,CAAC,EAEtCC,EAAU,CACd,QAASzB,EACT,KAAMA,EACN,OAAQuB,EACR,KAAML,EACN,MAAAG,CACF,EAEAlB,EAAS,KAAKsB,CAAO,CACvB,EAvBuB,WAyBVC,GAAarB,EAAA,SAAUa,EAAO,CACzC,IAAMS,EAAU,CACd,QAAS3B,EACT,KAAMA,EACN,YAAakB,EACb,KAAMA,EACN,QAAS,CAAC,CACZ,EACAhB,EAAM,KAAKyB,CAAO,CACpB,EAT0B,cAWpBhB,GAAeN,EAAA,UAAY,CAC/B,IAAMuB,EAAcvB,EAAA,SAAUwB,EAAK,CACjC,OAAO1B,EAAS0B,CAAG,EAAE,SACvB,EAFoB,eAIhBC,EAAe,GACnB,OAAW,CAACC,EAAGN,CAAO,IAAKtB,EAAS,QAAQ,EAC1CyB,EAAYG,CAAC,EAEbD,EAAeA,GAAgBL,EAAQ,UAEzC,OAAOK,CACT,EAZqB,gBAcfE,GAAY3B,EAAA,UAAY,CAC5B,OAAOS,GAAa,CACtB,EAFkB,aAIXmB,EAAQ,CACb,UAAW5B,EAAA,IAAM6B,EAAU,EAAE,QAAlB,aACX,MAAA9B,GACA,gBAAA+B,GACA,gBAAAC,GACA,YAAAC,GACA,YAAAC,GACA,kBAAAC,GACA,kBAAAC,GACA,WAAAlC,GACA,YAAAE,GACA,SAAAC,GACA,QAAAQ,GACA,WAAAS,GACA,UAAAM,EACF,EC/HA,IAAMS,GAAYC,EAACC,GACjB;AAAA,mBACiBA,EAAQ,UAAU;AAAA,aACxBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOhBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,YAInBA,EAAQ,SAAS;AAAA,mBACVA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAOxBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,MAIxBA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAS5DA,EAAQ,OAAO;AAAA,cACbA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAYpBA,EAAQ,cAAc;AAAA;AAAA;AAAA;AAAA,cAIpBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,cAKjBA,EAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,wBAKPA,EAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAWvCA,EAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAQXA,EAAQ,UAAU;AAAA;AAAA,kBAEnBA,EAAQ,aAAa;AAAA,wBACfA,EAAQ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOjCA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA,MAGrDA,EAAQ,UAAY,SAASA,EAAQ,SAAS,GAAK,EAAE;AAAA;AAAA;AAAA;AAAA,MAIrDA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA;AAAA,MAG/CA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA;AAAA,MAG/CA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA;AAAA,MAG/CA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA;AAAA,MAG/CA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA;AAAA,MAG/CA,EAAQ,OAAS,SAASA,EAAQ,MAAM,GAAK,EAAE;AAAA;AAAA,IAEjDC,GAAc,CAAC;AAAA,EArID,aAwIXC,GAAQJ,GCvIR,IAAMK,EAAWC,EAAA,SAAUC,EAAMC,EAAU,CAChD,OAAqBH,GAASE,EAAMC,CAAQ,CAC9C,EAFwB,YAIXC,GAAWH,EAAA,SAAUI,EAASC,EAAU,CAEnD,IAAMC,EAAgBF,EACnB,OAAO,QAAQ,EACf,KAAK,KAAMC,EAAS,EAAE,EACtB,KAAK,KAAMA,EAAS,EAAE,EACtB,KAAK,QAAS,MAAM,EACpB,KAAK,IAAK,EAAM,EAChB,KAAK,eAAgB,CAAC,EACtB,KAAK,WAAY,SAAS,EAEvBE,EAAOH,EAAQ,OAAO,GAAG,EAG/BG,EACG,OAAO,QAAQ,EACf,KAAK,KAAMF,EAAS,GAAK,GAAS,CAAC,EACnC,KAAK,KAAMA,EAAS,GAAK,GAAS,CAAC,EACnC,KAAK,IAAK,GAAG,EACb,KAAK,eAAgB,CAAC,EACtB,KAAK,OAAQ,MAAM,EACnB,KAAK,SAAU,MAAM,EAGxBE,EACG,OAAO,QAAQ,EACf,KAAK,KAAMF,EAAS,GAAK,GAAS,CAAC,EACnC,KAAK,KAAMA,EAAS,GAAK,GAAS,CAAC,EACnC,KAAK,IAAK,GAAG,EACb,KAAK,eAAgB,CAAC,EACtB,KAAK,OAAQ,MAAM,EACnB,KAAK,SAAU,MAAM,EAGxB,SAASG,EAAMD,EAAM,CACnB,IAAME,EAAMC,EAAM,EACf,WAAW,KAAK,GAAK,CAAC,EACtB,SAAS,GAAK,KAAK,GAAK,EAAE,EAC1B,YAAY,GAAU,EACtB,YAAY,kBAAY,EAE3BH,EACG,OAAO,MAAM,EACb,KAAK,QAAS,OAAO,EACrB,KAAK,IAAKE,CAAG,EACb,KAAK,YAAa,aAAeJ,EAAS,GAAK,KAAOA,EAAS,GAAK,GAAK,GAAG,CACjF,CAZSL,EAAAQ,EAAA,SAeT,SAASG,EAAIJ,EAAM,CACjB,IAAME,EAAMC,EAAM,EACf,WAAY,EAAI,KAAK,GAAM,CAAC,EAC5B,SAAS,GAAK,KAAK,GAAK,EAAE,EAC1B,YAAY,GAAU,EACtB,YAAY,kBAAY,EAE3BH,EACG,OAAO,MAAM,EACb,KAAK,QAAS,OAAO,EACrB,KAAK,IAAKE,CAAG,EACb,KAAK,YAAa,aAAeJ,EAAS,GAAK,KAAOA,EAAS,GAAK,GAAK,GAAG,CACjF,CAZSL,EAAAW,EAAA,OAeT,SAASC,EAAWL,EAAM,CACxBA,EACG,OAAO,MAAM,EACb,KAAK,QAAS,OAAO,EACrB,KAAK,SAAU,CAAC,EAChB,KAAK,KAAMF,EAAS,GAAK,CAAC,EAC1B,KAAK,KAAMA,EAAS,GAAK,CAAC,EAC1B,KAAK,KAAMA,EAAS,GAAK,CAAC,EAC1B,KAAK,KAAMA,EAAS,GAAK,CAAC,EAC1B,KAAK,QAAS,OAAO,EACrB,KAAK,eAAgB,KAAK,EAC1B,KAAK,SAAU,MAAM,CAC1B,CAZS,OAAAL,EAAAY,EAAA,cAcLP,EAAS,MAAQ,EACnBG,EAAMD,CAAI,EACDF,EAAS,MAAQ,EAC1BM,EAAIJ,CAAI,EAERK,EAAWL,CAAI,EAGVD,CACT,EAvFwB,YAyFXO,GAAab,EAAA,SAAUI,EAASU,EAAY,CACvD,IAAMR,EAAgBF,EAAQ,OAAO,QAAQ,EAC7C,OAAAE,EAAc,KAAK,KAAMQ,EAAW,EAAE,EACtCR,EAAc,KAAK,KAAMQ,EAAW,EAAE,EACtCR,EAAc,KAAK,QAAS,SAAWQ,EAAW,GAAG,EACrDR,EAAc,KAAK,OAAQQ,EAAW,IAAI,EAC1CR,EAAc,KAAK,SAAUQ,EAAW,MAAM,EAC9CR,EAAc,KAAK,IAAKQ,EAAW,CAAC,EAEhCR,EAAc,QAAU,QAC1BA,EAAc,KAAK,QAASA,EAAc,KAAK,EAG7CQ,EAAW,QAAU,QACvBR,EAAc,OAAO,OAAO,EAAE,KAAKQ,EAAW,KAAK,EAG9CR,CACT,EAlB0B,cAoBbS,GAAWf,EAAA,SAAUC,EAAMe,EAAU,CAChD,OAAqBD,GAASd,EAAMe,CAAQ,CAC9C,EAFwB,YAIXC,GAAYjB,EAAA,SAAUC,EAAMiB,EAAW,CAQlD,SAASC,EAAUC,EAAGC,EAAGC,EAAOC,EAAQC,EAAK,CAC3C,OACEJ,EACA,IACAC,EACA,KACCD,EAAIE,GACL,IACAD,EACA,KACCD,EAAIE,GACL,KACCD,EAAIE,EAASC,GACd,KACCJ,EAAIE,EAAQE,EAAM,KACnB,KACCH,EAAIE,GACL,IACAH,EACA,KACCC,EAAIE,EAET,CAtBSvB,EAAAmB,EAAA,aAuBT,IAAMM,EAAUxB,EAAK,OAAO,SAAS,EACrCwB,EAAQ,KAAK,SAAUN,EAAUD,EAAU,EAAGA,EAAU,EAAG,GAAI,GAAI,CAAC,CAAC,EACrEO,EAAQ,KAAK,QAAS,UAAU,EAEhCP,EAAU,EAAIA,EAAU,EAAIA,EAAU,YACtCA,EAAU,EAAIA,EAAU,EAAI,GAAMA,EAAU,YAC5CH,GAASd,EAAMiB,CAAS,CAC1B,EAtCyB,aAwCZQ,GAAc1B,EAAA,SAAUC,EAAM0B,EAASC,EAAM,CACxD,IAAMC,EAAI5B,EAAK,OAAO,GAAG,EAEnB6B,EAAqBC,EAAY,EACvCD,EAAK,EAAIH,EAAQ,EACjBG,EAAK,EAAIH,EAAQ,EACjBG,EAAK,KAAOH,EAAQ,KAEpBG,EAAK,MACHF,EAAK,MAAQD,EAAQ,UACrBC,EAAK,gBAAkBD,EAAQ,UAAY,GAC7CG,EAAK,OAASF,EAAK,OACnBE,EAAK,MAAQ,gCAAkCH,EAAQ,IACvDG,EAAK,GAAK,EACVA,EAAK,GAAK,EACV/B,EAAS8B,EAAGC,CAAI,EAEhBE,GAAuBJ,CAAI,EACzBD,EAAQ,KACRE,EACAC,EAAK,EACLA,EAAK,EACLA,EAAK,MACLA,EAAK,OACL,CAAE,MAAO,gCAAkCH,EAAQ,GAAI,EACvDC,EACAD,EAAQ,MACV,CACF,EA5B2B,eA8BvBM,GAAY,GAQHC,GAAWlC,EAAA,SAAUC,EAAMkC,EAAMP,EAAM,CAClD,IAAMQ,EAASD,EAAK,EAAIP,EAAK,MAAQ,EAC/BC,EAAI5B,EAAK,OAAO,GAAG,EACzBgC,KACA,IAAMI,EAAY,IAAM,EAAI,GAC5BR,EAAE,OAAO,MAAM,EACZ,KAAK,KAAM,OAASI,EAAS,EAC7B,KAAK,KAAMG,CAAM,EACjB,KAAK,KAAMD,EAAK,CAAC,EACjB,KAAK,KAAMC,CAAM,EACjB,KAAK,KAAMC,CAAS,EACpB,KAAK,QAAS,WAAW,EACzB,KAAK,eAAgB,KAAK,EAC1B,KAAK,mBAAoB,KAAK,EAC9B,KAAK,SAAU,MAAM,EAExBlC,GAAS0B,EAAG,CACV,GAAIO,EACJ,GAAI,KAAO,EAAID,EAAK,OAAS,GAC7B,MAAOA,EAAK,KACd,CAAC,EAED,IAAML,EAAqBC,EAAY,EACvCD,EAAK,EAAIK,EAAK,EACdL,EAAK,EAAIK,EAAK,EACdL,EAAK,KAAOK,EAAK,KACjBL,EAAK,MAAQF,EAAK,MAClBE,EAAK,OAASF,EAAK,OACnBE,EAAK,MAAQ,kBAAoBK,EAAK,IACtCL,EAAK,GAAK,EACVA,EAAK,GAAK,EACV/B,EAAS8B,EAAGC,CAAI,EAEhB,IAAIQ,EAAOH,EAAK,EAAI,GACpBA,EAAK,OAAO,QAASI,GAAW,CAC9B,IAAMC,EAASL,EAAK,OAAOI,CAAM,EAAE,MAE7BE,EAAS,CACb,GAAIH,EACJ,GAAIH,EAAK,EACT,EAAG,EACH,KAAMK,EACN,OAAQ,OACR,MAAOD,EACP,IAAKJ,EAAK,OAAOI,CAAM,EAAE,QAC3B,EAEA1B,GAAWgB,EAAGY,CAAM,EACpBH,GAAQ,EACV,CAAC,EAEDN,GAAuBJ,CAAI,EACzBO,EAAK,KACLN,EACAC,EAAK,EACLA,EAAK,EACLA,EAAK,MACLA,EAAK,OACL,CAAE,MAAO,MAAO,EAChBF,EACAO,EAAK,MACP,CACF,EA9DwB,YAsEXO,GAAqB1C,EAAA,SAAUC,EAAM0C,EAAQ,CAC1CD,GAAmBzC,EAAM0C,CAAM,CAC/C,EAFkC,sBAI5BX,GAA0B,UAAY,CAW1C,SAASY,EAAOC,EAAShB,EAAGT,EAAGC,EAAGC,EAAOC,EAAQuB,EAAWN,EAAQ,CAClE,IAAMO,EAAOlB,EACV,OAAO,MAAM,EACb,KAAK,IAAKT,EAAIE,EAAQ,CAAC,EACvB,KAAK,IAAKD,EAAIE,EAAS,EAAI,CAAC,EAC5B,MAAM,aAAciB,CAAM,EAC1B,MAAM,cAAe,QAAQ,EAC7B,KAAKK,CAAO,EACfG,EAAcD,EAAMD,CAAS,CAC/B,CATS9C,EAAA4C,EAAA,UAsBT,SAASK,EAAQJ,EAAShB,EAAGT,EAAGC,EAAGC,EAAOC,EAAQuB,EAAWlB,EAAMY,EAAQ,CACzE,GAAM,CAAE,aAAAU,EAAc,eAAAC,CAAe,EAAIvB,EAEnCwB,EAAQP,EAAQ,MAAM,cAAc,EAC1C,QAASQ,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAAK,CACrC,IAAMC,EAAKD,EAAIH,EAAgBA,GAAgBE,EAAM,OAAS,GAAM,EAC9DL,EAAOlB,EACV,OAAO,MAAM,EACb,KAAK,IAAKT,EAAIE,EAAQ,CAAC,EACvB,KAAK,IAAKD,CAAC,EACX,KAAK,OAAQmB,CAAM,EACnB,MAAM,cAAe,QAAQ,EAC7B,MAAM,YAAaU,CAAY,EAC/B,MAAM,cAAeC,CAAc,EACtCJ,EACG,OAAO,OAAO,EACd,KAAK,IAAK3B,EAAIE,EAAQ,CAAC,EACvB,KAAK,KAAMgC,CAAE,EACb,KAAKF,EAAMC,CAAC,CAAC,EAEhBN,EACG,KAAK,IAAK1B,EAAIE,EAAS,CAAG,EAC1B,KAAK,oBAAqB,SAAS,EACnC,KAAK,qBAAsB,SAAS,EAEvCyB,EAAcD,EAAMD,CAAS,CAC/B,CACF,CA3BS9C,EAAAiD,EAAA,WAuCT,SAASM,EAAKV,EAAShB,EAAGT,EAAGC,EAAGC,EAAOC,EAAQuB,EAAWlB,EAAM,CAC9D,IAAM4B,EAAO3B,EAAE,OAAO,QAAQ,EASxBkB,EARIS,EACP,OAAO,eAAe,EACtB,KAAK,IAAKpC,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASC,CAAK,EACnB,KAAK,SAAUC,CAAM,EACrB,KAAK,WAAY,OAAO,EAGxB,OAAO,WAAW,EAClB,MAAM,UAAW,OAAO,EACxB,MAAM,SAAU,MAAM,EACtB,MAAM,QAAS,MAAM,EAExBwB,EACG,OAAO,KAAK,EACZ,KAAK,QAAS,OAAO,EACrB,MAAM,UAAW,YAAY,EAC7B,MAAM,aAAc,QAAQ,EAC5B,MAAM,iBAAkB,QAAQ,EAChC,KAAKF,CAAO,EAEfI,EAAQJ,EAASW,EAAMpC,EAAGC,EAAGC,EAAOC,EAAQuB,EAAWlB,CAAI,EAC3DoB,EAAcD,EAAMD,CAAS,CAC/B,CA1BS9C,EAAAuD,EAAA,QAgCT,SAASP,EAAcS,EAAQC,EAAmB,CAChD,QAAWC,KAAOD,EACZC,KAAOD,GAETD,EAAO,KAAKE,EAAKD,EAAkBC,CAAG,CAAC,CAG7C,CAPS,OAAA3D,EAAAgD,EAAA,iBASF,SAAUpB,EAAM,CACrB,OAAOA,EAAK,gBAAkB,KAAO2B,EAAO3B,EAAK,gBAAkB,MAAQgB,EAASK,CACtF,CACF,EAAG,EAEGW,GAAe5D,EAAA,SAAU6D,EAAU,CACvCA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,WAAW,EACtB,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,CAAC,EACrB,KAAK,eAAgB,CAAC,EACtB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,kBAAkB,CACjC,EAZqB,gBAcdC,EAAQ,CACb,SAAA/D,EACA,WAAAc,GACA,YAAAa,GACA,SAAAX,GACA,UAAAE,GACA,SAAAiB,GACA,mBAAAQ,GACA,aAAAkB,EACF,ECvZO,IAAMG,GAAUC,EAAA,SAAUC,EAAK,CACvB,OAAO,KAAKA,CAAG,EAEvB,QAAQ,SAAUC,EAAK,CAC1BC,EAAKD,CAAG,EAAID,EAAIC,CAAG,CACrB,CAAC,CACH,EANuB,WAQjBE,EAAS,CAAC,EACZC,EAAW,EAGf,SAASC,GAAgBC,EAAS,CAChC,IAAMJ,EAAOK,EAAU,EAAE,QACnBC,EAAgBN,EAAK,cAC3BE,EAAW,EACX,IAAIK,EAAO,GAEX,OAAO,KAAKN,CAAM,EAAE,QAASO,GAAW,CACtC,IAAMC,EAASR,EAAOO,CAAM,EAAE,MACxBE,EAAa,CACjB,GAAI,GACJ,GAAIH,EACJ,EAAG,EACH,KAAME,EACN,OAAQ,OACR,IAAKR,EAAOO,CAAM,EAAE,QACtB,EACAG,EAAQ,WAAWP,EAASM,CAAU,EAGtC,IAAIE,EAAcR,EAAQ,OAAO,MAAM,EAAE,KAAK,aAAc,QAAQ,EAAE,KAAKI,CAAM,EAC3EK,EAAgBD,EAAY,KAAK,EAAE,sBAAsB,EAAE,MACjEA,EAAY,OAAO,EAEnB,IAAIE,EAAQ,CAAC,EAGb,GAAID,GAAiBP,EACnBQ,EAAQ,CAACN,CAAM,MACV,CAEL,IAAMO,EAAQP,EAAO,MAAM,GAAG,EAC1BQ,EAAc,GAClBJ,EAAcR,EAAQ,OAAO,MAAM,EAAE,KAAK,aAAc,QAAQ,EAEhEW,EAAM,QAASE,GAAS,CAEtB,IAAMC,EAAWF,EAAc,GAAGA,CAAW,IAAIC,CAAI,GAAKA,EAI1D,GAHAL,EAAY,KAAKM,CAAQ,EACPN,EAAY,KAAK,EAAE,sBAAsB,EAAE,MAE7CN,GASd,GAPIU,GACFF,EAAM,KAAKE,CAAW,EAExBA,EAAcC,EAGdL,EAAY,KAAKK,CAAI,EACjBL,EAAY,KAAK,EAAE,sBAAsB,EAAE,MAAQN,EAAe,CACpE,IAAIa,EAAa,GACjB,QAAWC,KAAQH,EACjBE,GAAcC,EACdR,EAAY,KAAKO,EAAa,GAAG,EAC7BP,EAAY,KAAK,EAAE,sBAAsB,EAAE,MAAQN,IAErDQ,EAAM,KAAKK,EAAW,MAAM,EAAG,EAAE,EAAI,GAAG,EACxCA,EAAaC,GAGjBJ,EAAcG,CAChB,OAGAH,EAAcE,CAElB,CAAC,EAGGF,GACFF,EAAM,KAAKE,CAAW,EAExBJ,EAAY,OAAO,CACrB,CAEAE,EAAM,QAAQ,CAACO,EAAMC,IAAU,CAC7B,IAAMC,EAAY,CAChB,EAAG,GACH,EAAGhB,EAAO,EAAIe,EAAQ,GACtB,KAAM,OACN,KAAMD,EACN,WAAYrB,EAAK,eAAiB,CACpC,EAIMwB,EADcb,EAAQ,SAASP,EAASmB,CAAS,EACzB,KAAK,EAAE,sBAAsB,EAAE,MAIzDC,EAAYtB,GAAYsB,EAAYxB,EAAK,WAAawB,IACxDtB,EAAWsB,EAEf,CAAC,EAEDjB,GAAQ,KAAK,IAAI,GAAIO,EAAM,OAAS,EAAE,CACxC,CAAC,CACH,CAjGSjB,EAAAM,GAAA,mBAoGT,IAAMH,EAAOK,EAAU,EAAE,QACrBoB,EAAa,EACJC,GAAO7B,EAAA,SAAU8B,EAAMC,EAAIC,EAASC,EAAS,CACxD,IAAMC,EAAe1B,EAAU,EACzB2B,EAAaD,EAAa,QAAQ,WAClCE,EAAgBF,EAAa,QAAQ,cACrCG,EAAkBH,EAAa,QAAQ,gBAEvCI,EAAgBJ,EAAa,cAE/BK,EACAD,IAAkB,YACpBC,EAAiBC,EAAO,KAAOT,CAAE,GAEnC,IAAMU,EACJH,IAAkB,UACdE,EAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EACrDC,EAAO,MAAM,EAGnBE,EAAO,KAAK,EACZ,IAAMnC,EAAUkC,EAAK,OAAO,IAAMV,CAAE,EAEpCjB,EAAQ,aAAaP,CAAO,EAE5B,IAAMoC,EAAQV,EAAQ,GAAG,SAAS,EAC5BW,EAAQX,EAAQ,GAAG,gBAAgB,EAEnCY,EAAaZ,EAAQ,GAAG,UAAU,EACxC,QAAWa,KAAU1C,EACnB,OAAOA,EAAO0C,CAAM,EAEtB,IAAIC,EAAW,EACfF,EAAW,QAASG,GAAc,CAChC5C,EAAO4C,CAAS,EAAI,CAClB,MAAO7C,EAAK,aAAa4C,EAAW5C,EAAK,aAAa,MAAM,EAC5D,SAAU4C,CACZ,EACAA,GACF,CAAC,EAEDzC,GAAgBC,CAAO,EACvBqB,EAAazB,EAAK,WAAaE,EAC/BqC,EAAO,OAAO,EAAG,EAAGd,EAAY,OAAO,KAAKxB,CAAM,EAAE,OAAS,EAAE,EAC/D6C,GAAU1C,EAASoC,EAAO,CAAC,EAE3B,IAAMO,EAAMR,EAAO,UAAU,EACzBE,GACFrC,EACG,OAAO,MAAM,EACb,KAAKqC,CAAK,EACV,KAAK,IAAKhB,CAAU,EACpB,KAAK,YAAaQ,CAAa,EAC/B,KAAK,cAAe,MAAM,EAC1B,KAAK,IAAK,EAAE,EACZ,KAAK,OAAQD,CAAU,EACvB,KAAK,cAAeE,CAAe,EAGxC,IAAMc,EAASD,EAAI,MAAQA,EAAI,OAAS,EAAI/C,EAAK,eAC3CiD,EAAQxB,EAAasB,EAAI,MAAQ,EAAI/C,EAAK,eAEhDkD,GAAiB9C,EAAS4C,EAAQC,EAAOjD,EAAK,WAAW,EAGzDI,EACG,OAAO,MAAM,EACb,KAAK,KAAMqB,CAAU,EACrB,KAAK,KAAMzB,EAAK,OAAS,CAAC,EAC1B,KAAK,KAAMiD,EAAQxB,EAAa,CAAC,EACjC,KAAK,KAAMzB,EAAK,OAAS,CAAC,EAC1B,KAAK,eAAgB,CAAC,EACtB,KAAK,SAAU,OAAO,EACtB,KAAK,aAAc,iBAAiB,EAEvC,IAAMmD,EAAoBV,EAAQ,GAAK,EACvCrC,EAAQ,KAAK,UAAW,GAAG2C,EAAI,MAAM,QAAQE,CAAK,IAAID,EAASG,CAAiB,EAAE,EAClF/C,EAAQ,KAAK,sBAAuB,eAAe,EACnDA,EAAQ,KAAK,SAAU4C,EAASG,EAAoB,EAAE,CACxD,EA7EoB,QA+EPZ,EAAS,CACpB,KAAM,CACJ,OAAQ,OACR,MAAO,OACP,OAAQ,OACR,MAAO,MACT,EACA,YAAa,EAEb,cAAe,CAAC,EAChB,KAAM1C,EAAA,UAAY,CAChB,KAAK,cAAgB,CAAC,EACtB,KAAK,KAAO,CACV,OAAQ,OACR,MAAO,OACP,OAAQ,OACR,MAAO,MACT,EACA,KAAK,YAAc,CACrB,EATM,QAUN,UAAWA,EAAA,SAAUuD,EAAKrD,EAAKsD,EAAKC,EAAK,CACnCF,EAAIrD,CAAG,IAAM,OACfqD,EAAIrD,CAAG,EAAIsD,EAEXD,EAAIrD,CAAG,EAAIuD,EAAID,EAAKD,EAAIrD,CAAG,CAAC,CAEhC,EANW,aAOX,aAAcF,EAAA,SAAU0D,EAAQC,EAAQC,EAAOC,EAAO,CACpD,IAAM1D,EAAOK,EAAU,EAAE,QAEnBsD,EAAQ,KACVC,EAAM,EAEV,SAASC,EAASC,EAAqB,CACrC,OAAOjE,EAAA,SAA0BkE,EAAM,CACrCH,IAEA,IAAMI,EAAIL,EAAM,cAAc,OAASC,EAAM,EAC7CD,EAAM,UAAUI,EAAM,SAAUP,EAASQ,EAAIhE,EAAK,UAAW,KAAK,GAAG,EACrE2D,EAAM,UAAUI,EAAM,QAASL,EAAQM,EAAIhE,EAAK,UAAW,KAAK,GAAG,EAEnE2D,EAAM,UAAUpB,EAAO,KAAM,SAAUgB,EAASS,EAAIhE,EAAK,UAAW,KAAK,GAAG,EAC5E2D,EAAM,UAAUpB,EAAO,KAAM,QAASkB,EAAQO,EAAIhE,EAAK,UAAW,KAAK,GAAG,EAEpE8D,IAAS,eACbH,EAAM,UAAUI,EAAM,SAAUR,EAASS,EAAIhE,EAAK,UAAW,KAAK,GAAG,EACrE2D,EAAM,UAAUI,EAAM,QAASN,EAAQO,EAAIhE,EAAK,UAAW,KAAK,GAAG,EAEnE2D,EAAM,UAAUpB,EAAO,KAAM,SAAUiB,EAASQ,EAAIhE,EAAK,UAAW,KAAK,GAAG,EAC5E2D,EAAM,UAAUpB,EAAO,KAAM,QAASmB,EAAQM,EAAIhE,EAAK,UAAW,KAAK,GAAG,EAE9E,EAjBO,mBAkBT,CAnBSH,EAAAgE,EAAA,YAqBT,KAAK,cAAc,QAAQA,EAAS,CAAC,CACvC,EA5Bc,gBA6Bd,OAAQhE,EAAA,SAAU0D,EAAQC,EAAQC,EAAOC,EAAO,CAC9C,IAAMO,EAAU,KAAK,IAAIV,EAAQE,CAAK,EAChCS,EAAS,KAAK,IAAIX,EAAQE,CAAK,EAC/BU,EAAU,KAAK,IAAIX,EAAQE,CAAK,EAChCU,EAAS,KAAK,IAAIZ,EAAQE,CAAK,EAErC,KAAK,UAAUnB,EAAO,KAAM,SAAU0B,EAAS,KAAK,GAAG,EACvD,KAAK,UAAU1B,EAAO,KAAM,SAAU4B,EAAS,KAAK,GAAG,EACvD,KAAK,UAAU5B,EAAO,KAAM,QAAS2B,EAAQ,KAAK,GAAG,EACrD,KAAK,UAAU3B,EAAO,KAAM,QAAS6B,EAAQ,KAAK,GAAG,EAErD,KAAK,aAAaH,EAASE,EAASD,EAAQE,CAAM,CACpD,EAZQ,UAaR,gBAAiBvE,EAAA,SAAUwE,EAAM,CAC/B,KAAK,YAAc,KAAK,YAAcA,EACtC,KAAK,KAAK,MAAQ,KAAK,WACzB,EAHiB,mBAIjB,eAAgBxE,EAAA,UAAY,CAC1B,OAAO,KAAK,WACd,EAFgB,kBAGhB,UAAWA,EAAA,UAAY,CACrB,OAAO,KAAK,IACd,EAFW,YAGb,EAEMyE,GAAQtE,EAAK,aACbuE,GAAcvE,EAAK,eAEZ8C,GAAYjD,EAAA,SAAUO,EAASoC,EAAOgC,EAAa,CAC9D,IAAMxE,EAAOK,EAAU,EAAE,QACrBoE,EAAc,GACZC,EAAiB1E,EAAK,OAAS,EAAIA,EAAK,eACxC2E,EAAUH,EAAcE,EAE1BE,EAAgB,EAChBC,EAAO,OACPpE,EAAS,QACTqE,EAAM,EAGV,OAAW,CAACC,EAAGC,CAAI,IAAKxC,EAAM,QAAQ,EAAG,CACvC,GAAIiC,IAAgBO,EAAK,QAAS,CAChCH,EAAOP,GAAMM,EAAgBN,GAAM,MAAM,EACzCQ,EAAMF,EAAgBN,GAAM,OAC5B7D,EAAS8D,GAAYK,EAAgBL,GAAY,MAAM,EAGvD,IAAIU,EAAqB,EACnBC,EAAiBF,EAAK,QAC5B,QAASG,EAAYJ,EAAGI,EAAY3C,EAAM,QACpCA,EAAM2C,CAAS,EAAE,SAAWD,EADgBC,IAE9CF,EAAqBA,EAAqB,EAM9C,IAAMG,EAAU,CACd,EAAGL,EAAI/E,EAAK,WAAa+E,EAAI/E,EAAK,MAAQyB,EAC1C,EAAG,GACH,KAAMuD,EAAK,QACX,KAAAH,EACA,IAAAC,EACA,OAAArE,EACA,UAAWwE,CACb,EAEAtE,EAAQ,YAAYP,EAASgF,EAASpF,CAAI,EAC1CyE,EAAcO,EAAK,QACnBJ,GACF,CAGA,IAAMS,EAAaL,EAAK,OAAO,OAAO,CAACM,EAAKzC,KACtC5C,EAAO4C,CAAS,IAClByC,EAAIzC,CAAS,EAAI5C,EAAO4C,CAAS,GAG5ByC,GACN,CAAC,CAAC,EAGLN,EAAK,EAAID,EAAI/E,EAAK,WAAa+E,EAAI/E,EAAK,MAAQyB,EAChDuD,EAAK,EAAIL,EACTK,EAAK,MAAQhF,EAAK,eAClBgF,EAAK,OAAShF,EAAK,eACnBgF,EAAK,OAASvE,EACduE,EAAK,KAAOH,EACZG,EAAK,IAAMF,EACXE,EAAK,OAASK,EAGd1E,EAAQ,SAASP,EAAS4E,EAAMhF,CAAI,EACpCuC,EAAO,OAAOyC,EAAK,EAAGA,EAAK,EAAGA,EAAK,EAAIA,EAAK,MAAQhF,EAAK,WAAY,IAAM,EAAI,EAAE,CACnF,CACF,EAnEyB,aAqElBuF,GAAQ,CACb,QAAA3F,GACA,KAAA8B,EACF,EC5VO,IAAM8D,GAA6B,CACxC,OAAAC,GACA,GAAAC,EACA,SAAAC,GACA,OAAAC,GACA,KAAMC,EAACC,GAAQ,CACbH,GAAS,QAAQG,EAAI,OAAO,EAC5BJ,EAAG,MAAM,CACX,EAHM,OAIR", "names": ["parser", "o", "__name", "k", "v", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "journey_default", "parser", "currentSection", "sections", "tasks", "rawTasks", "clear", "__name", "addSection", "txt", "getSections", "getTasks", "allItemsProcessed", "compileTasks", "max<PERSON><PERSON><PERSON>", "iterationCount", "updateActors", "tempActors", "task", "addTask", "descr", "taskData", "pieces", "score", "peeps", "peopleList", "s", "rawTask", "addTaskOrg", "newTask", "compileTask", "pos", "allProcessed", "i", "getActors", "journeyDb_default", "getConfig", "setDiagramTitle", "getDiagramTitle", "setAccTitle", "getAccTitle", "setAccDescription", "getAccDescription", "getStyles", "__name", "options", "getIconStyles", "styles_default", "drawRect", "__name", "elem", "rectData", "drawFace", "element", "faceData", "circleElement", "face", "smile", "arc", "arc_default", "sad", "ambivalent", "drawCircle", "circleData", "drawText", "textData", "drawLabel", "txtObject", "genPoints", "x", "y", "width", "height", "cut", "polygon", "drawSection", "section", "conf", "g", "rect", "getNoteRect", "_drawTextCandidateFunc", "taskCount", "drawTask", "task", "center", "maxHeight", "xPos", "person", "colour", "circle", "drawBackgroundRect", "bounds", "byText", "content", "textAttrs", "text", "_setTextAttrs", "byTspan", "taskFontSize", "taskFontFamily", "lines", "i", "dy", "byFo", "body", "toText", "fromTextAttrsDict", "key", "initGraphics", "graphics", "svgDraw_default", "setConf", "__name", "cnf", "key", "conf", "actors", "max<PERSON><PERSON><PERSON>", "drawActorLegend", "diagram", "getConfig", "max<PERSON><PERSON><PERSON><PERSON>", "yPos", "person", "colour", "circleData", "svgDraw_default", "measureText", "fullTextWidth", "lines", "words", "currentLine", "word", "testLine", "<PERSON><PERSON><PERSON>", "char", "line", "index", "labelData", "lineWidth", "leftMargin", "draw", "text", "id", "version", "diagObj", "configObject", "titleColor", "titleFontSize", "titleFontFamily", "securityLevel", "sandboxElement", "select_default", "root", "bounds", "tasks", "title", "<PERSON><PERSON><PERSON><PERSON>", "member", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "drawTasks", "box", "height", "width", "configureSvgSize", "extraVertForTitle", "obj", "val", "fun", "startx", "starty", "stopx", "stopy", "_self", "cnt", "updateFn", "type", "item", "n", "_startx", "_stopx", "_starty", "_stopy", "bump", "fills", "textColours", "verticalPos", "lastSection", "sectionVHeight", "taskPos", "sectionNumber", "fill", "num", "i", "task", "taskInSectionCount", "currentSection", "taskIndex", "section", "taskActors", "acc", "journeyRenderer_default", "diagram", "journey_default", "journeyDb_default", "journeyRenderer_default", "styles_default", "__name", "cnf"]}