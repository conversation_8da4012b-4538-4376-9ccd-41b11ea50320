export function clear(): void;
export function insertEdgeLabel(elem: any, edge: any): Promise<any>;
export function positionEdgeLabel(edge: any, paths: any): void;
export function intersection(node: any, outsidePoint: any, insidePoint: any): {
    x: any;
    y: number;
} | {
    x: number;
    y: any;
};
export function insertEdge(elem: any, e: any, edge: any, clusterDb: any, diagramType: any, graph: any, id: any): {
    updatedPath: any;
    originalPath: any;
};
