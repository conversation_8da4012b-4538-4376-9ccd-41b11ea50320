import{o as Ee}from"./chunk-CRSA2SMT.mjs";import{$ as Le,J as se,L as Re,M as Fe,Y as ve,b as L,ha as U}from"./chunk-63ZE7VZ5.mjs";import{a,b as re,e as mt}from"./chunk-GTKDMUJJ.mjs";var _e=re((ir,Be)=>{"use strict";var P=1e3,j=P*60,D=j*60,E=D*24,Rt=E*7,Ft=E*365.25;Be.exports=function(o,e){e=e||{};var t=typeof o;if(t==="string"&&o.length>0)return vt(o);if(t==="number"&&isFinite(o))return e.long?Et(o):Lt(o);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(o))};function vt(o){if(o=String(o),!(o.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(o);if(e){var t=parseFloat(e[1]),n=(e[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return t*Ft;case"weeks":case"week":case"w":return t*Rt;case"days":case"day":case"d":return t*E;case"hours":case"hour":case"hrs":case"hr":case"h":return t*D;case"minutes":case"minute":case"mins":case"min":case"m":return t*j;case"seconds":case"second":case"secs":case"sec":case"s":return t*P;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}a(vt,"parse");function Lt(o){var e=Math.abs(o);return e>=E?Math.round(o/E)+"d":e>=D?Math.round(o/D)+"h":e>=j?Math.round(o/j)+"m":e>=P?Math.round(o/P)+"s":o+"ms"}a(Lt,"fmtShort");function Et(o){var e=Math.abs(o);return e>=E?J(o,e,E,"day"):e>=D?J(o,e,D,"hour"):e>=j?J(o,e,j,"minute"):e>=P?J(o,e,P,"second"):o+" ms"}a(Et,"fmtLong");function J(o,e,t,n){var r=e>=t*1.5;return Math.round(o/t)+" "+n+(r?"s":"")}a(J,"plural")});var qe=re((ar,Oe)=>{"use strict";function At(o){t.debug=t,t.default=t,t.coerce=p,t.disable=s,t.enable=r,t.enabled=l,t.humanize=_e(),t.destroy=u,Object.keys(o).forEach(c=>{t[c]=o[c]}),t.names=[],t.skips=[],t.formatters={};function e(c){let h=0;for(let f=0;f<c.length;f++)h=(h<<5)-h+c.charCodeAt(f),h|=0;return t.colors[Math.abs(h)%t.colors.length]}a(e,"selectColor"),t.selectColor=e;function t(c){let h,f=null,g,b;function d(...m){if(!d.enabled)return;let T=d,I=Number(new Date),C=I-(h||I);T.diff=C,T.prev=h,T.curr=I,h=I,m[0]=t.coerce(m[0]),typeof m[0]!="string"&&m.unshift("%O");let w=0;m[0]=m[0].replace(/%([a-zA-Z%])/g,(q,G)=>{if(q==="%%")return"%";w++;let v=t.formatters[G];if(typeof v=="function"){let dt=m[w];q=v.call(T,dt),m.splice(w,1),w--}return q}),t.formatArgs.call(T,m),(T.log||t.log).apply(T,m)}return a(d,"debug"),d.namespace=c,d.useColors=t.useColors(),d.color=t.selectColor(c),d.extend=n,d.destroy=t.destroy,Object.defineProperty(d,"enabled",{enumerable:!0,configurable:!1,get:a(()=>f!==null?f:(g!==t.namespaces&&(g=t.namespaces,b=t.enabled(c)),b),"get"),set:a(m=>{f=m},"set")}),typeof t.init=="function"&&t.init(d),d}a(t,"createDebug");function n(c,h){let f=t(this.namespace+(typeof h>"u"?":":h)+c);return f.log=this.log,f}a(n,"extend");function r(c){t.save(c),t.namespaces=c,t.names=[],t.skips=[];let h=(typeof c=="string"?c:"").trim().replace(" ",",").split(",").filter(Boolean);for(let f of h)f[0]==="-"?t.skips.push(f.slice(1)):t.names.push(f)}a(r,"enable");function i(c,h){let f=0,g=0,b=-1,d=0;for(;f<c.length;)if(g<h.length&&(h[g]===c[f]||h[g]==="*"))h[g]==="*"?(b=g,d=f,g++):(f++,g++);else if(b!==-1)g=b+1,d++,f=d;else return!1;for(;g<h.length&&h[g]==="*";)g++;return g===h.length}a(i,"matchesTemplate");function s(){let c=[...t.names,...t.skips.map(h=>"-"+h)].join(",");return t.enable(""),c}a(s,"disable");function l(c){for(let h of t.skips)if(i(c,h))return!1;for(let h of t.names)if(i(c,h))return!0;return!1}a(l,"enabled");function p(c){return c instanceof Error?c.stack||c.message:c}a(p,"coerce");function u(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return a(u,"destroy"),t.enable(t.load()),t}a(At,"setup");Oe.exports=At});var Ge=re((S,K)=>{"use strict";S.formatArgs=Mt;S.save=Pt;S.load=jt;S.useColors=zt;S.storage=Dt();S.destroy=(()=>{let o=!1;return()=>{o||(o=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();S.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function zt(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let o;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(o=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(o[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}a(zt,"useColors");function Mt(o){if(o[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+o[0]+(this.useColors?"%c ":" ")+"+"+K.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;o.splice(1,0,e,"color: inherit");let t=0,n=0;o[0].replace(/%[a-zA-Z%]/g,r=>{r!=="%%"&&(t++,r==="%c"&&(n=t))}),o.splice(n,0,e)}a(Mt,"formatArgs");S.log=console.debug||console.log||(()=>{});function Pt(o){try{o?S.storage.setItem("debug",o):S.storage.removeItem("debug")}catch{}}a(Pt,"save");function jt(){let o;try{o=S.storage.getItem("debug")}catch{}return!o&&typeof process<"u"&&"env"in process&&(o=process.env.DEBUG),o}a(jt,"load");function Dt(){try{return localStorage}catch{}}a(Dt,"localstorage");K.exports=qe()(S);var{formatters:Bt}=K.exports;Bt.j=function(o){try{return JSON.stringify(o)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var kt=Object.freeze({left:0,top:0,width:16,height:16}),M=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),oe=Object.freeze({...kt,...M}),Ae=Object.freeze({...oe,body:"",hidden:!1});var xt=Object.freeze({width:null,height:null}),ze=Object.freeze({...xt,...M});var ie=a((o,e,t,n="")=>{let r=o.split(":");if(o.slice(0,1)==="@"){if(r.length<2||r.length>3)return null;n=r.shift().slice(1)}if(r.length>3||!r.length)return null;if(r.length>1){let l=r.pop(),p=r.pop(),u={provider:r.length>0?r[0]:n,prefix:p,name:l};return e&&!Q(u)?null:u}let i=r[0],s=i.split("-");if(s.length>1){let l={provider:n,prefix:s.shift(),name:s.join("-")};return e&&!Q(l)?null:l}if(t&&n===""){let l={provider:n,prefix:"",name:i};return e&&!Q(l,t)?null:l}return null},"stringToIcon"),Q=a((o,e)=>o?!!((e&&o.prefix===""||o.prefix)&&o.name):!1,"validateIconName");function Me(o,e){let t={};!o.hFlip!=!e.hFlip&&(t.hFlip=!0),!o.vFlip!=!e.vFlip&&(t.vFlip=!0);let n=((o.rotate||0)+(e.rotate||0))%4;return n&&(t.rotate=n),t}a(Me,"mergeIconTransformations");function le(o,e){let t=Me(o,e);for(let n in Ae)n in M?n in o&&!(n in t)&&(t[n]=M[n]):n in e?t[n]=e[n]:n in o&&(t[n]=o[n]);return t}a(le,"mergeIconData");function Pe(o,e){let t=o.icons,n=o.aliases||Object.create(null),r=Object.create(null);function i(s){if(t[s])return r[s]=[];if(!(s in r)){r[s]=null;let l=n[s]&&n[s].parent,p=l&&i(l);p&&(r[s]=[l].concat(p))}return r[s]}return a(i,"resolve"),(e||Object.keys(t).concat(Object.keys(n))).forEach(i),r}a(Pe,"getIconsTree");function je(o,e,t){let n=o.icons,r=o.aliases||Object.create(null),i={};function s(l){i=le(n[l]||r[l],i)}return a(s,"parse"),s(e),t.forEach(s),le(o,i)}a(je,"internalGetIconData");function ae(o,e){if(o.icons[e])return je(o,e,[]);let t=Pe(o,[e])[e];return t?je(o,e,t):null}a(ae,"getIconData");var bt=/(-?[0-9.]*[0-9]+[0-9.]*)/g,wt=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function ce(o,e,t){if(e===1)return o;if(t=t||100,typeof o=="number")return Math.ceil(o*e*t)/t;if(typeof o!="string")return o;let n=o.split(bt);if(n===null||!n.length)return o;let r=[],i=n.shift(),s=wt.test(i);for(;;){if(s){let l=parseFloat(i);isNaN(l)?r.push(i):r.push(Math.ceil(l*e*t)/t)}else r.push(i);if(i=n.shift(),i===void 0)return r.join("");s=!s}}a(ce,"calculateSize");function yt(o,e="defs"){let t="",n=o.indexOf("<"+e);for(;n>=0;){let r=o.indexOf(">",n),i=o.indexOf("</"+e);if(r===-1||i===-1)break;let s=o.indexOf(">",i);if(s===-1)break;t+=o.slice(r+1,i).trim(),o=o.slice(0,n).trim()+o.slice(s+1)}return{defs:t,content:o}}a(yt,"splitSVGDefs");function Ct(o,e){return o?"<defs>"+o+"</defs>"+e:e}a(Ct,"mergeDefsAndContent");function De(o,e,t){let n=yt(o);return Ct(n.defs,e+n.content+t)}a(De,"wrapSVGContent");var St=a(o=>o==="unset"||o==="undefined"||o==="none","isUnsetKeyword");function pe(o,e){let t={...oe,...o},n={...ze,...e},r={left:t.left,top:t.top,width:t.width,height:t.height},i=t.body;[t,n].forEach(d=>{let m=[],T=d.hFlip,I=d.vFlip,C=d.rotate;T?I?C+=2:(m.push("translate("+(r.width+r.left).toString()+" "+(0-r.top).toString()+")"),m.push("scale(-1 1)"),r.top=r.left=0):I&&(m.push("translate("+(0-r.left).toString()+" "+(r.height+r.top).toString()+")"),m.push("scale(1 -1)"),r.top=r.left=0);let w;switch(C<0&&(C-=Math.floor(C/4)*4),C=C%4,C){case 1:w=r.height/2+r.top,m.unshift("rotate(90 "+w.toString()+" "+w.toString()+")");break;case 2:m.unshift("rotate(180 "+(r.width/2+r.left).toString()+" "+(r.height/2+r.top).toString()+")");break;case 3:w=r.width/2+r.left,m.unshift("rotate(-90 "+w.toString()+" "+w.toString()+")");break}C%2===1&&(r.left!==r.top&&(w=r.left,r.left=r.top,r.top=w),r.width!==r.height&&(w=r.width,r.width=r.height,r.height=w)),m.length&&(i=De(i,'<g transform="'+m.join(" ")+'">',"</g>"))});let s=n.width,l=n.height,p=r.width,u=r.height,c,h;s===null?(h=l===null?"1em":l==="auto"?u:l,c=ce(h,p/u)):(c=s==="auto"?p:s,h=l===null?ce(c,u/p):l==="auto"?u:l);let f={},g=a((d,m)=>{St(m)||(f[d]=m.toString())},"setAttr");g("width",c),g("height",h);let b=[r.left,r.top,p,u];return f.viewBox=b.join(" "),{attributes:f,viewBox:b,body:i}}a(pe,"iconToSVG");var Tt=/\sid="(\S+)"/g,It="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16),$t=0;function ue(o,e=It){let t=[],n;for(;n=Tt.exec(o);)t.push(n[1]);if(!t.length)return o;let r="suffix"+(Math.random()*16777216|Date.now()).toString(16);return t.forEach(i=>{let s=typeof e=="function"?e(i):e+($t++).toString(),l=i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");o=o.replace(new RegExp('([#;"])('+l+')([")]|\\.[a-z])',"g"),"$1"+s+r+"$3")}),o=o.replace(new RegExp(r,"g"),""),o}a(ue,"replaceIDs");function he(o,e){let t=o.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(let n in e)t+=" "+n+'="'+e[n]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+t+">"+o+"</svg>"}a(he,"iconToHTML");var hr=mt(Ge(),1);var _t={body:'<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/><text transform="translate(21.16 64.67)" style="fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;"><tspan x="0" y="0">?</tspan></text></g>',height:80,width:80},fe=new Map,We=new Map,yr=a(o=>{for(let e of o){if(!e.name)throw new Error('Invalid icon loader. Must have a "name" property with non-empty string value.');if(L.debug("Registering icon pack:",e.name),"loader"in e)We.set(e.name,e.loader);else if("icons"in e)fe.set(e.name,e.icons);else throw L.error("Invalid icon loader:",e),new Error('Invalid icon loader. Must have either "icons" or "loader" property.')}},"registerIconPacks"),Ne=a(async(o,e)=>{let t=ie(o,!0,e!==void 0);if(!t)throw new Error(`Invalid icon name: ${o}`);let n=t.prefix||e;if(!n)throw new Error(`Icon name must contain a prefix: ${o}`);let r=fe.get(n);if(!r){let s=We.get(n);if(!s)throw new Error(`Icon set not found: ${t.prefix}`);try{r={...await s(),prefix:n},fe.set(n,r)}catch(l){throw L.error(l),new Error(`Failed to load icon set: ${t.prefix}`)}}let i=ae(r,t.name);if(!i)throw new Error(`Icon not found: ${o}`);return i},"getRegisteredIconData"),Ze=a(async o=>{try{return await Ne(o),!0}catch{return!1}},"isIconAvailable"),Ve=a(async(o,e,t)=>{let n;try{n=await Ne(o,e?.fallbackPrefix)}catch(s){L.error(s),n=_t}let r=pe(n,e);return he(ue(r.body),{...r.attributes,...t})},"getIconSVG");function He(o){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];var n=Array.from(typeof o=="string"?[o]:o);n[n.length-1]=n[n.length-1].replace(/\r?\n([\t ]*)$/,"");var r=n.reduce(function(l,p){var u=p.match(/\n([\t ]+|(?!\s).)/g);return u?l.concat(u.map(function(c){var h,f;return(f=(h=c.match(/[\t ]/g))===null||h===void 0?void 0:h.length)!==null&&f!==void 0?f:0})):l},[]);if(r.length){var i=new RegExp(`
[	 ]{`+Math.min.apply(Math,r)+"}","g");n=n.map(function(l){return l.replace(i,`
`)})}n[0]=n[0].replace(/^\r?\n/,"");var s=n[0];return e.forEach(function(l,p){var u=s.match(/(?:^|\n)( *)$/),c=u?u[1]:"",h=l;typeof l=="string"&&l.includes(`
`)&&(h=String(l).split(`
`).map(function(f,g){return g===0?f:""+c+f}).join(`
`)),s+=h+n[p+1]}),s}a(He,"dedent");function me(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}a(me,"_getDefaults");var z=me();function Ye(o){z=o}a(Ye,"changeDefaults");var Z={exec:a(()=>null,"exec")};function x(o,e=""){let t=typeof o=="string"?o:o.source,n={replace:a((r,i)=>{let s=typeof i=="string"?i:i.source;return s=s.replace(y.caret,"$1"),t=t.replace(r,s),n},"replace"),getRegex:a(()=>new RegExp(t,e),"getRegex")};return n}a(x,"edit");var y={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:a(o=>new RegExp(`^( {0,3}${o})((?:[	 ][^\\n]*)?(?:\\n|$))`),"listItemRegex"),nextBulletRegex:a(o=>new RegExp(`^ {0,${Math.min(3,o-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),"nextBulletRegex"),hrRegex:a(o=>new RegExp(`^ {0,${Math.min(3,o-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),"hrRegex"),fencesBeginRegex:a(o=>new RegExp(`^ {0,${Math.min(3,o-1)}}(?:\`\`\`|~~~)`),"fencesBeginRegex"),headingBeginRegex:a(o=>new RegExp(`^ {0,${Math.min(3,o-1)}}#`),"headingBeginRegex"),htmlBeginRegex:a(o=>new RegExp(`^ {0,${Math.min(3,o-1)}}<(?:[a-z].*>|!--)`,"i"),"htmlBeginRegex")},Ot=/^(?:[ \t]*(?:\n|$))+/,qt=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,Gt=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,H=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Wt=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,ke=/(?:[*+-]|\d{1,9}[.)])/,et=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,tt=x(et).replace(/bull/g,ke).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),Nt=x(et).replace(/bull/g,ke).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),xe=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Zt=/^[^\n]+/,be=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Vt=x(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",be).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Ht=x(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,ke).getRegex(),ee="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",we=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Ut=x("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",we).replace("tag",ee).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),nt=x(xe).replace("hr",H).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ee).getRegex(),Qt=x(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",nt).getRegex(),ye={blockquote:Qt,code:qt,def:Vt,fences:Gt,heading:Wt,hr:H,html:Ut,lheading:tt,list:Ht,newline:Ot,paragraph:nt,table:Z,text:Zt},Ue=x("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",H).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ee).getRegex(),Jt={...ye,lheading:Nt,table:Ue,paragraph:x(xe).replace("hr",H).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Ue).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ee).getRegex()},Kt={...ye,html:x(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",we).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Z,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:x(xe).replace("hr",H).replace("heading",` *#{1,6} *[^
]`).replace("lheading",tt).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Xt=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Yt=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,rt=/^( {2,}|\\)\n(?!\s*$)/,en=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,te=/[\p{P}\p{S}]/u,Ce=/[\s\p{P}\p{S}]/u,st=/[^\s\p{P}\p{S}]/u,tn=x(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,Ce).getRegex(),ot=/(?!~)[\p{P}\p{S}]/u,nn=/(?!~)[\s\p{P}\p{S}]/u,rn=/(?:[^\s\p{P}\p{S}]|~)/u,sn=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,it=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,on=x(it,"u").replace(/punct/g,te).getRegex(),ln=x(it,"u").replace(/punct/g,ot).getRegex(),lt="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",an=x(lt,"gu").replace(/notPunctSpace/g,st).replace(/punctSpace/g,Ce).replace(/punct/g,te).getRegex(),cn=x(lt,"gu").replace(/notPunctSpace/g,rn).replace(/punctSpace/g,nn).replace(/punct/g,ot).getRegex(),pn=x("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,st).replace(/punctSpace/g,Ce).replace(/punct/g,te).getRegex(),un=x(/\\(punct)/,"gu").replace(/punct/g,te).getRegex(),hn=x(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),fn=x(we).replace("(?:-->|$)","-->").getRegex(),gn=x("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",fn).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Y=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,dn=x(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Y).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),at=x(/^!?\[(label)\]\[(ref)\]/).replace("label",Y).replace("ref",be).getRegex(),ct=x(/^!?\[(ref)\](?:\[\])?/).replace("ref",be).getRegex(),mn=x("reflink|nolink(?!\\()","g").replace("reflink",at).replace("nolink",ct).getRegex(),Se={_backpedal:Z,anyPunctuation:un,autolink:hn,blockSkip:sn,br:rt,code:Yt,del:Z,emStrongLDelim:on,emStrongRDelimAst:an,emStrongRDelimUnd:pn,escape:Xt,link:dn,nolink:ct,punctuation:tn,reflink:at,reflinkSearch:mn,tag:gn,text:en,url:Z},kn={...Se,link:x(/^!?\[(label)\]\((.*?)\)/).replace("label",Y).getRegex(),reflink:x(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Y).getRegex()},ge={...Se,emStrongRDelimAst:cn,emStrongLDelim:ln,url:x(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},xn={...ge,br:x(rt).replace("{2,}","*").getRegex(),text:x(ge.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},X={normal:ye,gfm:Jt,pedantic:Kt},W={normal:Se,gfm:ge,breaks:xn,pedantic:kn},bn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Qe=a(o=>bn[o],"getEscapeReplacement");function F(o,e){if(e){if(y.escapeTest.test(o))return o.replace(y.escapeReplace,Qe)}else if(y.escapeTestNoEncode.test(o))return o.replace(y.escapeReplaceNoEncode,Qe);return o}a(F,"escape");function Je(o){try{o=encodeURI(o).replace(y.percentDecode,"%")}catch{return null}return o}a(Je,"cleanUrl");function Ke(o,e){let t=o.replace(y.findPipe,(i,s,l)=>{let p=!1,u=s;for(;--u>=0&&l[u]==="\\";)p=!p;return p?"|":" |"}),n=t.split(y.splitPipe),r=0;if(n[0].trim()||n.shift(),n.length>0&&!n.at(-1)?.trim()&&n.pop(),e)if(n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(y.slashPipe,"|");return n}a(Ke,"splitCells");function N(o,e,t){let n=o.length;if(n===0)return"";let r=0;for(;r<n&&o.charAt(n-r-1)===e;)r++;return o.slice(0,n-r)}a(N,"rtrim");function wn(o,e){if(o.indexOf(e[1])===-1)return-1;let t=0;for(let n=0;n<o.length;n++)if(o[n]==="\\")n++;else if(o[n]===e[0])t++;else if(o[n]===e[1]&&(t--,t<0))return n;return-1}a(wn,"findClosingBracket");function Xe(o,e,t,n,r){let i=e.href,s=e.title||null,l=o[1].replace(r.other.outputLinkReplace,"$1");if(o[0].charAt(0)!=="!"){n.state.inLink=!0;let p={type:"link",raw:t,href:i,title:s,text:l,tokens:n.inlineTokens(l)};return n.state.inLink=!1,p}return{type:"image",raw:t,href:i,title:s,text:l}}a(Xe,"outputLink");function yn(o,e,t){let n=o.match(t.other.indentCodeCompensation);if(n===null)return e;let r=n[1];return e.split(`
`).map(i=>{let s=i.match(t.other.beginningSpace);if(s===null)return i;let[l]=s;return l.length>=r.length?i.slice(r.length):i}).join(`
`)}a(yn,"indentCodeCompensation");var _=class{static{a(this,"_Tokenizer")}options;rules;lexer;constructor(e){this.options=e||z}space(e){let t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){let t=this.rules.block.code.exec(e);if(t){let n=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:N(n,`
`)}}}fences(e){let t=this.rules.block.fences.exec(e);if(t){let n=t[0],r=yn(n,t[3]||"",this.rules);return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:r}}}heading(e){let t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(this.rules.other.endingHash.test(n)){let r=N(n,"#");(this.options.pedantic||!r||this.rules.other.endingSpaceChar.test(r))&&(n=r.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){let t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:N(t[0],`
`)}}blockquote(e){let t=this.rules.block.blockquote.exec(e);if(t){let n=N(t[0],`
`).split(`
`),r="",i="",s=[];for(;n.length>0;){let l=!1,p=[],u;for(u=0;u<n.length;u++)if(this.rules.other.blockquoteStart.test(n[u]))p.push(n[u]),l=!0;else if(!l)p.push(n[u]);else break;n=n.slice(u);let c=p.join(`
`),h=c.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");r=r?`${r}
${c}`:c,i=i?`${i}
${h}`:h;let f=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(h,s,!0),this.lexer.state.top=f,n.length===0)break;let g=s.at(-1);if(g?.type==="code")break;if(g?.type==="blockquote"){let b=g,d=b.raw+`
`+n.join(`
`),m=this.blockquote(d);s[s.length-1]=m,r=r.substring(0,r.length-b.raw.length)+m.raw,i=i.substring(0,i.length-b.text.length)+m.text;break}else if(g?.type==="list"){let b=g,d=b.raw+`
`+n.join(`
`),m=this.list(d);s[s.length-1]=m,r=r.substring(0,r.length-g.raw.length)+m.raw,i=i.substring(0,i.length-b.raw.length)+m.raw,n=d.substring(s.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:r,tokens:s,text:i}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim(),r=n.length>1,i={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");let s=this.rules.other.listItemRegex(n),l=!1;for(;e;){let u=!1,c="",h="";if(!(t=s.exec(e))||this.rules.block.hr.test(e))break;c=t[0],e=e.substring(c.length);let f=t[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,I=>" ".repeat(3*I.length)),g=e.split(`
`,1)[0],b=!f.trim(),d=0;if(this.options.pedantic?(d=2,h=f.trimStart()):b?d=t[1].length+1:(d=t[2].search(this.rules.other.nonSpaceChar),d=d>4?1:d,h=f.slice(d),d+=t[1].length),b&&this.rules.other.blankLine.test(g)&&(c+=g+`
`,e=e.substring(g.length+1),u=!0),!u){let I=this.rules.other.nextBulletRegex(d),C=this.rules.other.hrRegex(d),w=this.rules.other.fencesBeginRegex(d),ne=this.rules.other.headingBeginRegex(d),q=this.rules.other.htmlBeginRegex(d);for(;e;){let G=e.split(`
`,1)[0],v;if(g=G,this.options.pedantic?(g=g.replace(this.rules.other.listReplaceNesting,"  "),v=g):v=g.replace(this.rules.other.tabCharGlobal,"    "),w.test(g)||ne.test(g)||q.test(g)||I.test(g)||C.test(g))break;if(v.search(this.rules.other.nonSpaceChar)>=d||!g.trim())h+=`
`+v.slice(d);else{if(b||f.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||w.test(f)||ne.test(f)||C.test(f))break;h+=`
`+g}!b&&!g.trim()&&(b=!0),c+=G+`
`,e=e.substring(G.length+1),f=v.slice(d)}}i.loose||(l?i.loose=!0:this.rules.other.doubleBlankLine.test(c)&&(l=!0));let m=null,T;this.options.gfm&&(m=this.rules.other.listIsTask.exec(h),m&&(T=m[0]!=="[ ] ",h=h.replace(this.rules.other.listReplaceTask,""))),i.items.push({type:"list_item",raw:c,task:!!m,checked:T,loose:!1,text:h,tokens:[]}),i.raw+=c}let p=i.items.at(-1);if(p)p.raw=p.raw.trimEnd(),p.text=p.text.trimEnd();else return;i.raw=i.raw.trimEnd();for(let u=0;u<i.items.length;u++)if(this.lexer.state.top=!1,i.items[u].tokens=this.lexer.blockTokens(i.items[u].text,[]),!i.loose){let c=i.items[u].tokens.filter(f=>f.type==="space"),h=c.length>0&&c.some(f=>this.rules.other.anyLine.test(f.raw));i.loose=h}if(i.loose)for(let u=0;u<i.items.length;u++)i.items[u].loose=!0;return i}}html(e){let t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){let t=this.rules.block.def.exec(e);if(t){let n=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),r=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:r,title:i}}}table(e){let t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;let n=Ke(t[1]),r=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),i=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],s={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(let l of r)this.rules.other.tableAlignRight.test(l)?s.align.push("right"):this.rules.other.tableAlignCenter.test(l)?s.align.push("center"):this.rules.other.tableAlignLeft.test(l)?s.align.push("left"):s.align.push(null);for(let l=0;l<n.length;l++)s.header.push({text:n[l],tokens:this.lexer.inline(n[l]),header:!0,align:s.align[l]});for(let l of i)s.rows.push(Ke(l,s.header.length).map((p,u)=>({text:p,tokens:this.lexer.inline(p),header:!1,align:s.align[u]})));return s}}lheading(e){let t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){let t=this.rules.block.paragraph.exec(e);if(t){let n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){let t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){let t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){let t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){let t=this.rules.inline.link.exec(e);if(t){let n=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(n)){if(!this.rules.other.endAngleBracket.test(n))return;let s=N(n.slice(0,-1),"\\");if((n.length-s.length)%2===0)return}else{let s=wn(t[2],"()");if(s>-1){let p=(t[0].indexOf("!")===0?5:4)+t[1].length+s;t[2]=t[2].substring(0,s),t[0]=t[0].substring(0,p).trim(),t[3]=""}}let r=t[2],i="";if(this.options.pedantic){let s=this.rules.other.pedanticHrefTitle.exec(r);s&&(r=s[1],i=s[3])}else i=t[3]?t[3].slice(1,-1):"";return r=r.trim(),this.rules.other.startAngleBracket.test(r)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(n)?r=r.slice(1):r=r.slice(1,-1)),Xe(t,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let r=(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," "),i=t[r.toLowerCase()];if(!i){let s=n[0].charAt(0);return{type:"text",raw:s,text:s}}return Xe(n,i,n[0],this.lexer,this.rules)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(!r||r[3]&&n.match(this.rules.other.unicodeAlphaNumeric))return;if(!(r[1]||r[2]||"")||!n||this.rules.inline.punctuation.exec(n)){let s=[...r[0]].length-1,l,p,u=s,c=0,h=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(h.lastIndex=0,t=t.slice(-1*e.length+s);(r=h.exec(t))!=null;){if(l=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!l)continue;if(p=[...l].length,r[3]||r[4]){u+=p;continue}else if((r[5]||r[6])&&s%3&&!((s+p)%3)){c+=p;continue}if(u-=p,u>0)continue;p=Math.min(p,p+u+c);let f=[...r[0]][0].length,g=e.slice(0,s+r.index+f+p);if(Math.min(s,p)%2){let d=g.slice(1,-1);return{type:"em",raw:g,text:d,tokens:this.lexer.inlineTokens(d)}}let b=g.slice(2,-2);return{type:"strong",raw:g,text:b,tokens:this.lexer.inlineTokens(b)}}}}codespan(e){let t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(this.rules.other.newLineCharGlobal," "),r=this.rules.other.nonSpaceChar.test(n),i=this.rules.other.startingSpaceChar.test(n)&&this.rules.other.endingSpaceChar.test(n);return r&&i&&(n=n.substring(1,n.length-1)),{type:"codespan",raw:t[0],text:n}}}br(e){let t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){let t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){let t=this.rules.inline.autolink.exec(e);if(t){let n,r;return t[2]==="@"?(n=t[1],r="mailto:"+n):(n=t[1],r=n),{type:"link",raw:t[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let n,r;if(t[2]==="@")n=t[0],r="mailto:"+n;else{let i;do i=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??"";while(i!==t[0]);n=t[0],t[1]==="www."?r="http://"+t[0]:r=t[0]}return{type:"link",raw:t[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(e){let t=this.rules.inline.text.exec(e);if(t){let n=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:n}}}},$=class o{static{a(this,"_Lexer")}tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||z,this.options.tokenizer=this.options.tokenizer||new _,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let t={other:y,block:X.normal,inline:W.normal};this.options.pedantic?(t.block=X.pedantic,t.inline=W.pedantic):this.options.gfm&&(t.block=X.gfm,this.options.breaks?t.inline=W.breaks:t.inline=W.gfm),this.tokenizer.rules=t}static get rules(){return{block:X,inline:W}}static lex(e,t){return new o(t).lex(e)}static lexInline(e,t){return new o(t).inlineTokens(e)}lex(e){e=e.replace(y.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){let n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){for(this.options.pedantic&&(e=e.replace(y.tabCharGlobal,"    ").replace(y.spaceLine,""));e;){let r;if(this.options.extensions?.block?.some(s=>(r=s.call({lexer:this},e,t))?(e=e.substring(r.raw.length),t.push(r),!0):!1))continue;if(r=this.tokenizer.space(e)){e=e.substring(r.raw.length);let s=t.at(-1);r.raw.length===1&&s!==void 0?s.raw+=`
`:t.push(r);continue}if(r=this.tokenizer.code(e)){e=e.substring(r.raw.length);let s=t.at(-1);s?.type==="paragraph"||s?.type==="text"?(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue.at(-1).src=s.text):t.push(r);continue}if(r=this.tokenizer.fences(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.heading(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.hr(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.blockquote(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.list(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.html(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.def(e)){e=e.substring(r.raw.length);let s=t.at(-1);s?.type==="paragraph"||s?.type==="text"?(s.raw+=`
`+r.raw,s.text+=`
`+r.raw,this.inlineQueue.at(-1).src=s.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.lheading(e)){e=e.substring(r.raw.length),t.push(r);continue}let i=e;if(this.options.extensions?.startBlock){let s=1/0,l=e.slice(1),p;this.options.extensions.startBlock.forEach(u=>{p=u.call({lexer:this},l),typeof p=="number"&&p>=0&&(s=Math.min(s,p))}),s<1/0&&s>=0&&(i=e.substring(0,s+1))}if(this.state.top&&(r=this.tokenizer.paragraph(i))){let s=t.at(-1);n&&s?.type==="paragraph"?(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=s.text):t.push(r),n=i.length!==e.length,e=e.substring(r.raw.length);continue}if(r=this.tokenizer.text(e)){e=e.substring(r.raw.length);let s=t.at(-1);s?.type==="text"?(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=s.text):t.push(r);continue}if(e){let s="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(s);break}else throw new Error(s)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n=e,r=null;if(this.tokens.links){let l=Object.keys(this.tokens.links);if(l.length>0)for(;(r=this.tokenizer.rules.inline.reflinkSearch.exec(n))!=null;)l.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(r=this.tokenizer.rules.inline.blockSkip.exec(n))!=null;)n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(r=this.tokenizer.rules.inline.anyPunctuation.exec(n))!=null;)n=n.slice(0,r.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);let i=!1,s="";for(;e;){i||(s=""),i=!1;let l;if(this.options.extensions?.inline?.some(u=>(l=u.call({lexer:this},e,t))?(e=e.substring(l.raw.length),t.push(l),!0):!1))continue;if(l=this.tokenizer.escape(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.tag(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.link(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(l.raw.length);let u=t.at(-1);l.type==="text"&&u?.type==="text"?(u.raw+=l.raw,u.text+=l.text):t.push(l);continue}if(l=this.tokenizer.emStrong(e,n,s)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.codespan(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.br(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.del(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.autolink(e)){e=e.substring(l.raw.length),t.push(l);continue}if(!this.state.inLink&&(l=this.tokenizer.url(e))){e=e.substring(l.raw.length),t.push(l);continue}let p=e;if(this.options.extensions?.startInline){let u=1/0,c=e.slice(1),h;this.options.extensions.startInline.forEach(f=>{h=f.call({lexer:this},c),typeof h=="number"&&h>=0&&(u=Math.min(u,h))}),u<1/0&&u>=0&&(p=e.substring(0,u+1))}if(l=this.tokenizer.inlineText(p)){e=e.substring(l.raw.length),l.raw.slice(-1)!=="_"&&(s=l.raw.slice(-1)),i=!0;let u=t.at(-1);u?.type==="text"?(u.raw+=l.raw,u.text+=l.text):t.push(l);continue}if(e){let u="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(u);break}else throw new Error(u)}}return t}},O=class{static{a(this,"_Renderer")}options;parser;constructor(e){this.options=e||z}space(e){return""}code({text:e,lang:t,escaped:n}){let r=(t||"").match(y.notSpaceStart)?.[0],i=e.replace(y.endingNewline,"")+`
`;return r?'<pre><code class="language-'+F(r)+'">'+(n?i:F(i,!0))+`</code></pre>
`:"<pre><code>"+(n?i:F(i,!0))+`</code></pre>
`}blockquote({tokens:e}){return`<blockquote>
${this.parser.parse(e)}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return`<hr>
`}list(e){let t=e.ordered,n=e.start,r="";for(let l=0;l<e.items.length;l++){let p=e.items[l];r+=this.listitem(p)}let i=t?"ol":"ul",s=t&&n!==1?' start="'+n+'"':"";return"<"+i+s+`>
`+r+"</"+i+`>
`}listitem(e){let t="";if(e.task){let n=this.checkbox({checked:!!e.checked});e.loose?e.tokens[0]?.type==="paragraph"?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type==="text"&&(e.tokens[0].tokens[0].text=n+" "+F(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",n="";for(let i=0;i<e.header.length;i++)n+=this.tablecell(e.header[i]);t+=this.tablerow({text:n});let r="";for(let i=0;i<e.rows.length;i++){let s=e.rows[i];n="";for(let l=0;l<s.length;l++)n+=this.tablecell(s[l]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+r+`</table>
`}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){let t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${F(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){let r=this.parser.parseInline(n),i=Je(e);if(i===null)return r;e=i;let s='<a href="'+e+'"';return t&&(s+=' title="'+F(t)+'"'),s+=">"+r+"</a>",s}image({href:e,title:t,text:n}){let r=Je(e);if(r===null)return F(n);e=r;let i=`<img src="${e}" alt="${n}"`;return t&&(i+=` title="${F(t)}"`),i+=">",i}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:F(e.text)}},V=class{static{a(this,"_TextRenderer")}strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}},R=class o{static{a(this,"_Parser")}options;renderer;textRenderer;constructor(e){this.options=e||z,this.options.renderer=this.options.renderer||new O,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new V}static parse(e,t){return new o(t).parse(e)}static parseInline(e,t){return new o(t).parseInline(e)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let l=i,p=this.options.extensions.renderers[l.type].call({parser:this},l);if(p!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){n+=p||"";continue}}let s=i;switch(s.type){case"space":{n+=this.renderer.space(s);continue}case"hr":{n+=this.renderer.hr(s);continue}case"heading":{n+=this.renderer.heading(s);continue}case"code":{n+=this.renderer.code(s);continue}case"table":{n+=this.renderer.table(s);continue}case"blockquote":{n+=this.renderer.blockquote(s);continue}case"list":{n+=this.renderer.list(s);continue}case"html":{n+=this.renderer.html(s);continue}case"paragraph":{n+=this.renderer.paragraph(s);continue}case"text":{let l=s,p=this.renderer.text(l);for(;r+1<e.length&&e[r+1].type==="text";)l=e[++r],p+=`
`+this.renderer.text(l);t?n+=this.renderer.paragraph({type:"paragraph",raw:p,text:p,tokens:[{type:"text",raw:p,text:p,escaped:!0}]}):n+=p;continue}default:{let l='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return n}parseInline(e,t=this.renderer){let n="";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let l=this.options.extensions.renderers[i.type].call({parser:this},i);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=l||"";continue}}let s=i;switch(s.type){case"escape":{n+=t.text(s);break}case"html":{n+=t.html(s);break}case"link":{n+=t.link(s);break}case"image":{n+=t.image(s);break}case"strong":{n+=t.strong(s);break}case"em":{n+=t.em(s);break}case"codespan":{n+=t.codespan(s);break}case"br":{n+=t.br(s);break}case"del":{n+=t.del(s);break}case"text":{n+=t.text(s);break}default:{let l='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return n}},B=class{static{a(this,"_Hooks")}options;block;constructor(e){this.options=e||z}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?$.lex:$.lexInline}provideParser(){return this.block?R.parse:R.parseInline}},de=class{static{a(this,"Marked")}defaults=me();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=R;Renderer=O;TextRenderer=V;Lexer=$;Tokenizer=_;Hooks=B;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(let r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":{let i=r;for(let s of i.header)n=n.concat(this.walkTokens(s.tokens,t));for(let s of i.rows)for(let l of s)n=n.concat(this.walkTokens(l.tokens,t));break}case"list":{let i=r;n=n.concat(this.walkTokens(i.items,t));break}default:{let i=r;this.defaults.extensions?.childTokens?.[i.type]?this.defaults.extensions.childTokens[i.type].forEach(s=>{let l=i[s].flat(1/0);n=n.concat(this.walkTokens(l,t))}):i.tokens&&(n=n.concat(this.walkTokens(i.tokens,t)))}}return n}use(...e){let t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(n=>{let r={...n};if(r.async=this.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if("renderer"in i){let s=t.renderers[i.name];s?t.renderers[i.name]=function(...l){let p=i.renderer.apply(this,l);return p===!1&&(p=s.apply(this,l)),p}:t.renderers[i.name]=i.renderer}if("tokenizer"in i){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");let s=t[i.level];s?s.unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level==="block"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level==="inline"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}"childTokens"in i&&i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),r.extensions=t),n.renderer){let i=this.defaults.renderer||new O(this.defaults);for(let s in n.renderer){if(!(s in i))throw new Error(`renderer '${s}' does not exist`);if(["options","parser"].includes(s))continue;let l=s,p=n.renderer[l],u=i[l];i[l]=(...c)=>{let h=p.apply(i,c);return h===!1&&(h=u.apply(i,c)),h||""}}r.renderer=i}if(n.tokenizer){let i=this.defaults.tokenizer||new _(this.defaults);for(let s in n.tokenizer){if(!(s in i))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;let l=s,p=n.tokenizer[l],u=i[l];i[l]=(...c)=>{let h=p.apply(i,c);return h===!1&&(h=u.apply(i,c)),h}}r.tokenizer=i}if(n.hooks){let i=this.defaults.hooks||new B;for(let s in n.hooks){if(!(s in i))throw new Error(`hook '${s}' does not exist`);if(["options","block"].includes(s))continue;let l=s,p=n.hooks[l],u=i[l];B.passThroughHooks.has(s)?i[l]=c=>{if(this.defaults.async)return Promise.resolve(p.call(i,c)).then(f=>u.call(i,f));let h=p.call(i,c);return u.call(i,h)}:i[l]=(...c)=>{let h=p.apply(i,c);return h===!1&&(h=u.apply(i,c)),h}}r.hooks=i}if(n.walkTokens){let i=this.defaults.walkTokens,s=n.walkTokens;r.walkTokens=function(l){let p=[];return p.push(s.call(this,l)),i&&(p=p.concat(i.call(this,l))),p}}this.defaults={...this.defaults,...r}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return $.lex(e,t??this.defaults)}parser(e,t){return R.parse(e,t??this.defaults)}parseMarkdown(e){return a((n,r)=>{let i={...r},s={...this.defaults,...i},l=this.onError(!!s.silent,!!s.async);if(this.defaults.async===!0&&i.async===!1)return l(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof n>"u"||n===null)return l(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return l(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));s.hooks&&(s.hooks.options=s,s.hooks.block=e);let p=s.hooks?s.hooks.provideLexer():e?$.lex:$.lexInline,u=s.hooks?s.hooks.provideParser():e?R.parse:R.parseInline;if(s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(n):n).then(c=>p(c,s)).then(c=>s.hooks?s.hooks.processAllTokens(c):c).then(c=>s.walkTokens?Promise.all(this.walkTokens(c,s.walkTokens)).then(()=>c):c).then(c=>u(c,s)).then(c=>s.hooks?s.hooks.postprocess(c):c).catch(l);try{s.hooks&&(n=s.hooks.preprocess(n));let c=p(n,s);s.hooks&&(c=s.hooks.processAllTokens(c)),s.walkTokens&&this.walkTokens(c,s.walkTokens);let h=u(c,s);return s.hooks&&(h=s.hooks.postprocess(h)),h}catch(c){return l(c)}},"parse")}onError(e,t){return n=>{if(n.message+=`
Please report this to https://github.com/markedjs/marked.`,e){let r="<p>An error occurred:</p><pre>"+F(n.message+"",!0)+"</pre>";return t?Promise.resolve(r):r}if(t)return Promise.reject(n);throw n}}},A=new de;function k(o,e){return A.parse(o,e)}a(k,"marked");k.options=k.setOptions=function(o){return A.setOptions(o),k.defaults=A.defaults,Ye(k.defaults),k};k.getDefaults=me;k.defaults=z;k.use=function(...o){return A.use(...o),k.defaults=A.defaults,Ye(k.defaults),k};k.walkTokens=function(o,e){return A.walkTokens(o,e)};k.parseInline=A.parseInline;k.Parser=R;k.parser=R.parse;k.Renderer=O;k.TextRenderer=V;k.Lexer=$;k.lexer=$.lex;k.Tokenizer=_;k.Hooks=B;k.parse=k;var $r=k.options,Rr=k.setOptions,Fr=k.use,vr=k.walkTokens,Lr=k.parseInline;var Er=R.parse,Ar=$.lex;function Cn(o,{markdownAutoWrap:e}){let n=o.replace(/<br\/>/g,`
`).replace(/\n{2,}/g,`
`),r=He(n);return e===!1?r.replace(/ /g,"&nbsp;"):r}a(Cn,"preprocessMarkdown");function pt(o,e={}){let t=Cn(o,e),n=k.lexer(t),r=[[]],i=0;function s(l,p="normal"){l.type==="text"?l.text.split(`
`).forEach((c,h)=>{h!==0&&(i++,r.push([])),c.split(" ").forEach(f=>{f=f.replace(/&#39;/g,"'"),f&&r[i].push({content:f,type:p})})}):l.type==="strong"||l.type==="em"?l.tokens.forEach(u=>{s(u,l.type)}):l.type==="html"&&r[i].push({content:l.text,type:"normal"})}return a(s,"processNode"),n.forEach(l=>{l.type==="paragraph"?l.tokens?.forEach(p=>{s(p)}):l.type==="html"&&r[i].push({content:l.text,type:"normal"})}),r}a(pt,"markdownToLines");function ut(o,{markdownAutoWrap:e}={}){let t=k.lexer(o);function n(r){return r.type==="text"?e===!1?r.text.replace(/\n */g,"<br/>").replace(/ /g,"&nbsp;"):r.text.replace(/\n */g,"<br/>"):r.type==="strong"?`<strong>${r.tokens?.map(n).join("")}</strong>`:r.type==="em"?`<em>${r.tokens?.map(n).join("")}</em>`:r.type==="paragraph"?`<p>${r.tokens?.map(n).join("")}</p>`:r.type==="space"?"":r.type==="html"?`${r.text}`:r.type==="escape"?r.text:`Unsupported markdown: ${r.type}`}return a(n,"output"),t.map(n).join("")}a(ut,"markdownToHTML");function Sn(o){return Intl.Segmenter?[...new Intl.Segmenter().segment(o)].map(e=>e.segment):[...o]}a(Sn,"splitTextToChars");function Tn(o,e){let t=Sn(e.content);return ht(o,[],t,e.type)}a(Tn,"splitWordToFitWidth");function ht(o,e,t,n){if(t.length===0)return[{content:e.join(""),type:n},{content:"",type:n}];let[r,...i]=t,s=[...e,r];return o([{content:s.join(""),type:n}])?ht(o,s,i,n):(e.length===0&&r&&(e.push(r),t.shift()),[{content:e.join(""),type:n},{content:t.join(""),type:n}])}a(ht,"splitWordToFitWidthRecursion");function ft(o,e){if(o.some(({content:t})=>t.includes(`
`)))throw new Error("splitLineToFitWidth does not support newlines in the line");return Te(o,e)}a(ft,"splitLineToFitWidth");function Te(o,e,t=[],n=[]){if(o.length===0)return n.length>0&&t.push(n),t.length>0?t:[];let r="";o[0].content===" "&&(r=" ",o.shift());let i=o.shift()??{content:" ",type:"normal"},s=[...n];if(r!==""&&s.push({content:r,type:"normal"}),s.push(i),e(s))return Te(o,e,t,s);if(n.length>0)t.push(n),o.unshift(i);else if(i.content){let[l,p]=Tn(e,i);t.push([l]),p.content&&o.unshift(p)}return Te(o,e,t)}a(Te,"splitLineToFitWidthRecursion");function gt(o,e){e&&o.attr("style",e)}a(gt,"applyStyle");async function In(o,e,t,n,r=!1){let i=o.append("foreignObject");i.attr("width",`${10*t}px`),i.attr("height",`${10*t}px`);let s=i.append("xhtml:div"),l=e.label;e.label&&se(e.label)&&(l=await Re(e.label.replace(Fe.lineBreakRegex,`
`),ve()));let p=e.isNode?"nodeLabel":"edgeLabel",u=s.append("span");u.html(l),gt(u,e.labelStyle),u.attr("class",`${p} ${n}`),gt(s,e.labelStyle),s.style("display","table-cell"),s.style("white-space","nowrap"),s.style("line-height","1.5"),s.style("max-width",t+"px"),s.style("text-align","center"),s.attr("xmlns","http://www.w3.org/1999/xhtml"),r&&s.attr("class","labelBkg");let c=s.node().getBoundingClientRect();return c.width===t&&(s.style("display","table"),s.style("white-space","break-spaces"),s.style("width",t+"px"),c=s.node().getBoundingClientRect()),i.node()}a(In,"addHtmlSpan");function Ie(o,e,t){return o.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",e*t-.1+"em").attr("dy",t+"em")}a(Ie,"createTspan");function $n(o,e,t){let n=o.append("text"),r=Ie(n,1,e);$e(r,t);let i=r.node().getComputedTextLength();return n.remove(),i}a($n,"computeWidthOfText");function Qr(o,e,t){let n=o.append("text"),r=Ie(n,1,e);$e(r,[{content:t,type:"normal"}]);let i=r.node()?.getBoundingClientRect();return i&&n.remove(),i}a(Qr,"computeDimensionOfText");function Rn(o,e,t,n=!1){let i=e.append("g"),s=i.insert("rect").attr("class","background").attr("style","stroke: none"),l=i.append("text").attr("y","-10.1"),p=0;for(let u of t){let c=a(f=>$n(i,1.1,f)<=o,"checkWidth"),h=c(u)?[u]:ft(u,c);for(let f of h){let g=Ie(l,p,1.1);$e(g,f),p++}}if(n){let u=l.node().getBBox(),c=2;return s.attr("x",u.x-c).attr("y",u.y-c).attr("width",u.width+2*c).attr("height",u.height+2*c),i.node()}else return l.node()}a(Rn,"createFormattedText");function $e(o,e){o.text(""),e.forEach((t,n)=>{let r=o.append("tspan").attr("font-style",t.type==="em"?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight",t.type==="strong"?"bold":"normal");n===0?r.text(t.content):r.text(" "+t.content)})}a($e,"updateTextContentAndStyles");async function Fn(o){let e=[];o.replace(/(fa[bklrs]?):fa-([\w-]+)/g,(n,r,i)=>(e.push((async()=>{let s=`${r}:${i}`;return await Ze(s)?await Ve(s,void 0,{class:"label-icon"}):`<i class='${Le(n).replace(":"," ")}'></i>`})()),n));let t=await Promise.all(e);return o.replace(/(fa[bklrs]?):fa-([\w-]+)/g,()=>t.shift()??"")}a(Fn,"replaceIconSubstring");var Jr=a(async(o,e="",{style:t="",isTitle:n=!1,classes:r="",useHtmlLabels:i=!0,isNode:s=!0,width:l=200,addSvgBackground:p=!1}={},u)=>{if(L.debug("XYZ createText",e,t,n,r,i,s,"addSvgBackground: ",p),i){let c=ut(e,u),h=await Fn(Ee(c)),f=e.replace(/\\\\/g,"\\"),g={isNode:s,label:se(e)?f:h,labelStyle:t.replace("fill:","color:")};return await In(o,g,l,r,p)}else{let c=e.replace(/<br\s*\/?>/g,"<br/>"),h=pt(c.replace("<br>","<br/>"),u),f=Rn(l,o,h,e?p:!1);if(s){/stroke:/.exec(t)&&(t=t.replace("stroke:","lineColor:"));let g=t.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");U(f).attr("style",g)}else{let g=t.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/background:/g,"fill:");U(f).select("rect").attr("style",g.replace(/background:/g,"fill:"));let b=t.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");U(f).select("text").attr("style",b)}return f}},"createText");export{_t as a,yr as b,Ve as c,He as d,Qr as e,Fn as f,Jr as g};
