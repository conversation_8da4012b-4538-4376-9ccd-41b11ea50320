import{f as a}from"./chunk-NPFRQMEE.mjs";import{a as o}from"./chunk-GTKDMUJJ.mjs";var t={},n={info:a(async()=>{let{createInfoServices:e}=await import("./info-3VTXS3R3-AQVDCF6T.mjs"),r=e().Info.parser.LangiumParser;t.info=r},"info"),packet:a(async()=>{let{createPacketServices:e}=await import("./packet-DSR6H3E6-X5MPTC4R.mjs"),r=e().Packet.parser.LangiumParser;t.packet=r},"packet"),pie:a(async()=>{let{createPieServices:e}=await import("./pie-GAM7RPQU-T5NX3J7C.mjs"),r=e().Pie.parser.LangiumParser;t.pie=r},"pie"),architecture:a(async()=>{let{createArchitectureServices:e}=await import("./architecture-I2MV5QL6-P6QY7IHZ.mjs"),r=e().Architecture.parser.LangiumParser;t.architecture=r},"architecture"),gitGraph:a(async()=>{let{createGitGraphServices:e}=await import("./gitGraph-PIIEIUND-EMGFVSJW.mjs"),r=e().GitGraph.parser.LangiumParser;t.gitGraph=r},"gitGraph"),radar:a(async()=>{let{createRadarServices:e}=await import("./radar-NEH6LVNW-P44L663V.mjs"),r=e().Radar.parser.LangiumParser;t.radar=r},"radar"),treemap:a(async()=>{let{createTreemapServices:e}=await import("./treemap-FKARHQ26-ARMCRNF4.mjs"),r=e().Treemap.parser.LangiumParser;t.treemap=r},"treemap")};async function p(e,r){let i=n[e];if(!i)throw new Error(`Unknown diagram type: ${e}`);t[e]||await i();let s=t[e].parse(r);if(s.lexerErrors.length>0||s.parserErrors.length>0)throw new m(s);return s.value}o(p,"parse");a(p,"parse");var m=class extends Error{static{o(this,"MermaidParseError")}constructor(e){let r=e.lexerErrors.map(c=>c.message).join(`
`),i=e.parserErrors.map(c=>c.message).join(`
`);super(`Parsing failed: ${r} ${i}`),this.result=e}static{a(this,"MermaidParseError")}};export{p as a};
