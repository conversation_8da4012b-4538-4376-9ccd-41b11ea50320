{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-QKX3RCWE.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  MermaidGeneratedSharedModule,\n  TreemapGeneratedModule,\n  __name\n} from \"./chunk-ORCS5NZH.mjs\";\n\n// src/language/treemap/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/treemap/tokenBuilder.ts\nvar TreemapTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"TreemapTokenBuilder\");\n  }\n  constructor() {\n    super([\"treemap\"]);\n  }\n};\n\n// src/language/treemap/valueConverter.ts\nvar classDefRegex = /classDef\\s+([A-Z_a-z]\\w+)(?:\\s+([^\\n\\r;]*))?;?/;\nvar TreemapValueConverter = class extends AbstractMermaidValueConverter {\n  static {\n    __name(this, \"TreemapValueConverter\");\n  }\n  runCustomConverter(rule, input, _cstNode) {\n    if (rule.name === \"NUMBER2\") {\n      return parseFloat(input.replace(/,/g, \"\"));\n    } else if (rule.name === \"SEPARATOR\") {\n      return input.substring(1, input.length - 1);\n    } else if (rule.name === \"STRING2\") {\n      return input.substring(1, input.length - 1);\n    } else if (rule.name === \"INDENTATION\") {\n      return input.length;\n    } else if (rule.name === \"ClassDef\") {\n      if (typeof input !== \"string\") {\n        return input;\n      }\n      const match = classDefRegex.exec(input);\n      if (match) {\n        return {\n          $type: \"ClassDefStatement\",\n          className: match[1],\n          styleText: match[2] || void 0\n        };\n      }\n    }\n    return void 0;\n  }\n};\n\n// src/language/treemap/treemap-validator.ts\nfunction registerValidationChecks(services) {\n  const validator = services.validation.TreemapValidator;\n  const registry = services.validation.ValidationRegistry;\n  if (registry) {\n    const checks = {\n      TreemapDoc: validator.checkSingleRoot.bind(validator)\n      // Remove unused validation for TreemapRow\n    };\n    registry.register(checks, validator);\n  }\n}\n__name(registerValidationChecks, \"registerValidationChecks\");\nvar TreemapValidator = class {\n  static {\n    __name(this, \"TreemapValidator\");\n  }\n  /**\n   * Validates that a treemap has only one root node.\n   * A root node is defined as a node that has no indentation.\n   */\n  checkSingleRoot(doc, accept) {\n    let rootNodeIndentation;\n    for (const row of doc.TreemapRows) {\n      if (!row.item) {\n        continue;\n      }\n      if (rootNodeIndentation === void 0 && // Check if this is a root node (no indentation)\n      row.indent === void 0) {\n        rootNodeIndentation = 0;\n      } else if (row.indent === void 0) {\n        accept(\"error\", \"Multiple root nodes are not allowed in a treemap.\", {\n          node: row,\n          property: \"item\"\n        });\n      } else if (rootNodeIndentation !== void 0 && rootNodeIndentation >= parseInt(row.indent, 10)) {\n        accept(\"error\", \"Multiple root nodes are not allowed in a treemap.\", {\n          node: row,\n          property: \"item\"\n        });\n      }\n    }\n  }\n};\n\n// src/language/treemap/module.ts\nvar TreemapModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new TreemapTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new TreemapValueConverter(), \"ValueConverter\")\n  },\n  validation: {\n    TreemapValidator: /* @__PURE__ */ __name(() => new TreemapValidator(), \"TreemapValidator\")\n  }\n};\nfunction createTreemapServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Treemap = inject(\n    createDefaultCoreModule({ shared }),\n    TreemapGeneratedModule,\n    TreemapModule\n  );\n  shared.ServiceRegistry.register(Treemap);\n  registerValidationChecks(Treemap);\n  return { shared, Treemap };\n}\n__name(createTreemapServices, \"createTreemapServices\");\n\nexport {\n  TreemapModule,\n  createTreemapServices\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAiBA,IAAI,sBAAsB,cAAc,4BAA4B;AAAA,EAjBpE,OAiBoE;AAAA;AAAA;AAAA,EAClE,OAAO;AACL,IAAAA,QAAO,MAAM,qBAAqB;AAAA,EACpC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,SAAS,CAAC;AAAA,EACnB;AACF;AAGA,IAAI,gBAAgB;AACpB,IAAI,wBAAwB,cAAc,8BAA8B;AAAA,EA5BxE,OA4BwE;AAAA;AAAA;AAAA,EACtE,OAAO;AACL,IAAAA,QAAO,MAAM,uBAAuB;AAAA,EACtC;AAAA,EACA,mBAAmB,MAAM,OAAO,UAAU;AACxC,QAAI,KAAK,SAAS,WAAW;AAC3B,aAAO,WAAW,MAAM,QAAQ,MAAM,EAAE,CAAC;AAAA,IAC3C,WAAW,KAAK,SAAS,aAAa;AACpC,aAAO,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC;AAAA,IAC5C,WAAW,KAAK,SAAS,WAAW;AAClC,aAAO,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC;AAAA,IAC5C,WAAW,KAAK,SAAS,eAAe;AACtC,aAAO,MAAM;AAAA,IACf,WAAW,KAAK,SAAS,YAAY;AACnC,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,cAAc,KAAK,KAAK;AACtC,UAAI,OAAO;AACT,eAAO;AAAA,UACL,OAAO;AAAA,UACP,WAAW,MAAM,CAAC;AAAA,UAClB,WAAW,MAAM,CAAC,KAAK;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,yBAAyB,UAAU;AAC1C,QAAM,YAAY,SAAS,WAAW;AACtC,QAAM,WAAW,SAAS,WAAW;AACrC,MAAI,UAAU;AACZ,UAAM,SAAS;AAAA,MACb,YAAY,UAAU,gBAAgB,KAAK,SAAS;AAAA;AAAA,IAEtD;AACA,aAAS,SAAS,QAAQ,SAAS;AAAA,EACrC;AACF;AAVS;AAWTA,QAAO,0BAA0B,0BAA0B;AAC3D,IAAI,mBAAmB,MAAM;AAAA,EAvE7B,OAuE6B;AAAA;AAAA;AAAA,EAC3B,OAAO;AACL,IAAAA,QAAO,MAAM,kBAAkB;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,KAAK,QAAQ;AAC3B,QAAI;AACJ,eAAW,OAAO,IAAI,aAAa;AACjC,UAAI,CAAC,IAAI,MAAM;AACb;AAAA,MACF;AACA,UAAI,wBAAwB;AAAA,MAC5B,IAAI,WAAW,QAAQ;AACrB,8BAAsB;AAAA,MACxB,WAAW,IAAI,WAAW,QAAQ;AAChC,eAAO,SAAS,qDAAqD;AAAA,UACnE,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,WAAW,wBAAwB,UAAU,uBAAuB,SAAS,IAAI,QAAQ,EAAE,GAAG;AAC5F,eAAO,SAAS,qDAAqD;AAAA,UACnE,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,gBAAgB;AAAA,EAClB,QAAQ;AAAA,IACN,cAA8B,gBAAAA,QAAO,MAAM,IAAI,oBAAoB,GAAG,cAAc;AAAA,IACpF,gBAAgC,gBAAAA,QAAO,MAAM,IAAI,sBAAsB,GAAG,gBAAgB;AAAA,EAC5F;AAAA,EACA,YAAY;AAAA,IACV,kBAAkC,gBAAAA,QAAO,MAAM,IAAI,iBAAiB,GAAG,kBAAkB;AAAA,EAC3F;AACF;AACA,SAAS,sBAAsB,UAAU,iBAAiB;AACxD,QAAM,SAAS;AAAA,IACb,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,UAAU;AAAA,IACd,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,OAAO;AACvC,2BAAyB,OAAO;AAChC,SAAO,EAAE,QAAQ,QAAQ;AAC3B;AAbS;AAcTA,QAAO,uBAAuB,uBAAuB;", "names": ["__name"]}