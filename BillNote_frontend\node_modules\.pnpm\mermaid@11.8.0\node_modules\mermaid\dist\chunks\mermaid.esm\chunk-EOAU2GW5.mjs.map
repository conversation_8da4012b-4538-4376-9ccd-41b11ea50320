{"version": 3, "sources": ["../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/graph.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nvar DEFAULT_EDGE_NAME = '\\x00';\nvar GRAPH_NODE = '\\x00';\nvar EDGE_KEY_DELIM = '\\x01';\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\nexport class Graph {\n  constructor(opts = {}) {\n    this._isDirected = Object.prototype.hasOwnProperty.call(opts, 'directed')\n      ? opts.directed\n      : true;\n    this._isMultigraph = Object.prototype.hasOwnProperty.call(opts, 'multigraph')\n      ? opts.multigraph\n      : false;\n    this._isCompound = Object.prototype.hasOwnProperty.call(opts, 'compound')\n      ? opts.compound\n      : false;\n\n    // Label for the graph itself\n    this._label = undefined;\n\n    // Defaults to be set when creating a new node\n    this._defaultNodeLabelFn = _.constant(undefined);\n\n    // Defaults to be set when creating a new edge\n    this._defaultEdgeLabelFn = _.constant(undefined);\n\n    // v -> label\n    this._nodes = {};\n\n    if (this._isCompound) {\n      // v -> parent\n      this._parent = {};\n\n      // v -> children\n      this._children = {};\n      this._children[GRAPH_NODE] = {};\n    }\n\n    // v -> edgeObj\n    this._in = {};\n\n    // u -> v -> Number\n    this._preds = {};\n\n    // v -> edgeObj\n    this._out = {};\n\n    // v -> w -> Number\n    this._sucs = {};\n\n    // e -> edgeObj\n    this._edgeObjs = {};\n\n    // e -> label\n    this._edgeLabels = {};\n  }\n  /* === Graph functions ========= */\n  isDirected() {\n    return this._isDirected;\n  }\n  isMultigraph() {\n    return this._isMultigraph;\n  }\n  isCompound() {\n    return this._isCompound;\n  }\n  setGraph(label) {\n    this._label = label;\n    return this;\n  }\n  graph() {\n    return this._label;\n  }\n  /* === Node functions ========== */\n  setDefaultNodeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultNodeLabelFn = newDefault;\n    return this;\n  }\n  nodeCount() {\n    return this._nodeCount;\n  }\n  nodes() {\n    return _.keys(this._nodes);\n  }\n  sources() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._in[v]);\n    });\n  }\n  sinks() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._out[v]);\n    });\n  }\n  setNodes(vs, value) {\n    var args = arguments;\n    var self = this;\n    _.each(vs, function (v) {\n      if (args.length > 1) {\n        self.setNode(v, value);\n      } else {\n        self.setNode(v);\n      }\n    });\n    return this;\n  }\n  setNode(v, value) {\n    if (Object.prototype.hasOwnProperty.call(this._nodes, v)) {\n      if (arguments.length > 1) {\n        this._nodes[v] = value;\n      }\n      return this;\n    }\n\n    // @ts-expect-error\n    this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);\n    if (this._isCompound) {\n      this._parent[v] = GRAPH_NODE;\n      this._children[v] = {};\n      this._children[GRAPH_NODE][v] = true;\n    }\n    this._in[v] = {};\n    this._preds[v] = {};\n    this._out[v] = {};\n    this._sucs[v] = {};\n    ++this._nodeCount;\n    return this;\n  }\n  node(v) {\n    return this._nodes[v];\n  }\n  hasNode(v) {\n    return Object.prototype.hasOwnProperty.call(this._nodes, v);\n  }\n  removeNode(v) {\n    if (Object.prototype.hasOwnProperty.call(this._nodes, v)) {\n      var removeEdge = (e) => this.removeEdge(this._edgeObjs[e]);\n      delete this._nodes[v];\n      if (this._isCompound) {\n        this._removeFromParentsChildList(v);\n        delete this._parent[v];\n        _.each(this.children(v), (child) => {\n          this.setParent(child);\n        });\n        delete this._children[v];\n      }\n      _.each(_.keys(this._in[v]), removeEdge);\n      delete this._in[v];\n      delete this._preds[v];\n      _.each(_.keys(this._out[v]), removeEdge);\n      delete this._out[v];\n      delete this._sucs[v];\n      --this._nodeCount;\n    }\n    return this;\n  }\n  setParent(v, parent) {\n    if (!this._isCompound) {\n      throw new Error('Cannot set parent in a non-compound graph');\n    }\n\n    if (_.isUndefined(parent)) {\n      parent = GRAPH_NODE;\n    } else {\n      // Coerce parent to string\n      parent += '';\n      for (var ancestor = parent; !_.isUndefined(ancestor); ancestor = this.parent(ancestor)) {\n        if (ancestor === v) {\n          throw new Error('Setting ' + parent + ' as parent of ' + v + ' would create a cycle');\n        }\n      }\n\n      this.setNode(parent);\n    }\n\n    this.setNode(v);\n    this._removeFromParentsChildList(v);\n    this._parent[v] = parent;\n    this._children[parent][v] = true;\n    return this;\n  }\n  _removeFromParentsChildList(v) {\n    delete this._children[this._parent[v]][v];\n  }\n  parent(v) {\n    if (this._isCompound) {\n      var parent = this._parent[v];\n      if (parent !== GRAPH_NODE) {\n        return parent;\n      }\n    }\n  }\n  children(v) {\n    if (_.isUndefined(v)) {\n      v = GRAPH_NODE;\n    }\n\n    if (this._isCompound) {\n      var children = this._children[v];\n      if (children) {\n        return _.keys(children);\n      }\n    } else if (v === GRAPH_NODE) {\n      return this.nodes();\n    } else if (this.hasNode(v)) {\n      return [];\n    }\n  }\n  predecessors(v) {\n    var predsV = this._preds[v];\n    if (predsV) {\n      return _.keys(predsV);\n    }\n  }\n  successors(v) {\n    var sucsV = this._sucs[v];\n    if (sucsV) {\n      return _.keys(sucsV);\n    }\n  }\n  neighbors(v) {\n    var preds = this.predecessors(v);\n    if (preds) {\n      return _.union(preds, this.successors(v));\n    }\n  }\n  isLeaf(v) {\n    var neighbors;\n    if (this.isDirected()) {\n      neighbors = this.successors(v);\n    } else {\n      neighbors = this.neighbors(v);\n    }\n    return neighbors.length === 0;\n  }\n  filterNodes(filter) {\n    // @ts-expect-error\n    var copy = new this.constructor({\n      directed: this._isDirected,\n      multigraph: this._isMultigraph,\n      compound: this._isCompound,\n    });\n\n    copy.setGraph(this.graph());\n\n    var self = this;\n    _.each(this._nodes, function (value, v) {\n      if (filter(v)) {\n        copy.setNode(v, value);\n      }\n    });\n\n    _.each(this._edgeObjs, function (e) {\n      // @ts-expect-error\n      if (copy.hasNode(e.v) && copy.hasNode(e.w)) {\n        copy.setEdge(e, self.edge(e));\n      }\n    });\n\n    var parents = {};\n    function findParent(v) {\n      var parent = self.parent(v);\n      if (parent === undefined || copy.hasNode(parent)) {\n        parents[v] = parent;\n        return parent;\n      } else if (parent in parents) {\n        return parents[parent];\n      } else {\n        return findParent(parent);\n      }\n    }\n\n    if (this._isCompound) {\n      _.each(copy.nodes(), function (v) {\n        copy.setParent(v, findParent(v));\n      });\n    }\n\n    return copy;\n  }\n  /* === Edge functions ========== */\n  setDefaultEdgeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultEdgeLabelFn = newDefault;\n    return this;\n  }\n  edgeCount() {\n    return this._edgeCount;\n  }\n  edges() {\n    return _.values(this._edgeObjs);\n  }\n  setPath(vs, value) {\n    var self = this;\n    var args = arguments;\n    _.reduce(vs, function (v, w) {\n      if (args.length > 1) {\n        self.setEdge(v, w, value);\n      } else {\n        self.setEdge(v, w);\n      }\n      return w;\n    });\n    return this;\n  }\n  /*\n   * setEdge(v, w, [value, [name]])\n   * setEdge({ v, w, [name] }, [value])\n   */\n  setEdge() {\n    var v, w, name, value;\n    var valueSpecified = false;\n    var arg0 = arguments[0];\n\n    if (typeof arg0 === 'object' && arg0 !== null && 'v' in arg0) {\n      v = arg0.v;\n      w = arg0.w;\n      name = arg0.name;\n      if (arguments.length === 2) {\n        value = arguments[1];\n        valueSpecified = true;\n      }\n    } else {\n      v = arg0;\n      w = arguments[1];\n      name = arguments[3];\n      if (arguments.length > 2) {\n        value = arguments[2];\n        valueSpecified = true;\n      }\n    }\n\n    v = '' + v;\n    w = '' + w;\n    if (!_.isUndefined(name)) {\n      name = '' + name;\n    }\n\n    var e = edgeArgsToId(this._isDirected, v, w, name);\n    if (Object.prototype.hasOwnProperty.call(this._edgeLabels, e)) {\n      if (valueSpecified) {\n        this._edgeLabels[e] = value;\n      }\n      return this;\n    }\n\n    if (!_.isUndefined(name) && !this._isMultigraph) {\n      throw new Error('Cannot set a named edge when isMultigraph = false');\n    }\n\n    // It didn't exist, so we need to create it.\n    // First ensure the nodes exist.\n    this.setNode(v);\n    this.setNode(w);\n\n    // @ts-expect-error\n    this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);\n\n    var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);\n    // Ensure we add undirected edges in a consistent way.\n    v = edgeObj.v;\n    w = edgeObj.w;\n\n    Object.freeze(edgeObj);\n    this._edgeObjs[e] = edgeObj;\n    incrementOrInitEntry(this._preds[w], v);\n    incrementOrInitEntry(this._sucs[v], w);\n    this._in[w][e] = edgeObj;\n    this._out[v][e] = edgeObj;\n    this._edgeCount++;\n    return this;\n  }\n  edge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return this._edgeLabels[e];\n  }\n  hasEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return Object.prototype.hasOwnProperty.call(this._edgeLabels, e);\n  }\n  removeEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    var edge = this._edgeObjs[e];\n    if (edge) {\n      v = edge.v;\n      w = edge.w;\n      delete this._edgeLabels[e];\n      delete this._edgeObjs[e];\n      decrementOrRemoveEntry(this._preds[w], v);\n      decrementOrRemoveEntry(this._sucs[v], w);\n      delete this._in[w][e];\n      delete this._out[v][e];\n      this._edgeCount--;\n    }\n    return this;\n  }\n  inEdges(v, u) {\n    var inV = this._in[v];\n    if (inV) {\n      var edges = _.values(inV);\n      if (!u) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.v === u;\n      });\n    }\n  }\n  outEdges(v, w) {\n    var outV = this._out[v];\n    if (outV) {\n      var edges = _.values(outV);\n      if (!w) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.w === w;\n      });\n    }\n  }\n  nodeEdges(v, w) {\n    var inEdges = this.inEdges(v, w);\n    if (inEdges) {\n      return inEdges.concat(this.outEdges(v, w));\n    }\n  }\n}\n\n/* Number of nodes in the graph. Should only be changed by the implementation. */\nGraph.prototype._nodeCount = 0;\n\n/* Number of edges in the graph. Should only be changed by the implementation. */\nGraph.prototype._edgeCount = 0;\n\nfunction incrementOrInitEntry(map, k) {\n  if (map[k]) {\n    map[k]++;\n  } else {\n    map[k] = 1;\n  }\n}\n\nfunction decrementOrRemoveEntry(map, k) {\n  if (!--map[k]) {\n    delete map[k];\n  }\n}\n\nfunction edgeArgsToId(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM + (_.isUndefined(name) ? DEFAULT_EDGE_NAME : name);\n}\n\nfunction edgeArgsToObj(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var edgeObj = { v: v, w: w };\n  if (name) {\n    edgeObj.name = name;\n  }\n  return edgeObj;\n}\n\nfunction edgeObjToId(isDirected, edgeObj) {\n  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAEA,IAAI,oBAAoB;AACxB,IAAI,aAAa;AACjB,IAAI,iBAAiB;AAqBd,IAAM,QAAN,MAAY;AAAA,EAzBnB,OAyBmB;AAAA;AAAA;AAAA,EACjB,YAAY,OAAO,CAAC,GAAG;AACrB,SAAK,cAAc,OAAO,UAAU,eAAe,KAAK,MAAM,UAAU,IACpE,KAAK,WACL;AACJ,SAAK,gBAAgB,OAAO,UAAU,eAAe,KAAK,MAAM,YAAY,IACxE,KAAK,aACL;AACJ,SAAK,cAAc,OAAO,UAAU,eAAe,KAAK,MAAM,UAAU,IACpE,KAAK,WACL;AAGJ,SAAK,SAAS;AAGd,SAAK,sBAAwB,iBAAS,MAAS;AAG/C,SAAK,sBAAwB,iBAAS,MAAS;AAG/C,SAAK,SAAS,CAAC;AAEf,QAAI,KAAK,aAAa;AAEpB,WAAK,UAAU,CAAC;AAGhB,WAAK,YAAY,CAAC;AAClB,WAAK,UAAU,UAAU,IAAI,CAAC;AAAA,IAChC;AAGA,SAAK,MAAM,CAAC;AAGZ,SAAK,SAAS,CAAC;AAGf,SAAK,OAAO,CAAC;AAGb,SAAK,QAAQ,CAAC;AAGd,SAAK,YAAY,CAAC;AAGlB,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,SAAS,OAAO;AACd,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,oBAAoB,YAAY;AAC9B,QAAI,CAAG,mBAAW,UAAU,GAAG;AAC7B,mBAAe,iBAAS,UAAU;AAAA,IACpC;AACA,SAAK,sBAAsB;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,QAAQ;AACN,WAAS,aAAK,KAAK,MAAM;AAAA,EAC3B;AAAA,EACA,UAAU;AACR,QAAI,OAAO;AACX,WAAS,eAAO,KAAK,MAAM,GAAG,SAAU,GAAG;AACzC,aAAS,gBAAQ,KAAK,IAAI,CAAC,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,QAAQ;AACN,QAAI,OAAO;AACX,WAAS,eAAO,KAAK,MAAM,GAAG,SAAU,GAAG;AACzC,aAAS,gBAAQ,KAAK,KAAK,CAAC,CAAC;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EACA,SAAS,IAAI,OAAO;AAClB,QAAI,OAAO;AACX,QAAI,OAAO;AACX,IAAE,gBAAK,IAAI,SAAU,GAAG;AACtB,UAAI,KAAK,SAAS,GAAG;AACnB,aAAK,QAAQ,GAAG,KAAK;AAAA,MACvB,OAAO;AACL,aAAK,QAAQ,CAAC;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,GAAG,OAAO;AAChB,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,QAAQ,CAAC,GAAG;AACxD,UAAI,UAAU,SAAS,GAAG;AACxB,aAAK,OAAO,CAAC,IAAI;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAGA,SAAK,OAAO,CAAC,IAAI,UAAU,SAAS,IAAI,QAAQ,KAAK,oBAAoB,CAAC;AAC1E,QAAI,KAAK,aAAa;AACpB,WAAK,QAAQ,CAAC,IAAI;AAClB,WAAK,UAAU,CAAC,IAAI,CAAC;AACrB,WAAK,UAAU,UAAU,EAAE,CAAC,IAAI;AAAA,IAClC;AACA,SAAK,IAAI,CAAC,IAAI,CAAC;AACf,SAAK,OAAO,CAAC,IAAI,CAAC;AAClB,SAAK,KAAK,CAAC,IAAI,CAAC;AAChB,SAAK,MAAM,CAAC,IAAI,CAAC;AACjB,MAAE,KAAK;AACP,WAAO;AAAA,EACT;AAAA,EACA,KAAK,GAAG;AACN,WAAO,KAAK,OAAO,CAAC;AAAA,EACtB;AAAA,EACA,QAAQ,GAAG;AACT,WAAO,OAAO,UAAU,eAAe,KAAK,KAAK,QAAQ,CAAC;AAAA,EAC5D;AAAA,EACA,WAAW,GAAG;AACZ,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,QAAQ,CAAC,GAAG;AACxD,UAAI,aAAa,wBAAC,MAAM,KAAK,WAAW,KAAK,UAAU,CAAC,CAAC,GAAxC;AACjB,aAAO,KAAK,OAAO,CAAC;AACpB,UAAI,KAAK,aAAa;AACpB,aAAK,4BAA4B,CAAC;AAClC,eAAO,KAAK,QAAQ,CAAC;AACrB,QAAE,gBAAK,KAAK,SAAS,CAAC,GAAG,CAAC,UAAU;AAClC,eAAK,UAAU,KAAK;AAAA,QACtB,CAAC;AACD,eAAO,KAAK,UAAU,CAAC;AAAA,MACzB;AACA,MAAE,gBAAO,aAAK,KAAK,IAAI,CAAC,CAAC,GAAG,UAAU;AACtC,aAAO,KAAK,IAAI,CAAC;AACjB,aAAO,KAAK,OAAO,CAAC;AACpB,MAAE,gBAAO,aAAK,KAAK,KAAK,CAAC,CAAC,GAAG,UAAU;AACvC,aAAO,KAAK,KAAK,CAAC;AAClB,aAAO,KAAK,MAAM,CAAC;AACnB,QAAE,KAAK;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,GAAG,QAAQ;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AAEA,QAAM,oBAAY,MAAM,GAAG;AACzB,eAAS;AAAA,IACX,OAAO;AAEL,gBAAU;AACV,eAAS,WAAW,QAAQ,CAAG,oBAAY,QAAQ,GAAG,WAAW,KAAK,OAAO,QAAQ,GAAG;AACtF,YAAI,aAAa,GAAG;AAClB,gBAAM,IAAI,MAAM,aAAa,SAAS,mBAAmB,IAAI,uBAAuB;AAAA,QACtF;AAAA,MACF;AAEA,WAAK,QAAQ,MAAM;AAAA,IACrB;AAEA,SAAK,QAAQ,CAAC;AACd,SAAK,4BAA4B,CAAC;AAClC,SAAK,QAAQ,CAAC,IAAI;AAClB,SAAK,UAAU,MAAM,EAAE,CAAC,IAAI;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,4BAA4B,GAAG;AAC7B,WAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC;AAAA,EAC1C;AAAA,EACA,OAAO,GAAG;AACR,QAAI,KAAK,aAAa;AACpB,UAAI,SAAS,KAAK,QAAQ,CAAC;AAC3B,UAAI,WAAW,YAAY;AACzB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,GAAG;AACV,QAAM,oBAAY,CAAC,GAAG;AACpB,UAAI;AAAA,IACN;AAEA,QAAI,KAAK,aAAa;AACpB,UAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,UAAI,UAAU;AACZ,eAAS,aAAK,QAAQ;AAAA,MACxB;AAAA,IACF,WAAW,MAAM,YAAY;AAC3B,aAAO,KAAK,MAAM;AAAA,IACpB,WAAW,KAAK,QAAQ,CAAC,GAAG;AAC1B,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa,GAAG;AACd,QAAI,SAAS,KAAK,OAAO,CAAC;AAC1B,QAAI,QAAQ;AACV,aAAS,aAAK,MAAM;AAAA,IACtB;AAAA,EACF;AAAA,EACA,WAAW,GAAG;AACZ,QAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,QAAI,OAAO;AACT,aAAS,aAAK,KAAK;AAAA,IACrB;AAAA,EACF;AAAA,EACA,UAAU,GAAG;AACX,QAAI,QAAQ,KAAK,aAAa,CAAC;AAC/B,QAAI,OAAO;AACT,aAAS,cAAM,OAAO,KAAK,WAAW,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO,GAAG;AACR,QAAI;AACJ,QAAI,KAAK,WAAW,GAAG;AACrB,kBAAY,KAAK,WAAW,CAAC;AAAA,IAC/B,OAAO;AACL,kBAAY,KAAK,UAAU,CAAC;AAAA,IAC9B;AACA,WAAO,UAAU,WAAW;AAAA,EAC9B;AAAA,EACA,YAAY,QAAQ;AAElB,QAAI,OAAO,IAAI,KAAK,YAAY;AAAA,MAC9B,UAAU,KAAK;AAAA,MACf,YAAY,KAAK;AAAA,MACjB,UAAU,KAAK;AAAA,IACjB,CAAC;AAED,SAAK,SAAS,KAAK,MAAM,CAAC;AAE1B,QAAI,OAAO;AACX,IAAE,gBAAK,KAAK,QAAQ,SAAU,OAAO,GAAG;AACtC,UAAI,OAAO,CAAC,GAAG;AACb,aAAK,QAAQ,GAAG,KAAK;AAAA,MACvB;AAAA,IACF,CAAC;AAED,IAAE,gBAAK,KAAK,WAAW,SAAU,GAAG;AAElC,UAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC,GAAG;AAC1C,aAAK,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,MAC9B;AAAA,IACF,CAAC;AAED,QAAI,UAAU,CAAC;AACf,aAAS,WAAW,GAAG;AACrB,UAAI,SAAS,KAAK,OAAO,CAAC;AAC1B,UAAI,WAAW,UAAa,KAAK,QAAQ,MAAM,GAAG;AAChD,gBAAQ,CAAC,IAAI;AACb,eAAO;AAAA,MACT,WAAW,UAAU,SAAS;AAC5B,eAAO,QAAQ,MAAM;AAAA,MACvB,OAAO;AACL,eAAO,WAAW,MAAM;AAAA,MAC1B;AAAA,IACF;AAVS;AAYT,QAAI,KAAK,aAAa;AACpB,MAAE,gBAAK,KAAK,MAAM,GAAG,SAAU,GAAG;AAChC,aAAK,UAAU,GAAG,WAAW,CAAC,CAAC;AAAA,MACjC,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,oBAAoB,YAAY;AAC9B,QAAI,CAAG,mBAAW,UAAU,GAAG;AAC7B,mBAAe,iBAAS,UAAU;AAAA,IACpC;AACA,SAAK,sBAAsB;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,QAAQ;AACN,WAAS,eAAO,KAAK,SAAS;AAAA,EAChC;AAAA,EACA,QAAQ,IAAI,OAAO;AACjB,QAAI,OAAO;AACX,QAAI,OAAO;AACX,IAAE,eAAO,IAAI,SAAU,GAAG,GAAG;AAC3B,UAAI,KAAK,SAAS,GAAG;AACnB,aAAK,QAAQ,GAAG,GAAG,KAAK;AAAA,MAC1B,OAAO;AACL,aAAK,QAAQ,GAAG,CAAC;AAAA,MACnB;AACA,aAAO;AAAA,IACT,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,GAAG,GAAG,MAAM;AAChB,QAAI,iBAAiB;AACrB,QAAI,OAAO,UAAU,CAAC;AAEtB,QAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,MAAM;AAC5D,UAAI,KAAK;AACT,UAAI,KAAK;AACT,aAAO,KAAK;AACZ,UAAI,UAAU,WAAW,GAAG;AAC1B,gBAAQ,UAAU,CAAC;AACnB,yBAAiB;AAAA,MACnB;AAAA,IACF,OAAO;AACL,UAAI;AACJ,UAAI,UAAU,CAAC;AACf,aAAO,UAAU,CAAC;AAClB,UAAI,UAAU,SAAS,GAAG;AACxB,gBAAQ,UAAU,CAAC;AACnB,yBAAiB;AAAA,MACnB;AAAA,IACF;AAEA,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,CAAG,oBAAY,IAAI,GAAG;AACxB,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,IAAI,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AACjD,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,aAAa,CAAC,GAAG;AAC7D,UAAI,gBAAgB;AAClB,aAAK,YAAY,CAAC,IAAI;AAAA,MACxB;AACA,aAAO;AAAA,IACT;AAEA,QAAI,CAAG,oBAAY,IAAI,KAAK,CAAC,KAAK,eAAe;AAC/C,YAAM,IAAI,MAAM,mDAAmD;AAAA,IACrE;AAIA,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,CAAC;AAGd,SAAK,YAAY,CAAC,IAAI,iBAAiB,QAAQ,KAAK,oBAAoB,GAAG,GAAG,IAAI;AAElF,QAAI,UAAU,cAAc,KAAK,aAAa,GAAG,GAAG,IAAI;AAExD,QAAI,QAAQ;AACZ,QAAI,QAAQ;AAEZ,WAAO,OAAO,OAAO;AACrB,SAAK,UAAU,CAAC,IAAI;AACpB,yBAAqB,KAAK,OAAO,CAAC,GAAG,CAAC;AACtC,yBAAqB,KAAK,MAAM,CAAC,GAAG,CAAC;AACrC,SAAK,IAAI,CAAC,EAAE,CAAC,IAAI;AACjB,SAAK,KAAK,CAAC,EAAE,CAAC,IAAI;AAClB,SAAK;AACL,WAAO;AAAA,EACT;AAAA,EACA,KAAK,GAAG,GAAG,MAAM;AACf,QAAI,IACF,UAAU,WAAW,IACjB,YAAY,KAAK,aAAa,UAAU,CAAC,CAAC,IAC1C,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AAC/C,WAAO,KAAK,YAAY,CAAC;AAAA,EAC3B;AAAA,EACA,QAAQ,GAAG,GAAG,MAAM;AAClB,QAAI,IACF,UAAU,WAAW,IACjB,YAAY,KAAK,aAAa,UAAU,CAAC,CAAC,IAC1C,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AAC/C,WAAO,OAAO,UAAU,eAAe,KAAK,KAAK,aAAa,CAAC;AAAA,EACjE;AAAA,EACA,WAAW,GAAG,GAAG,MAAM;AACrB,QAAI,IACF,UAAU,WAAW,IACjB,YAAY,KAAK,aAAa,UAAU,CAAC,CAAC,IAC1C,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AAC/C,QAAI,OAAO,KAAK,UAAU,CAAC;AAC3B,QAAI,MAAM;AACR,UAAI,KAAK;AACT,UAAI,KAAK;AACT,aAAO,KAAK,YAAY,CAAC;AACzB,aAAO,KAAK,UAAU,CAAC;AACvB,6BAAuB,KAAK,OAAO,CAAC,GAAG,CAAC;AACxC,6BAAuB,KAAK,MAAM,CAAC,GAAG,CAAC;AACvC,aAAO,KAAK,IAAI,CAAC,EAAE,CAAC;AACpB,aAAO,KAAK,KAAK,CAAC,EAAE,CAAC;AACrB,WAAK;AAAA,IACP;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,GAAG,GAAG;AACZ,QAAI,MAAM,KAAK,IAAI,CAAC;AACpB,QAAI,KAAK;AACP,UAAI,QAAU,eAAO,GAAG;AACxB,UAAI,CAAC,GAAG;AACN,eAAO;AAAA,MACT;AACA,aAAS,eAAO,OAAO,SAAU,MAAM;AACrC,eAAO,KAAK,MAAM;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,SAAS,GAAG,GAAG;AACb,QAAI,OAAO,KAAK,KAAK,CAAC;AACtB,QAAI,MAAM;AACR,UAAI,QAAU,eAAO,IAAI;AACzB,UAAI,CAAC,GAAG;AACN,eAAO;AAAA,MACT;AACA,aAAS,eAAO,OAAO,SAAU,MAAM;AACrC,eAAO,KAAK,MAAM;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,UAAU,GAAG,GAAG;AACd,QAAI,UAAU,KAAK,QAAQ,GAAG,CAAC;AAC/B,QAAI,SAAS;AACX,aAAO,QAAQ,OAAO,KAAK,SAAS,GAAG,CAAC,CAAC;AAAA,IAC3C;AAAA,EACF;AACF;AAGA,MAAM,UAAU,aAAa;AAG7B,MAAM,UAAU,aAAa;AAE7B,SAAS,qBAAqB,KAAK,GAAG;AACpC,MAAI,IAAI,CAAC,GAAG;AACV,QAAI,CAAC;AAAA,EACP,OAAO;AACL,QAAI,CAAC,IAAI;AAAA,EACX;AACF;AANS;AAQT,SAAS,uBAAuB,KAAK,GAAG;AACtC,MAAI,CAAC,EAAE,IAAI,CAAC,GAAG;AACb,WAAO,IAAI,CAAC;AAAA,EACd;AACF;AAJS;AAMT,SAAS,aAAa,YAAY,IAAI,IAAI,MAAM;AAC9C,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,MAAI,CAAC,cAAc,IAAI,GAAG;AACxB,QAAI,MAAM;AACV,QAAI;AACJ,QAAI;AAAA,EACN;AACA,SAAO,IAAI,iBAAiB,IAAI,kBAAoB,oBAAY,IAAI,IAAI,oBAAoB;AAC9F;AATS;AAWT,SAAS,cAAc,YAAY,IAAI,IAAI,MAAM;AAC/C,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,MAAI,CAAC,cAAc,IAAI,GAAG;AACxB,QAAI,MAAM;AACV,QAAI;AACJ,QAAI;AAAA,EACN;AACA,MAAI,UAAU,EAAE,GAAM,EAAK;AAC3B,MAAI,MAAM;AACR,YAAQ,OAAO;AAAA,EACjB;AACA,SAAO;AACT;AAbS;AAeT,SAAS,YAAY,YAAY,SAAS;AACxC,SAAO,aAAa,YAAY,QAAQ,GAAG,QAAQ,GAAG,QAAQ,IAAI;AACpE;AAFS;", "names": []}