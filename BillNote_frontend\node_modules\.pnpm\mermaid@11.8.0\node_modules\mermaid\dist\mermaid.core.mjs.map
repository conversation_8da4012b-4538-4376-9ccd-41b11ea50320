{"version": 3, "sources": ["../src/mermaid.ts", "../src/diagrams/c4/c4Detector.ts", "../src/diagrams/flowchart/flowDetector.ts", "../src/diagrams/flowchart/flowDetector-v2.ts", "../src/diagrams/er/erDetector.ts", "../src/diagrams/git/gitGraphDetector.ts", "../src/diagrams/gantt/ganttDetector.ts", "../src/diagrams/info/infoDetector.ts", "../src/diagrams/pie/pieDetector.ts", "../src/diagrams/quadrant-chart/quadrantDetector.ts", "../src/diagrams/xychart/xychartDetector.ts", "../src/diagrams/requirement/requirementDetector.ts", "../src/diagrams/sequence/sequenceDetector.ts", "../src/diagrams/class/classDetector.ts", "../src/diagrams/class/classDetector-V2.ts", "../src/diagrams/state/stateDetector.ts", "../src/diagrams/state/stateDetector-V2.ts", "../src/diagrams/user-journey/journeyDetector.ts", "../src/diagrams/error/errorRenderer.ts", "../src/diagrams/error/errorDiagram.ts", "../src/diagrams/flowchart/elk/detector.ts", "../src/diagrams/timeline/detector.ts", "../src/diagrams/mindmap/detector.ts", "../src/diagrams/kanban/detector.ts", "../src/diagrams/sankey/sankeyDetector.ts", "../src/diagrams/packet/detector.ts", "../src/diagrams/radar/detector.ts", "../src/diagrams/block/blockDetector.ts", "../src/diagrams/architecture/architectureDetector.ts", "../src/diagrams/treemap/detector.ts", "../src/diagram-api/diagram-orchestration.ts", "../src/diagram-api/loadDiagram.ts", "../src/mermaidAPI.ts", "../src/accessibility.ts", "../src/Diagram.ts", "../src/interactionDb.ts", "../src/diagram-api/comments.ts", "../src/diagram-api/frontmatter.ts", "../src/preprocess.ts", "../src/utils/base64.ts"], "sourcesContent": ["/**\n * Web page integration module for the mermaid framework. It uses the mermaidAPI for mermaid\n * functionality and to render the diagrams to svg code!\n */\nimport { registerIconPacks } from './rendering-util/icons.js';\nimport { dedent } from 'ts-dedent';\nimport type { MermaidConfig } from './config.type.js';\nimport { detectType, registerLazyLoadedDiagrams } from './diagram-api/detectType.js';\nimport { addDiagrams } from './diagram-api/diagram-orchestration.js';\nimport { loadRegisteredDiagrams } from './diagram-api/loadDiagram.js';\nimport type { ExternalDiagramDefinition, SVG, SVGGroup } from './diagram-api/types.js';\nimport type { ParseErrorFunction } from './Diagram.js';\nimport type { UnknownDiagramError } from './errors.js';\nimport type { InternalHelpers } from './internals.js';\nimport { log } from './logger.js';\nimport { mermaidAPI } from './mermaidAPI.js';\nimport type { LayoutLoaderDefinition, RenderOptions } from './rendering-util/render.js';\nimport { registerLayoutLoaders } from './rendering-util/render.js';\nimport type { LayoutData } from './rendering-util/types.js';\nimport type { ParseOptions, ParseResult, RenderResult } from './types.js';\nimport type { DetailedError } from './utils.js';\nimport utils, { isDetailedError } from './utils.js';\n\nexport type {\n  DetailedError,\n  ExternalDiagramDefinition,\n  InternalHelpers,\n  LayoutData,\n  LayoutLoaderDefinition,\n  MermaidConfig,\n  ParseErrorFunction,\n  ParseOptions,\n  ParseResult,\n  RenderOptions,\n  RenderResult,\n  SVG,\n  SVGGroup,\n  UnknownDiagramError,\n};\n\nexport interface RunOptions {\n  /**\n   * The query selector to use when finding elements to render. Default: `\".mermaid\"`.\n   */\n  querySelector?: string;\n  /**\n   * The nodes to render. If this is set, `querySelector` will be ignored.\n   */\n  nodes?: ArrayLike<HTMLElement>;\n  /**\n   * A callback to call after each diagram is rendered.\n   */\n  postRenderCallback?: (id: string) => unknown;\n  /**\n   * If `true`, errors will be logged to the console, but not thrown. Default: `false`\n   */\n  suppressErrors?: boolean;\n}\n\nconst handleError = (error: unknown, errors: DetailedError[], parseError?: ParseErrorFunction) => {\n  log.warn(error);\n  if (isDetailedError(error)) {\n    // handle case where error string and hash were\n    // wrapped in object like`const error = { str, hash };`\n    if (parseError) {\n      parseError(error.str, error.hash);\n    }\n    errors.push({ ...error, message: error.str, error });\n  } else {\n    // assume it is just error string and pass it on\n    if (parseError) {\n      parseError(error);\n    }\n    if (error instanceof Error) {\n      errors.push({\n        str: error.message,\n        message: error.message,\n        hash: error.name,\n        error,\n      });\n    }\n  }\n};\n\n/**\n * ## run\n *\n * Function that goes through the document to find the chart definitions in there and render them.\n *\n * The function tags the processed attributes with the attribute data-processed and ignores found\n * elements with the attribute already set. This way the init function can be triggered several\n * times.\n *\n * ```mermaid\n * graph LR;\n *  a(Find elements)-->b{Processed}\n *  b-->|Yes|c(Leave element)\n *  b-->|No |d(Transform)\n * ```\n *\n * Renders the mermaid diagrams\n *\n * @param options - Optional runtime configs\n */\nconst run = async function (\n  options: RunOptions = {\n    querySelector: '.mermaid',\n  }\n) {\n  try {\n    await runThrowsErrors(options);\n  } catch (e) {\n    if (isDetailedError(e)) {\n      log.error(e.str);\n    }\n    if (mermaid.parseError) {\n      mermaid.parseError(e as string);\n    }\n    if (!options.suppressErrors) {\n      log.error('Use the suppressErrors option to suppress these errors');\n      throw e;\n    }\n  }\n};\n\nconst runThrowsErrors = async function (\n  { postRenderCallback, querySelector, nodes }: Omit<RunOptions, 'suppressErrors'> = {\n    querySelector: '.mermaid',\n  }\n) {\n  const conf = mermaidAPI.getConfig();\n\n  log.debug(`${!postRenderCallback ? 'No ' : ''}Callback function found`);\n\n  let nodesToProcess: ArrayLike<HTMLElement>;\n  if (nodes) {\n    nodesToProcess = nodes;\n  } else if (querySelector) {\n    nodesToProcess = document.querySelectorAll(querySelector);\n  } else {\n    throw new Error('Nodes and querySelector are both undefined');\n  }\n\n  log.debug(`Found ${nodesToProcess.length} diagrams`);\n  if (conf?.startOnLoad !== undefined) {\n    log.debug('Start On Load: ' + conf?.startOnLoad);\n    mermaidAPI.updateSiteConfig({ startOnLoad: conf?.startOnLoad });\n  }\n\n  // generate the id of the diagram\n  const idGenerator = new utils.InitIDGenerator(conf.deterministicIds, conf.deterministicIDSeed);\n\n  let txt: string;\n  const errors: DetailedError[] = [];\n\n  // element is the current div with mermaid class\n  // eslint-disable-next-line unicorn/prefer-spread\n  for (const element of Array.from(nodesToProcess)) {\n    log.info('Rendering diagram: ' + element.id);\n    /*! Check if previously processed */\n    if (element.getAttribute('data-processed')) {\n      continue;\n    }\n    element.setAttribute('data-processed', 'true');\n\n    const id = `mermaid-${idGenerator.next()}`;\n\n    // Fetch the graph definition including tags\n    txt = element.innerHTML;\n\n    // transforms the html to pure text\n    txt = dedent(utils.entityDecode(txt)) // removes indentation, required for YAML parsing\n      .trim()\n      .replace(/<br\\s*\\/?>/gi, '<br/>');\n\n    const init = utils.detectInit(txt);\n    if (init) {\n      log.debug('Detected early reinit: ', init);\n    }\n    try {\n      const { svg, bindFunctions } = await render(id, txt, element);\n      element.innerHTML = svg;\n      if (postRenderCallback) {\n        await postRenderCallback(id);\n      }\n      if (bindFunctions) {\n        bindFunctions(element);\n      }\n    } catch (error) {\n      handleError(error, errors, mermaid.parseError);\n    }\n  }\n  if (errors.length > 0) {\n    // TODO: We should be throwing an error object.\n    throw errors[0];\n  }\n};\n\n/**\n * Used to set configurations for mermaid.\n * This function should be called before the run function.\n * @param config - Configuration object for mermaid.\n */\n\nconst initialize = function (config: MermaidConfig) {\n  mermaidAPI.initialize(config);\n};\n\n/**\n * ## init\n *\n * @deprecated Use {@link initialize} and {@link run} instead.\n *\n * Renders the mermaid diagrams\n *\n * @param config - **Deprecated**, please set configuration in {@link initialize}.\n * @param nodes - **Default**: `.mermaid`. One of the following:\n * - A DOM Node\n * - An array of DOM nodes (as would come from a jQuery selector)\n * - A W3C selector, a la `.mermaid`\n * @param callback - Called once for each rendered diagram's id.\n */\nconst init = async function (\n  config?: MermaidConfig,\n  nodes?: string | HTMLElement | NodeListOf<HTMLElement>,\n  callback?: (id: string) => unknown\n) {\n  log.warn('mermaid.init is deprecated. Please use run instead.');\n  if (config) {\n    initialize(config);\n  }\n  const runOptions: RunOptions = { postRenderCallback: callback, querySelector: '.mermaid' };\n  if (typeof nodes === 'string') {\n    runOptions.querySelector = nodes;\n  } else if (nodes) {\n    if (nodes instanceof HTMLElement) {\n      runOptions.nodes = [nodes];\n    } else {\n      runOptions.nodes = nodes;\n    }\n  }\n  await run(runOptions);\n};\n\n/**\n * Used to register external diagram types.\n * @param diagrams - Array of {@link ExternalDiagramDefinition}.\n * @param opts - If opts.lazyLoad is false, the diagrams will be loaded immediately.\n */\nconst registerExternalDiagrams = async (\n  diagrams: ExternalDiagramDefinition[],\n  {\n    lazyLoad = true,\n  }: {\n    lazyLoad?: boolean;\n  } = {}\n) => {\n  addDiagrams();\n  registerLazyLoadedDiagrams(...diagrams);\n  if (lazyLoad === false) {\n    await loadRegisteredDiagrams();\n  }\n};\n\n/**\n * ##contentLoaded Callback function that is called when page is loaded. This functions fetches\n * configuration for mermaid rendering and calls init for rendering the mermaid diagrams on the\n * page.\n */\nconst contentLoaded = function () {\n  if (mermaid.startOnLoad) {\n    const { startOnLoad } = mermaidAPI.getConfig();\n    if (startOnLoad) {\n      mermaid.run().catch((err) => log.error('Mermaid failed to initialize', err));\n    }\n  }\n};\n\nif (typeof document !== 'undefined') {\n  /*!\n   * Wait for document loaded before starting the execution\n   */\n  window.addEventListener('load', contentLoaded, false);\n}\n\n/**\n * ## setParseErrorHandler  Alternative to directly setting parseError using:\n *\n * ```js\n * mermaid.parseError = function(err,hash) {\n *   forExampleDisplayErrorInGui(err);  // do something with the error\n * };\n * ```\n *\n * This is provided for environments where the mermaid object can't directly have a new member added\n * to it (eg. dart interop wrapper). (Initially there is no parseError member of mermaid).\n *\n * @param parseErrorHandler - New parseError() callback.\n */\nconst setParseErrorHandler = function (parseErrorHandler: (err: any, hash: any) => void) {\n  mermaid.parseError = parseErrorHandler;\n};\n\nconst executionQueue: (() => Promise<unknown>)[] = [];\nlet executionQueueRunning = false;\nconst executeQueue = async () => {\n  if (executionQueueRunning) {\n    return;\n  }\n  executionQueueRunning = true;\n  while (executionQueue.length > 0) {\n    const f = executionQueue.shift();\n    if (f) {\n      try {\n        await f();\n      } catch (e) {\n        log.error('Error executing queue', e);\n      }\n    }\n  }\n  executionQueueRunning = false;\n};\n\n/**\n * Parse the text and validate the syntax.\n * @param text - The mermaid diagram definition.\n * @param parseOptions - Options for parsing. @see {@link ParseOptions}\n * @returns If valid, {@link ParseResult} otherwise `false` if parseOptions.suppressErrors is `true`.\n * @throws Error if the diagram is invalid and parseOptions.suppressErrors is false or not set.\n *\n * @example\n * ```js\n * console.log(await mermaid.parse('flowchart \\n a --> b'));\n * // { diagramType: 'flowchart-v2' }\n * console.log(await mermaid.parse('wrong \\n a --> b', { suppressErrors: true }));\n * // false\n * console.log(await mermaid.parse('wrong \\n a --> b', { suppressErrors: false }));\n * // throws Error\n * console.log(await mermaid.parse('wrong \\n a --> b'));\n * // throws Error\n * ```\n */\nconst parse: typeof mermaidAPI.parse = async (text, parseOptions) => {\n  return new Promise((resolve, reject) => {\n    // This promise will resolve when the render call is done.\n    // It will be queued first and will be executed when it is first in line\n    const performCall = () =>\n      new Promise((res, rej) => {\n        mermaidAPI.parse(text, parseOptions).then(\n          (r) => {\n            // This resolves for the promise for the queue handling\n            res(r);\n            // This fulfills the promise sent to the value back to the original caller\n            resolve(r);\n          },\n          (e) => {\n            log.error('Error parsing', e);\n            mermaid.parseError?.(e);\n            rej(e);\n            reject(e);\n          }\n        );\n      });\n    executionQueue.push(performCall);\n    executeQueue().catch(reject);\n  });\n};\n\n/**\n * Function that renders an SVG with a graph from a chart definition. Usage example below.\n *\n * ```javascript\n *  element = document.querySelector('#graphDiv');\n *  const graphDefinition = 'graph TB\\na-->b';\n *  const { svg, bindFunctions } = await mermaid.render('graphDiv', graphDefinition);\n *  element.innerHTML = svg;\n *  bindFunctions?.(element);\n * ```\n *\n * @remarks\n * Multiple calls to this function will be enqueued to run serially.\n *\n * @param id - The id for the SVG element (the element to be rendered)\n * @param text - The text for the graph definition\n * @param container - HTML element where the svg will be inserted. (Is usually element with the .mermaid class)\n *   If no svgContainingElement is provided then the SVG element will be appended to the body.\n *    Selector to element in which a div with the graph temporarily will be\n *   inserted. If one is provided a hidden div will be inserted in the body of the page instead. The\n *   element will be removed when rendering is completed.\n * @returns Returns the SVG Definition and BindFunctions.\n */\nconst render: typeof mermaidAPI.render = (id, text, container) => {\n  return new Promise((resolve, reject) => {\n    // This promise will resolve when the mermaidAPI.render call is done.\n    // It will be queued first and will be executed when it is first in line\n    const performCall = () =>\n      new Promise((res, rej) => {\n        mermaidAPI.render(id, text, container).then(\n          (r) => {\n            // This resolves for the promise for the queue handling\n            res(r);\n            // This fulfills the promise sent to the value back to the original caller\n            resolve(r);\n          },\n          (e) => {\n            log.error('Error parsing', e);\n            mermaid.parseError?.(e);\n            rej(e);\n            reject(e);\n          }\n        );\n      });\n    executionQueue.push(performCall);\n    executeQueue().catch(reject);\n  });\n};\n\nexport interface Mermaid {\n  startOnLoad: boolean;\n  parseError?: ParseErrorFunction;\n  /**\n   * @deprecated Use {@link parse} and {@link render} instead. Please [open a discussion](https://github.com/mermaid-js/mermaid/discussions) if your use case does not fit the new API.\n   * @internal\n   */\n  mermaidAPI: typeof mermaidAPI;\n  parse: typeof parse;\n  render: typeof render;\n  /**\n   * @deprecated Use {@link initialize} and {@link run} instead.\n   */\n  init: typeof init;\n  run: typeof run;\n  registerLayoutLoaders: typeof registerLayoutLoaders;\n  registerExternalDiagrams: typeof registerExternalDiagrams;\n  initialize: typeof initialize;\n  contentLoaded: typeof contentLoaded;\n  setParseErrorHandler: typeof setParseErrorHandler;\n  detectType: typeof detectType;\n  registerIconPacks: typeof registerIconPacks;\n}\n\nconst mermaid: Mermaid = {\n  startOnLoad: true,\n  mermaidAPI,\n  parse,\n  render,\n  init,\n  run,\n  registerExternalDiagrams,\n  registerLayoutLoaders,\n  initialize,\n  parseError: undefined,\n  contentLoaded,\n  setParseErrorHandler,\n  detectType,\n  registerIconPacks,\n};\n\nexport default mermaid;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'c4';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./c4Diagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'flowchart';\n\nconst detector: DiagramDetector = (txt, config) => {\n  // If we have conferred to only use new flow charts this function should always return false\n  // as in not signalling true for a legacy flowchart\n  if (\n    config?.flowchart?.defaultRenderer === 'dagre-wrapper' ||\n    config?.flowchart?.defaultRenderer === 'elk'\n  ) {\n    return false;\n  }\n  return /^\\s*graph/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./flowDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'flowchart-v2';\n\nconst detector: DiagramDetector = (txt, config) => {\n  if (config?.flowchart?.defaultRenderer === 'dagre-d3') {\n    return false;\n  }\n\n  if (config?.flowchart?.defaultRenderer === 'elk') {\n    config.layout = 'elk';\n  }\n\n  // If we have configured to use dagre-wrapper then we should return true in this function for graph code thus making it use the new flowchart diagram\n  if (/^\\s*graph/.test(txt) && config?.flowchart?.defaultRenderer === 'dagre-wrapper') {\n    return true;\n  }\n  return /^\\s*flowchart/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./flowDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'er';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*erDiagram/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./erDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type { DiagramDetector, DiagramLoader } from '../../diagram-api/types.js';\nimport type { ExternalDiagramDefinition } from '../../diagram-api/types.js';\n\nconst id = 'gitGraph';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*gitGraph/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./gitGraphDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'gantt';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*gantt/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./ganttDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'info';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*info/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./infoDiagram.js');\n  return { id, diagram };\n};\n\nexport const info: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'pie';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*pie/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./pieDiagram.js');\n  return { id, diagram };\n};\n\nexport const pie: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'quadrantChart';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*quadrantChart/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./quadrantDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'xychart';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*xychart-beta/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./xychartDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'requirement';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*requirement(Diagram)?/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./requirementDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'sequence';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*sequenceDiagram/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./sequenceDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'class';\n\nconst detector: DiagramDetector = (txt, config) => {\n  // If we have configured to use dagre-wrapper then we should never return true in this function\n  if (config?.class?.defaultRenderer === 'dagre-wrapper') {\n    return false;\n  }\n  // We have not opted to use the new renderer so we should return true if we detect a class diagram\n  return /^\\s*classDiagram/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./classDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'classDiagram';\n\nconst detector: DiagramDetector = (txt, config) => {\n  // If we have configured to use dagre-wrapper then we should return true in this function for classDiagram code thus making it use the new class diagram\n  if (/^\\s*classDiagram/.test(txt) && config?.class?.defaultRenderer === 'dagre-wrapper') {\n    return true;\n  }\n  // We have not opted to use the new renderer so we should return true if we detect a class diagram\n  return /^\\s*classDiagram-v2/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./classDiagram-v2.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'state';\n\nconst detector: DiagramDetector = (txt, config) => {\n  // If we have confirmed to only use new state diagrams this function should always return false\n  // as in not signalling true for a legacy state diagram\n  if (config?.state?.defaultRenderer === 'dagre-wrapper') {\n    return false;\n  }\n  return /^\\s*stateDiagram/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./stateDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'stateDiagram';\n\nconst detector: DiagramDetector = (txt, config) => {\n  if (/^\\s*stateDiagram-v2/.test(txt)) {\n    return true;\n  }\n  if (/^\\s*stateDiagram/.test(txt) && config?.state?.defaultRenderer === 'dagre-wrapper') {\n    return true;\n  }\n  return false;\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./stateDiagram-v2.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'journey';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*journey/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./journeyDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type { SVG, SVGGroup } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\n/**\n * Draws an info picture in the tag with id: id based on the graph definition in text.\n *\n * @param _text - Mermaid graph definition.\n * @param id - The text for the error\n * @param version - The version\n */\nexport const draw = (_text: string, id: string, version: string) => {\n  log.debug('rendering svg for syntax error\\n');\n  const svg: SVG = selectSvgElement(id);\n  const g: SVGGroup = svg.append('g');\n\n  svg.attr('viewBox', '0 0 2412 512');\n  configureSvgSize(svg, 100, 512, true);\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z'\n    );\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z'\n    );\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z'\n    );\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z'\n    );\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z'\n    );\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z'\n    );\n\n  g.append('text') // text label for the x axis\n    .attr('class', 'error-text')\n    .attr('x', 1440)\n    .attr('y', 250)\n    .attr('font-size', '150px')\n    .style('text-anchor', 'middle')\n    .text('Syntax error in text');\n  g.append('text') // text label for the x axis\n    .attr('class', 'error-text')\n    .attr('x', 1250)\n    .attr('y', 400)\n    .attr('font-size', '100px')\n    .style('text-anchor', 'middle')\n    .text(`mermaid version ${version}`);\n};\n\nexport const renderer = { draw };\n\nexport default renderer;\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { renderer } from './errorRenderer.js';\n\nconst diagram: DiagramDefinition = {\n  db: {},\n  renderer,\n  parser: {\n    parse: (): void => {\n      return;\n    },\n  },\n};\n\nexport default diagram;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../../diagram-api/types.js';\n\nconst id = 'flowchart-elk';\n\nconst detector: DiagramDetector = (txt, config = {}): boolean => {\n  if (\n    // If diagram explicitly states flowchart-elk\n    /^\\s*flowchart-elk/.test(txt) ||\n    // If a flowchart/graph diagram has their default renderer set to elk\n    (/^\\s*flowchart|graph/.test(txt) && config?.flowchart?.defaultRenderer === 'elk')\n  ) {\n    config.layout = 'elk';\n    return true;\n  }\n  return false;\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('../flowDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'timeline';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*timeline/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./timeline-definition.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\nconst id = 'mindmap';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*mindmap/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./mindmap-definition.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\nconst id = 'kanban';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*kanban/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./kanban-definition.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type { DiagramDetector, ExternalDiagramDefinition } from '../../diagram-api/types.js';\n\nconst id = 'sankey';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*sankey-beta/.test(txt);\n};\n\nconst loader = async () => {\n  const { diagram } = await import('./sankeyDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'packet';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*packet-beta/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./diagram.js');\n  return { id, diagram };\n};\n\nexport const packet: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'radar';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*radar-beta/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./diagram.js');\n  return { id, diagram };\n};\n\nexport const radar: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n", "import type { DiagramDetector, ExternalDiagramDefinition } from '../../diagram-api/types.js';\n\nconst id = 'block';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*block-beta/.test(txt);\n};\n\nconst loader = async () => {\n  const { diagram } = await import('./blockDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'architecture';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*architecture/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./architectureDiagram.js');\n  return { id, diagram };\n};\n\nconst architecture: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default architecture;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'treemap';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*treemap/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./diagram.js');\n  return { id, diagram };\n};\n\nexport const treemap: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n", "import c4 from '../diagrams/c4/c4Detector.js';\nimport flowchart from '../diagrams/flowchart/flowDetector.js';\nimport flowchartV2 from '../diagrams/flowchart/flowDetector-v2.js';\nimport er from '../diagrams/er/erDetector.js';\nimport git from '../diagrams/git/gitGraphDetector.js';\nimport gantt from '../diagrams/gantt/ganttDetector.js';\nimport { info } from '../diagrams/info/infoDetector.js';\nimport { pie } from '../diagrams/pie/pieDetector.js';\nimport quadrantChart from '../diagrams/quadrant-chart/quadrantDetector.js';\nimport xychart from '../diagrams/xychart/xychartDetector.js';\nimport requirement from '../diagrams/requirement/requirementDetector.js';\nimport sequence from '../diagrams/sequence/sequenceDetector.js';\nimport classDiagram from '../diagrams/class/classDetector.js';\nimport classDiagramV2 from '../diagrams/class/classDetector-V2.js';\nimport state from '../diagrams/state/stateDetector.js';\nimport stateV2 from '../diagrams/state/stateDetector-V2.js';\nimport journey from '../diagrams/user-journey/journeyDetector.js';\nimport errorDiagram from '../diagrams/error/errorDiagram.js';\nimport flowchartElk from '../diagrams/flowchart/elk/detector.js';\nimport timeline from '../diagrams/timeline/detector.js';\nimport mindmap from '../diagrams/mindmap/detector.js';\nimport kanban from '../diagrams/kanban/detector.js';\nimport sankey from '../diagrams/sankey/sankeyDetector.js';\nimport { packet } from '../diagrams/packet/detector.js';\nimport { radar } from '../diagrams/radar/detector.js';\nimport block from '../diagrams/block/blockDetector.js';\nimport architecture from '../diagrams/architecture/architectureDetector.js';\nimport { registerLazyLoadedDiagrams } from './detectType.js';\nimport { registerDiagram } from './diagramAPI.js';\nimport { treemap } from '../diagrams/treemap/detector.js';\nimport '../type.d.ts';\n\nlet hasLoadedDiagrams = false;\nexport const addDiagrams = () => {\n  if (hasLoadedDiagrams) {\n    return;\n  }\n  // This is added here to avoid race-conditions.\n  // We could optimize the loading logic somehow.\n  hasLoadedDiagrams = true;\n  registerDiagram('error', errorDiagram, (text) => {\n    return text.toLowerCase().trim() === 'error';\n  });\n  registerDiagram(\n    '---',\n    // --- diagram type may appear if YAML front-matter is not parsed correctly\n    {\n      db: {\n        clear: () => {\n          // Quite ok, clear needs to be there for --- to work as a regular diagram\n        },\n      },\n      styles: {}, // should never be used\n      renderer: {\n        draw: () => {\n          // should never be used\n        },\n      },\n      parser: {\n        parse: () => {\n          throw new Error(\n            'Diagrams beginning with --- are not valid. ' +\n              'If you were trying to use a YAML front-matter, please ensure that ' +\n              \"you've correctly opened and closed the YAML front-matter with un-indented `---` blocks\"\n          );\n        },\n      },\n      init: () => null, // no op\n    },\n    (text) => {\n      return text.toLowerCase().trimStart().startsWith('---');\n    }\n  );\n\n  if (includeLargeFeatures) {\n    registerLazyLoadedDiagrams(flowchartElk, mindmap, architecture);\n  }\n\n  // Ordering of detectors is important. The first one to return true will be used.\n  registerLazyLoadedDiagrams(\n    c4,\n    kanban,\n    classDiagramV2,\n    classDiagram,\n    er,\n    gantt,\n    info,\n    pie,\n    requirement,\n    sequence,\n    flowchartV2,\n    flowchart,\n    timeline,\n    git,\n    stateV2,\n    state,\n    journey,\n    quadrantChart,\n    sankey,\n    packet,\n    xychart,\n    block,\n    radar,\n    treemap\n  );\n};\n", "import { log } from '../logger.js';\nimport { detectors } from './detectType.js';\nimport { getDiagram, registerDiagram } from './diagramAPI.js';\n\nexport const loadRegisteredDiagrams = async () => {\n  log.debug(`Loading registered diagrams`);\n  // Load all lazy loaded diagrams in parallel\n  const results = await Promise.allSettled(\n    Object.entries(detectors).map(async ([key, { detector, loader }]) => {\n      if (loader) {\n        try {\n          getDiagram(key);\n        } catch {\n          try {\n            // Register diagram if it is not already registered\n            const { diagram, id } = await loader();\n            registerDiagram(id, diagram, detector);\n          } catch (err) {\n            // Remove failed diagram from detectors\n            log.error(`Failed to load external diagram with key ${key}. Removing from detectors.`);\n            delete detectors[key];\n            throw err;\n          }\n        }\n      }\n    })\n  );\n  const failed = results.filter((result) => result.status === 'rejected');\n  if (failed.length > 0) {\n    log.error(`Failed to load ${failed.length} external diagrams`);\n    for (const res of failed) {\n      log.error(res);\n    }\n    throw new Error(`Failed to load ${failed.length} external diagrams`);\n  }\n};\n", "/**\n * This file contains functions that are used internally by mermaid\n * and is not intended to be used by the end user.\n */\n// @ts-ignore TODO: Investigate D3 issue\nimport { select } from 'd3';\nimport { compile, serialize, stringify } from 'stylis';\nimport DOMPurify from 'dompurify';\nimport isEmpty from 'lodash-es/isEmpty.js';\nimport packageJson from '../package.json' assert { type: 'json' };\nimport { addSVGa11yTitleDescription, setA11yDiagramInfo } from './accessibility.js';\nimport assignWithDepth from './assignWithDepth.js';\nimport * as configApi from './config.js';\nimport type { MermaidConfig } from './config.type.js';\nimport { addDiagrams } from './diagram-api/diagram-orchestration.js';\nimport type { DiagramMetadata, DiagramStyleClassDef } from './diagram-api/types.js';\nimport { Diagram } from './Diagram.js';\nimport { evaluate } from './diagrams/common/common.js';\nimport errorRenderer from './diagrams/error/errorRenderer.js';\nimport { attachFunctions } from './interactionDb.js';\nimport { log, setLogLevel } from './logger.js';\nimport { preprocessDiagram } from './preprocess.js';\nimport getStyles from './styles.js';\nimport theme from './themes/index.js';\nimport type { D3Element, ParseOptions, ParseResult, RenderResult } from './types.js';\nimport { decodeEntities } from './utils.js';\nimport { toBase64 } from './utils/base64.js';\n\nconst MAX_TEXTLENGTH = 50_000;\nconst MAX_TEXTLENGTH_EXCEEDED_MSG =\n  'graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa';\n\nconst SECURITY_LVL_SANDBOX = 'sandbox';\nconst SECURITY_LVL_LOOSE = 'loose';\n\nconst XMLNS_SVG_STD = 'http://www.w3.org/2000/svg';\nconst XMLNS_XLINK_STD = 'http://www.w3.org/1999/xlink';\nconst XMLNS_XHTML_STD = 'http://www.w3.org/1999/xhtml';\n\n// ------------------------------\n// iFrame\nconst IFRAME_WIDTH = '100%';\nconst IFRAME_HEIGHT = '100%';\nconst IFRAME_STYLES = 'border:0;margin:0;';\nconst IFRAME_BODY_STYLE = 'margin:0';\nconst IFRAME_SANDBOX_OPTS = 'allow-top-navigation-by-user-activation allow-popups';\nconst IFRAME_NOT_SUPPORTED_MSG = 'The \"iframe\" tag is not supported by your browser.';\n\n// DOMPurify settings for svgCode\nconst DOMPURIFY_TAGS = ['foreignobject'];\nconst DOMPURIFY_ATTR = ['dominant-baseline'];\n\nfunction processAndSetConfigs(text: string) {\n  const processed = preprocessDiagram(text);\n  configApi.reset();\n  configApi.addDirective(processed.config ?? {});\n  return processed;\n}\n\n/**\n * Parse the text and validate the syntax.\n * @param text - The mermaid diagram definition.\n * @param parseOptions - Options for parsing. @see {@link ParseOptions}\n * @returns An object with the `diagramType` set to type of the diagram if valid. Otherwise `false` if parseOptions.suppressErrors is `true`.\n * @throws Error if the diagram is invalid and parseOptions.suppressErrors is false or not set.\n */\nasync function parse(\n  text: string,\n  parseOptions: ParseOptions & { suppressErrors: true }\n): Promise<ParseResult | false>;\nasync function parse(text: string, parseOptions?: ParseOptions): Promise<ParseResult>;\nasync function parse(text: string, parseOptions?: ParseOptions): Promise<ParseResult | false> {\n  addDiagrams();\n  try {\n    const { code, config } = processAndSetConfigs(text);\n    const diagram = await getDiagramFromText(code);\n    return { diagramType: diagram.type, config };\n  } catch (error) {\n    if (parseOptions?.suppressErrors) {\n      return false;\n    }\n    throw error;\n  }\n}\n\n/**\n * Create a CSS style that starts with the given class name, then the element,\n * with an enclosing block that has each of the cssClasses followed by !important;\n * @param cssClass - CSS class name\n * @param element - CSS element\n * @param cssClasses - list of CSS styles to append after the element\n * @returns - the constructed string\n */\nexport const cssImportantStyles = (\n  cssClass: string,\n  element: string,\n  cssClasses: string[] = []\n): string => {\n  return `\\n.${cssClass} ${element} { ${cssClasses.join(' !important; ')} !important; }`;\n};\n\n/**\n * Create the user styles\n * @internal\n * @param  config - configuration that has style and theme settings to use\n * @param  classDefs - the classDefs in the diagram text. Might be null if none were defined. Usually is the result of a call to getClasses(...)\n * @returns  the string with all the user styles\n */\nexport const createCssStyles = (\n  config: MermaidConfig,\n  classDefs: Map<string, DiagramStyleClassDef> | null | undefined = new Map()\n): string => {\n  let cssStyles = '';\n\n  // user provided theme CSS info\n  // If you add more configuration driven data into the user styles make sure that the value is\n  // sanitized by the sanitize CSS function TODO where is this method?  what should be used to replace it?  refactor so that it's always sanitized\n  if (config.themeCSS !== undefined) {\n    cssStyles += `\\n${config.themeCSS}`;\n  }\n\n  if (config.fontFamily !== undefined) {\n    cssStyles += `\\n:root { --mermaid-font-family: ${config.fontFamily}}`;\n  }\n  if (config.altFontFamily !== undefined) {\n    cssStyles += `\\n:root { --mermaid-alt-font-family: ${config.altFontFamily}}`;\n  }\n\n  // classDefs defined in the diagram text\n  if (classDefs instanceof Map) {\n    const htmlLabels = config.htmlLabels ?? config.flowchart?.htmlLabels; // TODO why specifically check the Flowchart diagram config?\n\n    const cssHtmlElements = ['> *', 'span']; // TODO make a constant\n    const cssShapeElements = ['rect', 'polygon', 'ellipse', 'circle', 'path']; // TODO make a constant\n\n    const cssElements = htmlLabels ? cssHtmlElements : cssShapeElements;\n\n    // create the CSS styles needed for each styleClass definition and css element\n    classDefs.forEach((styleClassDef) => {\n      // create the css styles for each cssElement and the styles (only if there are styles)\n      if (!isEmpty(styleClassDef.styles)) {\n        cssElements.forEach((cssElement) => {\n          cssStyles += cssImportantStyles(styleClassDef.id, cssElement, styleClassDef.styles);\n        });\n      }\n      // create the css styles for the tspan element and the text styles (only if there are textStyles)\n      if (!isEmpty(styleClassDef.textStyles)) {\n        cssStyles += cssImportantStyles(\n          styleClassDef.id,\n          'tspan',\n          (styleClassDef?.textStyles || []).map((s) => s.replace('color', 'fill'))\n        );\n      }\n    });\n  }\n  return cssStyles;\n};\n\nexport const createUserStyles = (\n  config: MermaidConfig,\n  graphType: string,\n  classDefs: Map<string, DiagramStyleClassDef> | undefined,\n  svgId: string\n): string => {\n  const userCSSstyles = createCssStyles(config, classDefs);\n  const allStyles = getStyles(graphType, userCSSstyles, config.themeVariables);\n\n  // Now turn all of the styles into a (compiled) string that starts with the id\n  // use the stylis library to compile the css, turn the results into a valid CSS string (serialize(...., stringify))\n  // @see https://github.com/thysultan/stylis\n  return serialize(compile(`${svgId}{${allStyles}}`), stringify);\n};\n\n/**\n * Clean up svgCode. Do replacements needed\n *\n * @param svgCode - the code to clean up\n * @param inSandboxMode - security level\n * @param useArrowMarkerUrls - should arrow marker's use full urls? (vs. just the anchors)\n * @returns the cleaned up svgCode\n */\nexport const cleanUpSvgCode = (\n  svgCode = '',\n  inSandboxMode: boolean,\n  useArrowMarkerUrls: boolean\n): string => {\n  let cleanedUpSvg = svgCode;\n\n  // Replace marker-end urls with just the # anchor (remove the preceding part of the URL)\n  if (!useArrowMarkerUrls && !inSandboxMode) {\n    cleanedUpSvg = cleanedUpSvg.replace(\n      /marker-end=\"url\\([\\d+./:=?A-Za-z-]*?#/g,\n      'marker-end=\"url(#'\n    );\n  }\n\n  cleanedUpSvg = decodeEntities(cleanedUpSvg);\n\n  // replace old br tags with newer style\n  cleanedUpSvg = cleanedUpSvg.replace(/<br>/g, '<br/>');\n\n  return cleanedUpSvg;\n};\n\n/**\n * Put the svgCode into an iFrame. Return the iFrame code\n *\n * @param svgCode - the svg code to put inside the iFrame\n * @param svgElement - the d3 node that has the current svgElement so we can get the height from it\n * @returns  - the code with the iFrame that now contains the svgCode\n */\nexport const putIntoIFrame = (svgCode = '', svgElement?: D3Element): string => {\n  const height = svgElement?.viewBox?.baseVal?.height\n    ? svgElement.viewBox.baseVal.height + 'px'\n    : IFRAME_HEIGHT;\n  const base64encodedSrc = toBase64(`<body style=\"${IFRAME_BODY_STYLE}\">${svgCode}</body>`);\n  return `<iframe style=\"width:${IFRAME_WIDTH};height:${height};${IFRAME_STYLES}\" src=\"data:text/html;charset=UTF-8;base64,${base64encodedSrc}\" sandbox=\"${IFRAME_SANDBOX_OPTS}\">\n  ${IFRAME_NOT_SUPPORTED_MSG}\n</iframe>`;\n};\n\n/**\n * Append an enclosing div, then svg, then g (group) to the d3 parentRoot. Set attributes.\n * Only set the style attribute on the enclosing div if divStyle is given.\n * Only set the xmlns:xlink attribute on svg if svgXlink is given.\n * Return the last node appended\n *\n * @param parentRoot - the d3 node to append things to\n * @param id - the value to set the id attr to\n * @param enclosingDivId - the id to set the enclosing div to\n * @param divStyle - if given, the style to set the enclosing div to\n * @param svgXlink - if given, the link to set the new svg element to\n * @returns - returns the parentRoot that had nodes appended\n */\nexport const appendDivSvgG = (\n  parentRoot: D3Element,\n  id: string,\n  enclosingDivId: string,\n  divStyle?: string,\n  svgXlink?: string\n): D3Element => {\n  const enclosingDiv = parentRoot.append('div');\n  enclosingDiv.attr('id', enclosingDivId);\n  if (divStyle) {\n    enclosingDiv.attr('style', divStyle);\n  }\n\n  const svgNode = enclosingDiv\n    .append('svg')\n    .attr('id', id)\n    .attr('width', '100%')\n    .attr('xmlns', XMLNS_SVG_STD);\n  if (svgXlink) {\n    svgNode.attr('xmlns:xlink', svgXlink);\n  }\n\n  svgNode.append('g');\n  return parentRoot;\n};\n\n/**\n * Append an iFrame node to the given parentNode and set the id, style, and 'sandbox' attributes\n *  Return the appended iframe d3 node\n *\n * @param parentNode - the d3 node to append the iFrame node to\n * @param iFrameId - id to use for the iFrame\n * @returns the appended iframe d3 node\n */\nfunction sandboxedIframe(parentNode: D3Element, iFrameId: string): D3Element {\n  return parentNode\n    .append('iframe')\n    .attr('id', iFrameId)\n    .attr('style', 'width: 100%; height: 100%;')\n    .attr('sandbox', '');\n}\n\n/**\n * Remove any existing elements from the given document\n *\n * @param doc - the document to removed elements from\n * @param id - id for any existing SVG element\n * @param divSelector - selector for any existing enclosing div element\n * @param iFrameSelector - selector for any existing iFrame element\n */\nexport const removeExistingElements = (\n  doc: Document,\n  id: string,\n  divId: string,\n  iFrameId: string\n) => {\n  // Remove existing SVG element if it exists\n  doc.getElementById(id)?.remove();\n  // Remove previous temporary element if it exists\n  // Both div and iframe needs to be cleared in case there is a config change happening between renders.\n  doc.getElementById(divId)?.remove();\n  doc.getElementById(iFrameId)?.remove();\n};\n\n/**\n * @deprecated - use the `mermaid.render` function instead of `mermaid.mermaidAPI.render`\n *\n * Deprecated for external use.\n */\n\nconst render = async function (\n  id: string,\n  text: string,\n  svgContainingElement?: Element\n): Promise<RenderResult> {\n  addDiagrams();\n\n  const processed = processAndSetConfigs(text);\n  text = processed.code;\n\n  const config = configApi.getConfig();\n  log.debug(config);\n\n  // Check the maximum allowed text size\n  if (text.length > (config?.maxTextSize ?? MAX_TEXTLENGTH)) {\n    text = MAX_TEXTLENGTH_EXCEEDED_MSG;\n  }\n\n  const idSelector = '#' + id;\n  const iFrameID = 'i' + id;\n  const iFrameID_selector = '#' + iFrameID;\n  const enclosingDivID = 'd' + id;\n  const enclosingDivID_selector = '#' + enclosingDivID;\n\n  const removeTempElements = () => {\n    // -------------------------------------------------------------------------------\n    // Remove the temporary HTML element if appropriate\n    const tmpElementSelector = isSandboxed ? iFrameID_selector : enclosingDivID_selector;\n    const node = select(tmpElementSelector).node();\n    if (node && 'remove' in node) {\n      node.remove();\n    }\n  };\n\n  let root: any = select('body');\n\n  const isSandboxed = config.securityLevel === SECURITY_LVL_SANDBOX;\n  const isLooseSecurityLevel = config.securityLevel === SECURITY_LVL_LOOSE;\n\n  const fontFamily = config.fontFamily;\n\n  // -------------------------------------------------------------------------------\n  // Define the root d3 node\n  // In regular execution the svgContainingElement will be the element with a mermaid class\n\n  if (svgContainingElement !== undefined) {\n    if (svgContainingElement) {\n      svgContainingElement.innerHTML = '';\n    }\n\n    if (isSandboxed) {\n      // If we are in sandboxed mode, we do everything mermaid related in a (sandboxed )iFrame\n      const iframe = sandboxedIframe(select(svgContainingElement), iFrameID);\n      root = select(iframe.nodes()[0]!.contentDocument!.body);\n      root.node().style.margin = 0;\n    } else {\n      root = select(svgContainingElement);\n    }\n    appendDivSvgG(root, id, enclosingDivID, `font-family: ${fontFamily}`, XMLNS_XLINK_STD);\n  } else {\n    // No svgContainingElement was provided\n\n    // If there is an existing element with the id, we remove it. This likely a previously rendered diagram\n    removeExistingElements(document, id, enclosingDivID, iFrameID);\n\n    // Add the temporary div used for rendering with the enclosingDivID.\n    // This temporary div will contain a svg with the id == id\n\n    if (isSandboxed) {\n      // If we are in sandboxed mode, we do everything mermaid related in a (sandboxed) iFrame\n      const iframe = sandboxedIframe(select('body'), iFrameID);\n      root = select(iframe.nodes()[0]!.contentDocument!.body);\n      root.node().style.margin = 0;\n    } else {\n      root = select('body');\n    }\n\n    appendDivSvgG(root, id, enclosingDivID);\n  }\n\n  // -------------------------------------------------------------------------------\n  // Create the diagram\n\n  // Important that we do not create the diagram until after the directives have been included\n  let diag: Diagram;\n  let parseEncounteredException;\n\n  try {\n    diag = await Diagram.fromText(text, { title: processed.title });\n  } catch (error) {\n    if (config.suppressErrorRendering) {\n      removeTempElements();\n      throw error;\n    }\n    diag = await Diagram.fromText('error');\n    parseEncounteredException = error;\n  }\n\n  // Get the temporary div element containing the svg\n  const element = root.select(enclosingDivID_selector).node();\n  const diagramType = diag.type;\n\n  // -------------------------------------------------------------------------------\n  // Create and insert the styles (user styles, theme styles, config styles)\n\n  // Insert an element into svg. This is where we put the styles\n  const svg = element.firstChild;\n  const firstChild = svg.firstChild;\n  const diagramClassDefs = diag.renderer.getClasses?.(text, diag);\n\n  const rules = createUserStyles(config, diagramType, diagramClassDefs, idSelector);\n\n  const style1 = document.createElement('style');\n  style1.innerHTML = rules;\n  svg.insertBefore(style1, firstChild);\n\n  // -------------------------------------------------------------------------------\n  // Draw the diagram with the renderer\n  try {\n    await diag.renderer.draw(text, id, packageJson.version, diag);\n  } catch (e) {\n    if (config.suppressErrorRendering) {\n      removeTempElements();\n    } else {\n      errorRenderer.draw(text, id, packageJson.version);\n    }\n    throw e;\n  }\n\n  // This is the d3 node for the svg element\n  const svgNode = root.select(`${enclosingDivID_selector} svg`);\n  const a11yTitle: string | undefined = diag.db.getAccTitle?.();\n  const a11yDescr: string | undefined = diag.db.getAccDescription?.();\n  addA11yInfo(diagramType, svgNode, a11yTitle, a11yDescr);\n  // -------------------------------------------------------------------------------\n  // Clean up SVG code\n  root.select(`[id=\"${id}\"]`).selectAll('foreignobject > *').attr('xmlns', XMLNS_XHTML_STD);\n\n  // Fix for when the base tag is used\n  let svgCode: string = root.select(enclosingDivID_selector).node().innerHTML;\n\n  log.debug('config.arrowMarkerAbsolute', config.arrowMarkerAbsolute);\n  svgCode = cleanUpSvgCode(svgCode, isSandboxed, evaluate(config.arrowMarkerAbsolute));\n\n  if (isSandboxed) {\n    const svgEl = root.select(enclosingDivID_selector + ' svg').node();\n    svgCode = putIntoIFrame(svgCode, svgEl);\n  } else if (!isLooseSecurityLevel) {\n    // Sanitize the svgCode using DOMPurify\n    svgCode = DOMPurify.sanitize(svgCode, {\n      ADD_TAGS: DOMPURIFY_TAGS,\n      ADD_ATTR: DOMPURIFY_ATTR,\n      HTML_INTEGRATION_POINTS: { foreignobject: true },\n    });\n  }\n\n  attachFunctions();\n\n  if (parseEncounteredException) {\n    throw parseEncounteredException;\n  }\n\n  removeTempElements();\n\n  return {\n    diagramType,\n    svg: svgCode,\n    bindFunctions: diag.db.bindFunctions,\n  };\n};\n\n/**\n * @param  userOptions - Initial Mermaid options\n */\nfunction initialize(userOptions: MermaidConfig = {}) {\n  const options: MermaidConfig = assignWithDepth({}, userOptions);\n  // Handle legacy location of font-family configuration\n  if (options?.fontFamily && !options.themeVariables?.fontFamily) {\n    if (!options.themeVariables) {\n      options.themeVariables = {};\n    }\n    options.themeVariables.fontFamily = options.fontFamily;\n  }\n\n  // Set default options\n  configApi.saveConfigFromInitialize(options);\n\n  if (options?.theme && options.theme in theme) {\n    // Todo merge with user options\n    options.themeVariables = theme[options.theme as keyof typeof theme].getThemeVariables(\n      options.themeVariables\n    );\n  } else if (options) {\n    options.themeVariables = theme.default.getThemeVariables(options.themeVariables);\n  }\n\n  const config =\n    typeof options === 'object' ? configApi.setSiteConfig(options) : configApi.getSiteConfig();\n\n  setLogLevel(config.logLevel);\n  addDiagrams();\n}\n\nconst getDiagramFromText = (text: string, metadata: Pick<DiagramMetadata, 'title'> = {}) => {\n  const { code } = preprocessDiagram(text);\n  return Diagram.fromText(code, metadata);\n};\n\n/**\n * Add accessibility (a11y) information to the diagram.\n *\n * @param diagramType - diagram type\n * @param svgNode - d3 node to insert the a11y title and desc info\n * @param a11yTitle - a11y title\n * @param a11yDescr - a11y description\n */\nfunction addA11yInfo(\n  diagramType: string,\n  svgNode: D3Element,\n  a11yTitle?: string,\n  a11yDescr?: string\n): void {\n  setA11yDiagramInfo(svgNode, diagramType);\n  addSVGa11yTitleDescription(svgNode, a11yTitle, a11yDescr, svgNode.attr('id'));\n}\n\n/**\n * @internal - Use mermaid.function instead of mermaid.mermaidAPI.function\n */\nexport const mermaidAPI = Object.freeze({\n  render,\n  parse,\n  getDiagramFromText,\n  initialize,\n  getConfig: configApi.getConfig,\n  setConfig: configApi.setConfig,\n  getSiteConfig: configApi.getSiteConfig,\n  updateSiteConfig: configApi.updateSiteConfig,\n  reset: () => {\n    configApi.reset();\n  },\n  globalReset: () => {\n    configApi.reset(configApi.defaultConfig);\n  },\n  defaultConfig: configApi.defaultConfig,\n});\n\nsetLogLevel(configApi.getConfig().logLevel);\nconfigApi.reset(configApi.getConfig());\nexport default mermaidAPI;\n", "/**\n * Accessibility (a11y) functions, types, helpers.\n *\n * @see https://www.w3.org/WAI/\n * @see https://www.w3.org/TR/wai-aria-1.1/\n * @see https://www.w3.org/TR/svg-aam-1.0/\n */\nimport type { D3Element } from './types.js';\n\n/**\n * SVG element role:\n * The SVG element role _should_ be set to 'graphics-document' per SVG standard\n * but in practice is not always done by browsers, etc. (As of 2022-12-08).\n * A fallback role of 'document' should be set for those browsers, etc., that only support ARIA 1.0.\n *\n * @see https://www.w3.org/TR/svg-aam-1.0/#roleMappingGeneralRules\n * @see https://www.w3.org/TR/graphics-aria-1.0/#graphics-document\n */\nconst SVG_ROLE = 'graphics-document document';\n\n/**\n * Add role and aria-roledescription to the svg element.\n *\n * @param svg - d3 object that contains the SVG HTML element\n * @param diagramType - diagram name for to the aria-roledescription\n */\nexport function setA11yDiagramInfo(svg: D3Element, diagramType: string) {\n  svg.attr('role', SVG_ROLE);\n  if (diagramType !== '') {\n    svg.attr('aria-roledescription', diagramType);\n  }\n}\n\n/**\n * Add an accessible title and/or description element to a chart.\n * The title is usually not displayed and the description is never displayed.\n *\n * The following charts display their title as a visual and accessibility element: gantt.\n *\n * @param svg - d3 node to insert the a11y title and desc info\n * @param a11yTitle - a11y title. undefined or empty strings mean to skip them\n * @param a11yDesc - a11y description. undefined or empty strings mean to skip them\n * @param baseId - id used to construct the a11y title and description id\n */\nexport function addSVGa11yTitleDescription(\n  svg: D3Element,\n  a11yTitle: string | undefined,\n  a11yDesc: string | undefined,\n  baseId: string\n): void {\n  if (svg.insert === undefined) {\n    return;\n  }\n\n  if (a11yDesc) {\n    const descId = `chart-desc-${baseId}`;\n    svg.attr('aria-describedby', descId);\n    svg.insert('desc', ':first-child').attr('id', descId).text(a11yDesc);\n  }\n  if (a11yTitle) {\n    const titleId = `chart-title-${baseId}`;\n    svg.attr('aria-labelledby', titleId);\n    svg.insert('title', ':first-child').attr('id', titleId).text(a11yTitle);\n  }\n}\n", "import * as configApi from './config.js';\nimport { getDiagram, registerDiagram } from './diagram-api/diagramAPI.js';\nimport { detectType, getDiagramLoader } from './diagram-api/detectType.js';\nimport { UnknownDiagramError } from './errors.js';\nimport { encodeEntities } from './utils.js';\nimport type { DetailedError } from './utils.js';\nimport type { DiagramDefinition, DiagramMetadata } from './diagram-api/types.js';\n\n// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\nexport type ParseErrorFunction = (err: string | DetailedError | unknown, hash?: any) => void;\n\n/**\n * An object representing a parsed mermaid diagram definition.\n * @privateRemarks This is exported as part of the public mermaidAPI.\n */\nexport class Diagram {\n  public static async fromText(text: string, metadata: Pick<DiagramMetadata, 'title'> = {}) {\n    const config = configApi.getConfig();\n    const type = detectType(text, config);\n    text = encodeEntities(text) + '\\n';\n    try {\n      getDiagram(type);\n    } catch {\n      const loader = getDiagramLoader(type);\n      if (!loader) {\n        throw new UnknownDiagramError(`Diagram ${type} not found.`);\n      }\n      // Diagram not available, loading it.\n      // new diagram will try getDiagram again and if fails then it is a valid throw\n      const { id, diagram } = await loader();\n      registerDiagram(id, diagram);\n    }\n    const { db, parser, renderer, init } = getDiagram(type);\n    if (parser.parser) {\n      // The parser.parser.yy is only present in JISON parsers. So, we'll only set if required.\n      parser.parser.yy = db;\n    }\n    db.clear?.();\n    init?.(config);\n    // This block was added for legacy compatibility. Use frontmatter instead of adding more special cases.\n    if (metadata.title) {\n      db.setDiagramTitle?.(metadata.title);\n    }\n    await parser.parse(text);\n    return new Diagram(type, text, db, parser, renderer);\n  }\n\n  private constructor(\n    public type: string,\n    public text: string,\n    public db: DiagramDefinition['db'],\n    public parser: DiagramDefinition['parser'],\n    public renderer: DiagramDefinition['renderer']\n  ) {}\n\n  async render(id: string, version: string) {\n    await this.renderer.draw(this.text, id, version, this);\n  }\n\n  getParser() {\n    return this.parser;\n  }\n\n  getType() {\n    return this.type;\n  }\n}\n", "let interactionFunctions: (() => void)[] = [];\nexport const addFunction = (func: () => void) => {\n  interactionFunctions.push(func);\n};\nexport const attachFunctions = () => {\n  interactionFunctions.forEach((f) => {\n    f();\n  });\n  interactionFunctions = [];\n};\n", "/**\n * Remove all lines starting with `%%` from the text that don't contain a `%%{`\n * @param text - The text to remove comments from\n * @returns cleaned text\n */\nexport const cleanupComments = (text: string): string => {\n  return text.replace(/^\\s*%%(?!{)[^\\n]+\\n?/gm, '').trimStart();\n};\n", "import type { GanttDiagramConfig, MermaidConfig } from '../config.type.js';\nimport { frontMatterRegex } from './regexes.js';\n// The \"* as yaml\" part is necessary for tree-shaking\nimport * as yaml from 'js-yaml';\n\ninterface FrontMatterMetadata {\n  title?: string;\n  // Allows custom display modes. Currently used for compact mode in gantt charts.\n  displayMode?: GanttDiagramConfig['displayMode'];\n  config?: MermaidConfig;\n}\n\nexport interface FrontMatterResult {\n  text: string;\n  metadata: FrontMatterMetadata;\n}\n\n/**\n * Extract and parse frontmatter from text, if present, and sets appropriate\n * properties in the provided db.\n * @param text - The text that may have a YAML frontmatter.\n * @returns text with frontmatter stripped out\n */\nexport function extractFrontMatter(text: string): FrontMatterResult {\n  const matches = text.match(frontMatterRegex);\n  if (!matches) {\n    return {\n      text,\n      metadata: {},\n    };\n  }\n\n  let parsed: FrontMatterMetadata =\n    yaml.load(matches[1], {\n      // To support config, we need JSON schema.\n      // https://www.yaml.org/spec/1.2/spec.html#id2803231\n      schema: yaml.JSON_SCHEMA,\n    }) ?? {};\n\n  // To handle runtime data type changes\n  parsed = typeof parsed === 'object' && !Array.isArray(parsed) ? parsed : {};\n\n  const metadata: FrontMatterMetadata = {};\n\n  // Only add properties that are explicitly supported, if they exist\n  if (parsed.displayMode) {\n    metadata.displayMode = parsed.displayMode.toString() as GanttDiagramConfig['displayMode'];\n  }\n  if (parsed.title) {\n    metadata.title = parsed.title.toString();\n  }\n  if (parsed.config) {\n    metadata.config = parsed.config;\n  }\n\n  return {\n    text: text.slice(matches[0].length),\n    metadata,\n  };\n}\n", "import { cleanupComments } from './diagram-api/comments.js';\nimport { extractFrontMatter } from './diagram-api/frontmatter.js';\nimport type { DiagramMetadata } from './diagram-api/types.js';\nimport utils, { cleanAndMerge, removeDirectives } from './utils.js';\n\nconst cleanupText = (code: string) => {\n  return (\n    code\n      // parser problems on CRLF ignore all CR and leave LF;;\n      .replace(/\\r\\n?/g, '\\n')\n      // clean up html tags so that all attributes use single quotes, parser throws error on double quotes\n      .replace(\n        /<(\\w+)([^>]*)>/g,\n        (match, tag, attributes) => '<' + tag + attributes.replace(/=\"([^\"]*)\"/g, \"='$1'\") + '>'\n      )\n  );\n};\n\nconst processFrontmatter = (code: string) => {\n  const { text, metadata } = extractFrontMatter(code);\n  const { displayMode, title, config = {} } = metadata;\n  if (displayMode) {\n    // Needs to be supported for legacy reasons\n    if (!config.gantt) {\n      config.gantt = {};\n    }\n    config.gantt.displayMode = displayMode;\n  }\n  return { title, config, text };\n};\n\nconst processDirectives = (code: string) => {\n  const initDirective = utils.detectInit(code) ?? {};\n  const wrapDirectives = utils.detectDirective(code, 'wrap');\n  if (Array.isArray(wrapDirectives)) {\n    initDirective.wrap = wrapDirectives.some(({ type }) => type === 'wrap');\n  } else if (wrapDirectives?.type === 'wrap') {\n    initDirective.wrap = true;\n  }\n  return {\n    text: removeDirectives(code),\n    directive: initDirective,\n  };\n};\n\n/**\n * Preprocess the given code by cleaning it up, extracting front matter and directives,\n * cleaning and merging configuration, and removing comments.\n * @param code - The code to preprocess.\n * @returns The object containing the preprocessed code, title, and configuration.\n */\nexport function preprocessDiagram(code: string) {\n  const cleanedCode = cleanupText(code);\n  const frontMatterResult = processFrontmatter(cleanedCode);\n  const directiveResult = processDirectives(frontMatterResult.text);\n  const config = cleanAndMerge(frontMatterResult.config, directiveResult.directive);\n  code = cleanupComments(directiveResult.text);\n  return {\n    code,\n    title: frontMatterResult.title,\n    config,\n  } satisfies DiagramMetadata & { code: string };\n}\n", "export function toBase64(str: string) {\n  // ref: https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n  const utf8Bytes = new TextEncoder().encode(str);\n  const utf8Str = Array.from(utf8Bytes, (byte) => String.fromCodePoint(byte)).join('');\n  return btoa(utf8Str);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAAS,cAAc;;;ACCvB,IAAM,KAAK;AAEX,IAAM,WAA4B,wBAAC,QAAQ;AACzC,SAAO,+DAA+D,KAAK,GAAG;AAChF,GAFkC;AAIlC,IAAM,SAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAA,SAAQ,IAAI,MAAM,OAAO,8CAAgB;AACjD,SAAO,EAAE,IAAI,SAAAA,SAAQ;AACvB,GAH8B;AAK9B,IAAM,SAAoC;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAO,qBAAQ;;;ACjBf,IAAMC,MAAK;AAEX,IAAMC,YAA4B,wBAAC,KAAK,WAAW;AAGjD,MACE,QAAQ,WAAW,oBAAoB,mBACvC,QAAQ,WAAW,oBAAoB,OACvC;AACA,WAAO;AAAA,EACT;AACA,SAAO,YAAY,KAAK,GAAG;AAC7B,GAVkC;AAYlC,IAAMC,UAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,gDAAkB;AACnD,SAAO,EAAE,IAAAH,KAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,UAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,uBAAQE;;;ACzBf,IAAMC,MAAK;AAEX,IAAMC,YAA4B,wBAAC,KAAK,WAAW;AACjD,MAAI,QAAQ,WAAW,oBAAoB,YAAY;AACrD,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,WAAW,oBAAoB,OAAO;AAChD,WAAO,SAAS;AAAA,EAClB;AAGA,MAAI,YAAY,KAAK,GAAG,KAAK,QAAQ,WAAW,oBAAoB,iBAAiB;AACnF,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,KAAK,GAAG;AACjC,GAdkC;AAgBlC,IAAMC,UAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,gDAAkB;AACnD,SAAO,EAAE,IAAAH,KAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,UAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,0BAAQE;;;AC7Bf,IAAMC,MAAK;AAEX,IAAMC,YAA4B,wBAAC,QAAQ;AACzC,SAAO,gBAAgB,KAAK,GAAG;AACjC,GAFkC;AAIlC,IAAMC,UAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,8CAAgB;AACjD,SAAO,EAAE,IAAAH,KAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,UAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,qBAAQE;;;ACpBf,IAAMC,MAAK;AAEX,IAAMC,YAA4B,wBAAC,QAAQ;AACzC,SAAO,eAAe,KAAK,GAAG;AAChC,GAFkC;AAIlC,IAAMC,UAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,oDAAsB;AACvD,SAAO,EAAE,IAAAH,KAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,UAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,2BAAQE;;;ACdf,IAAMC,MAAK;AAEX,IAAMC,YAA4B,wBAAC,QAAQ;AACzC,SAAO,YAAY,KAAK,GAAG;AAC7B,GAFkC;AAIlC,IAAMC,UAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,iDAAmB;AACpD,SAAO,EAAE,IAAAH,KAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,UAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,wBAAQE;;;ACjBf,IAAMC,MAAK;AAEX,IAAMC,YAA4B,wBAAC,QAAQ;AACzC,SAAO,WAAW,KAAK,GAAG;AAC5B,GAFkC;AAIlC,IAAMC,UAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,gDAAkB;AACnD,SAAO,EAAE,IAAAH,KAAI,SAAAG,SAAQ;AACvB,GAH8B;AAKvB,IAAM,OAAkC;AAAA,EAC7C,IAAAH;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;;;ACfA,IAAME,MAAK;AAEX,IAAMC,YAA4B,wBAAC,QAAQ;AACzC,SAAO,UAAU,KAAK,GAAG;AAC3B,GAFkC;AAIlC,IAAMC,UAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,+CAAiB;AAClD,SAAO,EAAE,IAAAH,KAAI,SAAAG,SAAQ;AACvB,GAH8B;AAKvB,IAAM,MAAiC;AAAA,EAC5C,IAAAH;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;;;ACfA,IAAME,MAAK;AAEX,IAAMC,YAA4B,wBAAC,QAAQ;AACzC,SAAO,oBAAoB,KAAK,GAAG;AACrC,GAFkC;AAIlC,IAAMC,UAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,oDAAsB;AACvD,SAAO,EAAE,IAAAH,KAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,UAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,2BAAQE;;;ACjBf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,mBAAmB,KAAK,GAAG;AACpC,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,mDAAqB;AACtD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,UAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,0BAAQE;;;ACjBf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,4BAA4B,KAAK,GAAG;AAC7C,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,uDAAyB;AAC1D,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,UAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,8BAAQE;;;ACjBf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,sBAAsB,KAAK,GAAG;AACvC,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,oDAAsB;AACvD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,2BAAQE;;;ACjBf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,KAAK,WAAW;AAEjD,MAAI,QAAQ,OAAO,oBAAoB,iBAAiB;AACtD,WAAO;AAAA,EACT;AAEA,SAAO,mBAAmB,KAAK,GAAG;AACpC,GAPkC;AASlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,iDAAmB;AACpD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,wBAAQE;;;ACtBf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,KAAK,WAAW;AAEjD,MAAI,mBAAmB,KAAK,GAAG,KAAK,QAAQ,OAAO,oBAAoB,iBAAiB;AACtF,WAAO;AAAA,EACT;AAEA,SAAO,sBAAsB,KAAK,GAAG;AACvC,GAPkC;AASlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,oDAAsB;AACvD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,2BAAQE;;;ACtBf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,KAAK,WAAW;AAGjD,MAAI,QAAQ,OAAO,oBAAoB,iBAAiB;AACtD,WAAO;AAAA,EACT;AACA,SAAO,mBAAmB,KAAK,GAAG;AACpC,GAPkC;AASlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,iDAAmB;AACpD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,wBAAQE;;;ACtBf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,KAAK,WAAW;AACjD,MAAI,sBAAsB,KAAK,GAAG,GAAG;AACnC,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,KAAK,GAAG,KAAK,QAAQ,OAAO,oBAAoB,iBAAiB;AACtF,WAAO;AAAA,EACT;AACA,SAAO;AACT,GARkC;AAUlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,oDAAsB;AACvD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,2BAAQE;;;ACvBf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,cAAc,KAAK,GAAG;AAC/B,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,mDAAqB;AACtD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,0BAAQE;;;ACXR,IAAM,OAAO,wBAAC,OAAeC,MAAY,YAAoB;AAClE,MAAI,MAAM,kCAAkC;AAC5C,QAAM,MAAW,iBAAiBA,IAAE;AACpC,QAAM,IAAc,IAAI,OAAO,GAAG;AAElC,MAAI,KAAK,WAAW,cAAc;AAClC,mBAAiB,KAAK,KAAK,KAAK,IAAI;AAEpC,IAAE,OAAO,MAAM,EACZ,KAAK,SAAS,YAAY,EAC1B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AAEF,IAAE,OAAO,MAAM,EACZ,KAAK,SAAS,YAAY,EAC1B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AAEF,IAAE,OAAO,MAAM,EACZ,KAAK,SAAS,YAAY,EAC1B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AAEF,IAAE,OAAO,MAAM,EACZ,KAAK,SAAS,YAAY,EAC1B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AAEF,IAAE,OAAO,MAAM,EACZ,KAAK,SAAS,YAAY,EAC1B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AAEF,IAAE,OAAO,MAAM,EACZ,KAAK,SAAS,YAAY,EAC1B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AAEF,IAAE,OAAO,MAAM,EACZ,KAAK,SAAS,YAAY,EAC1B,KAAK,KAAK,IAAI,EACd,KAAK,KAAK,GAAG,EACb,KAAK,aAAa,OAAO,EACzB,MAAM,eAAe,QAAQ,EAC7B,KAAK,sBAAsB;AAC9B,IAAE,OAAO,MAAM,EACZ,KAAK,SAAS,YAAY,EAC1B,KAAK,KAAK,IAAI,EACd,KAAK,KAAK,GAAG,EACb,KAAK,aAAa,OAAO,EACzB,MAAM,eAAe,QAAQ,EAC7B,KAAK,mBAAmB,OAAO,EAAE;AACtC,GAhEoB;AAkEb,IAAM,WAAW,EAAE,KAAK;AAE/B,IAAO,wBAAQ;;;AC7Ef,IAAM,UAA6B;AAAA,EACjC,IAAI,CAAC;AAAA,EACL;AAAA,EACA,QAAQ;AAAA,IACN,OAAO,6BAAY;AACjB;AAAA,IACF,GAFO;AAAA,EAGT;AACF;AAEA,IAAO,uBAAQ;;;ACPf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,KAAK,SAAS,CAAC,MAAe;AAC/D;AAAA;AAAA,IAEE,oBAAoB,KAAK,GAAG;AAAA,IAE3B,sBAAsB,KAAK,GAAG,KAAK,QAAQ,WAAW,oBAAoB;AAAA,IAC3E;AACA,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AACA,SAAO;AACT,GAXkC;AAalC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,gDAAmB;AACpD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,mBAAQE;;;AC1Bf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,eAAe,KAAK,GAAG;AAChC,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,wDAA0B;AAC3D,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAOG,oBAAQD;;;AClBf,IAAME,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,cAAc,KAAK,GAAG;AAC/B,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,uDAAyB;AAC1D,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAOG,oBAAQD;;;ACjBf,IAAME,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,aAAa,KAAK,GAAG;AAC9B,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,sDAAwB;AACzD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAOG,oBAAQD;;;ACpBf,IAAME,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,kBAAkB,KAAK,GAAG;AACnC,GAFkC;AAIlC,IAAMC,WAAS,mCAAY;AACzB,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,kDAAoB;AACrD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAHe;AAKf,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,yBAAQE;;;ACbf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,kBAAkB,KAAK,GAAG;AACnC,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,4CAAc;AAC/C,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAKvB,IAAM,SAAoC;AAAA,EAC/C,IAAAH;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;;;ACfA,IAAME,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,iBAAiB,KAAK,GAAG;AAClC,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,4CAAc;AAC/C,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAKvB,IAAM,QAAmC;AAAA,EAC9C,IAAAH;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;;;ACnBA,IAAME,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,iBAAiB,KAAK,GAAG;AAClC,GAFkC;AAIlC,IAAMC,WAAS,mCAAY;AACzB,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,iDAAmB;AACpD,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAHe;AAKf,IAAMC,WAAoC;AAAA,EACxC,IAAAJ;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,wBAAQE;;;ACbf,IAAMC,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,mBAAmB,KAAK,GAAG;AACpC,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,wDAA0B;AAC3D,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAK9B,IAAM,eAA0C;AAAA,EAC9C,IAAAH;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;AAEA,IAAO,+BAAQ;;;ACjBf,IAAME,OAAK;AAEX,IAAMC,aAA4B,wBAAC,QAAQ;AACzC,SAAO,cAAc,KAAK,GAAG;AAC/B,GAFkC;AAIlC,IAAMC,WAAwB,mCAAY;AACxC,QAAM,EAAE,SAAAC,SAAQ,IAAI,MAAM,OAAO,4CAAc;AAC/C,SAAO,EAAE,IAAAH,MAAI,SAAAG,SAAQ;AACvB,GAH8B;AAKvB,IAAM,UAAqC;AAAA,EAChD,IAAAH;AAAA,EACA,UAAAC;AAAA,EACA,QAAAC;AACF;;;ACWA,IAAI,oBAAoB;AACjB,IAAM,cAAc,6BAAM;AAC/B,MAAI,mBAAmB;AACrB;AAAA,EACF;AAGA,sBAAoB;AACpB,kBAAgB,SAAS,sBAAc,CAAC,SAAS;AAC/C,WAAO,KAAK,YAAY,EAAE,KAAK,MAAM;AAAA,EACvC,CAAC;AACD;AAAA,IACE;AAAA;AAAA,IAEA;AAAA,MACE,IAAI;AAAA,QACF,OAAO,6BAAM;AAAA,QAEb,GAFO;AAAA,MAGT;AAAA,MACA,QAAQ,CAAC;AAAA;AAAA,MACT,UAAU;AAAA,QACR,MAAM,6BAAM;AAAA,QAEZ,GAFM;AAAA,MAGR;AAAA,MACA,QAAQ;AAAA,QACN,OAAO,6BAAM;AACX,gBAAM,IAAI;AAAA,YACR;AAAA,UAGF;AAAA,QACF,GANO;AAAA,MAOT;AAAA,MACA,MAAM,6BAAM,MAAN;AAAA;AAAA,IACR;AAAA,IACA,CAAC,SAAS;AACR,aAAO,KAAK,YAAY,EAAE,UAAU,EAAE,WAAW,KAAK;AAAA,IACxD;AAAA,EACF;AAEA,MAAI,MAAsB;AACxB,+BAA2B,kBAAcE,mBAAS,4BAAY;AAAA,EAChE;AAGA;AAAA,IACE;AAAA,IACAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,GAxE2B;;;AC7BpB,IAAM,yBAAyB,mCAAY;AAChD,MAAI,MAAM,6BAA6B;AAEvC,QAAM,UAAU,MAAM,QAAQ;AAAA,IAC5B,OAAO,QAAQ,SAAS,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE,UAAAC,YAAU,QAAAC,SAAO,CAAC,MAAM;AACnE,UAAIA,UAAQ;AACV,YAAI;AACF,qBAAW,GAAG;AAAA,QAChB,QAAQ;AACN,cAAI;AAEF,kBAAM,EAAE,SAAAC,UAAS,IAAAC,KAAG,IAAI,MAAMF,SAAO;AACrC,4BAAgBE,MAAID,UAASF,UAAQ;AAAA,UACvC,SAAS,KAAK;AAEZ,gBAAI,MAAM,4CAA4C,GAAG,4BAA4B;AACrF,mBAAO,UAAU,GAAG;AACpB,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,SAAS,QAAQ,OAAO,CAAC,WAAW,OAAO,WAAW,UAAU;AACtE,MAAI,OAAO,SAAS,GAAG;AACrB,QAAI,MAAM,kBAAkB,OAAO,MAAM,oBAAoB;AAC7D,eAAW,OAAO,QAAQ;AACxB,UAAI,MAAM,GAAG;AAAA,IACf;AACA,UAAM,IAAI,MAAM,kBAAkB,OAAO,MAAM,oBAAoB;AAAA,EACrE;AACF,GA/BsC;;;ACCtC,SAAS,cAAc;AACvB,SAAS,SAAS,WAAW,iBAAiB;AAC9C,OAAO,eAAe;AACtB,OAAO,aAAa;;;ACUpB,IAAM,WAAW;AAQV,SAAS,mBAAmB,KAAgB,aAAqB;AACtE,MAAI,KAAK,QAAQ,QAAQ;AACzB,MAAI,gBAAgB,IAAI;AACtB,QAAI,KAAK,wBAAwB,WAAW;AAAA,EAC9C;AACF;AALgB;AAkBT,SAAS,2BACd,KACA,WACA,UACA,QACM;AACN,MAAI,IAAI,WAAW,QAAW;AAC5B;AAAA,EACF;AAEA,MAAI,UAAU;AACZ,UAAM,SAAS,cAAc,MAAM;AACnC,QAAI,KAAK,oBAAoB,MAAM;AACnC,QAAI,OAAO,QAAQ,cAAc,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,QAAQ;AAAA,EACrE;AACA,MAAI,WAAW;AACb,UAAM,UAAU,eAAe,MAAM;AACrC,QAAI,KAAK,mBAAmB,OAAO;AACnC,QAAI,OAAO,SAAS,cAAc,EAAE,KAAK,MAAM,OAAO,EAAE,KAAK,SAAS;AAAA,EACxE;AACF;AApBgB;;;AC7BT,IAAM,UAAN,MAAM,SAAQ;AAAA,EAgCX,YACC,MACA,MACA,IACA,QACAI,WACP;AALO;AACA;AACA;AACA;AACA,oBAAAA;AAAA,EACN;AAAA,EArDL,OAeqB;AAAA;AAAA;AAAA,EACnB,aAAoB,SAAS,MAAc,WAA2C,CAAC,GAAG;AACxF,UAAM,SAAmB,UAAU;AACnC,UAAM,OAAO,WAAW,MAAM,MAAM;AACpC,WAAO,eAAe,IAAI,IAAI;AAC9B,QAAI;AACF,iBAAW,IAAI;AAAA,IACjB,QAAQ;AACN,YAAMC,WAAS,iBAAiB,IAAI;AACpC,UAAI,CAACA,UAAQ;AACX,cAAM,IAAI,oBAAoB,WAAW,IAAI,aAAa;AAAA,MAC5D;AAGA,YAAM,EAAE,IAAAC,MAAI,SAAAC,SAAQ,IAAI,MAAMF,SAAO;AACrC,sBAAgBC,MAAIC,QAAO;AAAA,IAC7B;AACA,UAAM,EAAE,IAAI,QAAQ,UAAAH,WAAU,MAAAI,MAAK,IAAI,WAAW,IAAI;AACtD,QAAI,OAAO,QAAQ;AAEjB,aAAO,OAAO,KAAK;AAAA,IACrB;AACA,OAAG,QAAQ;AACX,IAAAA,QAAO,MAAM;AAEb,QAAI,SAAS,OAAO;AAClB,SAAG,kBAAkB,SAAS,KAAK;AAAA,IACrC;AACA,UAAM,OAAO,MAAM,IAAI;AACvB,WAAO,IAAI,SAAQ,MAAM,MAAM,IAAI,QAAQJ,SAAQ;AAAA,EACrD;AAAA,EAUA,MAAM,OAAOE,MAAY,SAAiB;AACxC,UAAM,KAAK,SAAS,KAAK,KAAK,MAAMA,MAAI,SAAS,IAAI;AAAA,EACvD;AAAA,EAEA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AACF;;;AClEA,IAAI,uBAAuC,CAAC;AAIrC,IAAM,kBAAkB,6BAAM;AACnC,uBAAqB,QAAQ,CAAC,MAAM;AAClC,MAAE;AAAA,EACJ,CAAC;AACD,yBAAuB,CAAC;AAC1B,GAL+B;;;ACCxB,IAAM,kBAAkB,wBAAC,SAAyB;AACvD,SAAO,KAAK,QAAQ,0BAA0B,EAAE,EAAE,UAAU;AAC9D,GAF+B;;;ACkBxB,SAAS,mBAAmB,MAAiC;AAClE,QAAM,UAAU,KAAK,MAAM,gBAAgB;AAC3C,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,MACL;AAAA,MACA,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAEA,MAAI,SACG,KAAK,QAAQ,CAAC,GAAG;AAAA;AAAA;AAAA,IAGpB,QAAa;AAAA,EACf,CAAC,KAAK,CAAC;AAGT,WAAS,OAAO,WAAW,YAAY,CAAC,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC;AAE1E,QAAM,WAAgC,CAAC;AAGvC,MAAI,OAAO,aAAa;AACtB,aAAS,cAAc,OAAO,YAAY,SAAS;AAAA,EACrD;AACA,MAAI,OAAO,OAAO;AAChB,aAAS,QAAQ,OAAO,MAAM,SAAS;AAAA,EACzC;AACA,MAAI,OAAO,QAAQ;AACjB,aAAS,SAAS,OAAO;AAAA,EAC3B;AAEA,SAAO;AAAA,IACL,MAAM,KAAK,MAAM,QAAQ,CAAC,EAAE,MAAM;AAAA,IAClC;AAAA,EACF;AACF;AApCgB;;;AClBhB,IAAM,cAAc,wBAAC,SAAiB;AACpC,SACE,KAEG,QAAQ,UAAU,IAAI,EAEtB;AAAA,IACC;AAAA,IACA,CAAC,OAAO,KAAK,eAAe,MAAM,MAAM,WAAW,QAAQ,eAAe,OAAO,IAAI;AAAA,EACvF;AAEN,GAXoB;AAapB,IAAM,qBAAqB,wBAAC,SAAiB;AAC3C,QAAM,EAAE,MAAM,SAAS,IAAI,mBAAmB,IAAI;AAClD,QAAM,EAAE,aAAa,OAAO,SAAS,CAAC,EAAE,IAAI;AAC5C,MAAI,aAAa;AAEf,QAAI,CAAC,OAAO,OAAO;AACjB,aAAO,QAAQ,CAAC;AAAA,IAClB;AACA,WAAO,MAAM,cAAc;AAAA,EAC7B;AACA,SAAO,EAAE,OAAO,QAAQ,KAAK;AAC/B,GAX2B;AAa3B,IAAM,oBAAoB,wBAAC,SAAiB;AAC1C,QAAM,gBAAgB,cAAM,WAAW,IAAI,KAAK,CAAC;AACjD,QAAM,iBAAiB,cAAM,gBAAgB,MAAM,MAAM;AACzD,MAAI,MAAM,QAAQ,cAAc,GAAG;AACjC,kBAAc,OAAO,eAAe,KAAK,CAAC,EAAE,KAAK,MAAM,SAAS,MAAM;AAAA,EACxE,WAAW,gBAAgB,SAAS,QAAQ;AAC1C,kBAAc,OAAO;AAAA,EACvB;AACA,SAAO;AAAA,IACL,MAAM,iBAAiB,IAAI;AAAA,IAC3B,WAAW;AAAA,EACb;AACF,GAZ0B;AAoBnB,SAAS,kBAAkB,MAAc;AAC9C,QAAM,cAAc,YAAY,IAAI;AACpC,QAAM,oBAAoB,mBAAmB,WAAW;AACxD,QAAM,kBAAkB,kBAAkB,kBAAkB,IAAI;AAChE,QAAM,SAAS,cAAc,kBAAkB,QAAQ,gBAAgB,SAAS;AAChF,SAAO,gBAAgB,gBAAgB,IAAI;AAC3C,SAAO;AAAA,IACL;AAAA,IACA,OAAO,kBAAkB;AAAA,IACzB;AAAA,EACF;AACF;AAXgB;;;ACnDT,SAAS,SAAS,KAAa;AAEpC,QAAM,YAAY,IAAI,YAAY,EAAE,OAAO,GAAG;AAC9C,QAAM,UAAU,MAAM,KAAK,WAAW,CAAC,SAAS,OAAO,cAAc,IAAI,CAAC,EAAE,KAAK,EAAE;AACnF,SAAO,KAAK,OAAO;AACrB;AALgB;;;AP4BhB,IAAM,iBAAiB;AACvB,IAAM,8BACJ;AAEF,IAAM,uBAAuB;AAC7B,IAAM,qBAAqB;AAE3B,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AAIxB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAC1B,IAAM,sBAAsB;AAC5B,IAAM,2BAA2B;AAGjC,IAAM,iBAAiB,CAAC,eAAe;AACvC,IAAM,iBAAiB,CAAC,mBAAmB;AAE3C,SAAS,qBAAqB,MAAc;AAC1C,QAAM,YAAY,kBAAkB,IAAI;AACxC,EAAU,MAAM;AAChB,EAAU,aAAa,UAAU,UAAU,CAAC,CAAC;AAC7C,SAAO;AACT;AALS;AAmBT,eAAe,MAAM,MAAc,cAA2D;AAC5F,cAAY;AACZ,MAAI;AACF,UAAM,EAAE,MAAM,OAAO,IAAI,qBAAqB,IAAI;AAClD,UAAMG,WAAU,MAAM,mBAAmB,IAAI;AAC7C,WAAO,EAAE,aAAaA,SAAQ,MAAM,OAAO;AAAA,EAC7C,SAAS,OAAO;AACd,QAAI,cAAc,gBAAgB;AAChC,aAAO;AAAA,IACT;AACA,UAAM;AAAA,EACR;AACF;AAZe;AAsBR,IAAM,qBAAqB,wBAChC,UACA,SACA,aAAuB,CAAC,MACb;AACX,SAAO;AAAA,GAAM,QAAQ,IAAI,OAAO,MAAM,WAAW,KAAK,eAAe,CAAC;AACxE,GANkC;AAe3B,IAAM,kBAAkB,wBAC7B,QACA,YAAkE,oBAAI,IAAI,MAC/D;AACX,MAAI,YAAY;AAKhB,MAAI,OAAO,aAAa,QAAW;AACjC,iBAAa;AAAA,EAAK,OAAO,QAAQ;AAAA,EACnC;AAEA,MAAI,OAAO,eAAe,QAAW;AACnC,iBAAa;AAAA,iCAAoC,OAAO,UAAU;AAAA,EACpE;AACA,MAAI,OAAO,kBAAkB,QAAW;AACtC,iBAAa;AAAA,qCAAwC,OAAO,aAAa;AAAA,EAC3E;AAGA,MAAI,qBAAqB,KAAK;AAC5B,UAAM,aAAa,OAAO,cAAc,OAAO,WAAW;AAE1D,UAAM,kBAAkB,CAAC,OAAO,MAAM;AACtC,UAAM,mBAAmB,CAAC,QAAQ,WAAW,WAAW,UAAU,MAAM;AAExE,UAAM,cAAc,aAAa,kBAAkB;AAGnD,cAAU,QAAQ,CAAC,kBAAkB;AAEnC,UAAI,CAAC,QAAQ,cAAc,MAAM,GAAG;AAClC,oBAAY,QAAQ,CAAC,eAAe;AAClC,uBAAa,mBAAmB,cAAc,IAAI,YAAY,cAAc,MAAM;AAAA,QACpF,CAAC;AAAA,MACH;AAEA,UAAI,CAAC,QAAQ,cAAc,UAAU,GAAG;AACtC,qBAAa;AAAA,UACX,cAAc;AAAA,UACd;AAAA,WACC,eAAe,cAAc,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,SAAS,MAAM,CAAC;AAAA,QACzE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT,GAhD+B;AAkDxB,IAAM,mBAAmB,wBAC9B,QACA,WACA,WACA,UACW;AACX,QAAM,gBAAgB,gBAAgB,QAAQ,SAAS;AACvD,QAAM,YAAY,eAAU,WAAW,eAAe,OAAO,cAAc;AAK3E,SAAO,UAAU,QAAQ,GAAG,KAAK,IAAI,SAAS,GAAG,GAAG,SAAS;AAC/D,GAbgC;AAuBzB,IAAM,iBAAiB,wBAC5B,UAAU,IACV,eACA,uBACW;AACX,MAAI,eAAe;AAGnB,MAAI,CAAC,sBAAsB,CAAC,eAAe;AACzC,mBAAe,aAAa;AAAA,MAC1B;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,iBAAe,eAAe,YAAY;AAG1C,iBAAe,aAAa,QAAQ,SAAS,OAAO;AAEpD,SAAO;AACT,GArB8B;AA8BvB,IAAM,gBAAgB,wBAAC,UAAU,IAAI,eAAmC;AAC7E,QAAM,SAAS,YAAY,SAAS,SAAS,SACzC,WAAW,QAAQ,QAAQ,SAAS,OACpC;AACJ,QAAM,mBAAmB,SAAS,gBAAgB,iBAAiB,KAAK,OAAO,SAAS;AACxF,SAAO,wBAAwB,YAAY,WAAW,MAAM,IAAI,aAAa,8CAA8C,gBAAgB,cAAc,mBAAmB;AAAA,IAC1K,wBAAwB;AAAA;AAE5B,GAR6B;AAuBtB,IAAM,gBAAgB,wBAC3B,YACAC,MACA,gBACA,UACA,aACc;AACd,QAAM,eAAe,WAAW,OAAO,KAAK;AAC5C,eAAa,KAAK,MAAM,cAAc;AACtC,MAAI,UAAU;AACZ,iBAAa,KAAK,SAAS,QAAQ;AAAA,EACrC;AAEA,QAAM,UAAU,aACb,OAAO,KAAK,EACZ,KAAK,MAAMA,IAAE,EACb,KAAK,SAAS,MAAM,EACpB,KAAK,SAAS,aAAa;AAC9B,MAAI,UAAU;AACZ,YAAQ,KAAK,eAAe,QAAQ;AAAA,EACtC;AAEA,UAAQ,OAAO,GAAG;AAClB,SAAO;AACT,GAxB6B;AAkC7B,SAAS,gBAAgB,YAAuB,UAA6B;AAC3E,SAAO,WACJ,OAAO,QAAQ,EACf,KAAK,MAAM,QAAQ,EACnB,KAAK,SAAS,4BAA4B,EAC1C,KAAK,WAAW,EAAE;AACvB;AANS;AAgBF,IAAM,yBAAyB,wBACpC,KACAA,MACA,OACA,aACG;AAEH,MAAI,eAAeA,IAAE,GAAG,OAAO;AAG/B,MAAI,eAAe,KAAK,GAAG,OAAO;AAClC,MAAI,eAAe,QAAQ,GAAG,OAAO;AACvC,GAZsC;AAoBtC,IAAM,SAAS,sCACbA,MACA,MACA,sBACuB;AACvB,cAAY;AAEZ,QAAM,YAAY,qBAAqB,IAAI;AAC3C,SAAO,UAAU;AAEjB,QAAM,SAAmB,UAAU;AACnC,MAAI,MAAM,MAAM;AAGhB,MAAI,KAAK,UAAU,QAAQ,eAAe,iBAAiB;AACzD,WAAO;AAAA,EACT;AAEA,QAAM,aAAa,MAAMA;AACzB,QAAM,WAAW,MAAMA;AACvB,QAAM,oBAAoB,MAAM;AAChC,QAAM,iBAAiB,MAAMA;AAC7B,QAAM,0BAA0B,MAAM;AAEtC,QAAM,qBAAqB,6BAAM;AAG/B,UAAM,qBAAqB,cAAc,oBAAoB;AAC7D,UAAM,OAAO,OAAO,kBAAkB,EAAE,KAAK;AAC7C,QAAI,QAAQ,YAAY,MAAM;AAC5B,WAAK,OAAO;AAAA,IACd;AAAA,EACF,GAR2B;AAU3B,MAAI,OAAY,OAAO,MAAM;AAE7B,QAAM,cAAc,OAAO,kBAAkB;AAC7C,QAAM,uBAAuB,OAAO,kBAAkB;AAEtD,QAAM,aAAa,OAAO;AAM1B,MAAI,yBAAyB,QAAW;AACtC,QAAI,sBAAsB;AACxB,2BAAqB,YAAY;AAAA,IACnC;AAEA,QAAI,aAAa;AAEf,YAAM,SAAS,gBAAgB,OAAO,oBAAoB,GAAG,QAAQ;AACrE,aAAO,OAAO,OAAO,MAAM,EAAE,CAAC,EAAG,gBAAiB,IAAI;AACtD,WAAK,KAAK,EAAE,MAAM,SAAS;AAAA,IAC7B,OAAO;AACL,aAAO,OAAO,oBAAoB;AAAA,IACpC;AACA,kBAAc,MAAMA,MAAI,gBAAgB,gBAAgB,UAAU,IAAI,eAAe;AAAA,EACvF,OAAO;AAIL,2BAAuB,UAAUA,MAAI,gBAAgB,QAAQ;AAK7D,QAAI,aAAa;AAEf,YAAM,SAAS,gBAAgB,OAAO,MAAM,GAAG,QAAQ;AACvD,aAAO,OAAO,OAAO,MAAM,EAAE,CAAC,EAAG,gBAAiB,IAAI;AACtD,WAAK,KAAK,EAAE,MAAM,SAAS;AAAA,IAC7B,OAAO;AACL,aAAO,OAAO,MAAM;AAAA,IACtB;AAEA,kBAAc,MAAMA,MAAI,cAAc;AAAA,EACxC;AAMA,MAAI;AACJ,MAAI;AAEJ,MAAI;AACF,WAAO,MAAM,QAAQ,SAAS,MAAM,EAAE,OAAO,UAAU,MAAM,CAAC;AAAA,EAChE,SAAS,OAAO;AACd,QAAI,OAAO,wBAAwB;AACjC,yBAAmB;AACnB,YAAM;AAAA,IACR;AACA,WAAO,MAAM,QAAQ,SAAS,OAAO;AACrC,gCAA4B;AAAA,EAC9B;AAGA,QAAM,UAAU,KAAK,OAAO,uBAAuB,EAAE,KAAK;AAC1D,QAAM,cAAc,KAAK;AAMzB,QAAM,MAAM,QAAQ;AACpB,QAAM,aAAa,IAAI;AACvB,QAAM,mBAAmB,KAAK,SAAS,aAAa,MAAM,IAAI;AAE9D,QAAM,QAAQ,iBAAiB,QAAQ,aAAa,kBAAkB,UAAU;AAEhF,QAAM,SAAS,SAAS,cAAc,OAAO;AAC7C,SAAO,YAAY;AACnB,MAAI,aAAa,QAAQ,UAAU;AAInC,MAAI;AACF,UAAM,KAAK,SAAS,KAAK,MAAMA,MAAI,gBAAY,SAAS,IAAI;AAAA,EAC9D,SAAS,GAAG;AACV,QAAI,OAAO,wBAAwB;AACjC,yBAAmB;AAAA,IACrB,OAAO;AACL,4BAAc,KAAK,MAAMA,MAAI,gBAAY,OAAO;AAAA,IAClD;AACA,UAAM;AAAA,EACR;AAGA,QAAM,UAAU,KAAK,OAAO,GAAG,uBAAuB,MAAM;AAC5D,QAAM,YAAgC,KAAK,GAAG,cAAc;AAC5D,QAAM,YAAgC,KAAK,GAAG,oBAAoB;AAClE,cAAY,aAAa,SAAS,WAAW,SAAS;AAGtD,OAAK,OAAO,QAAQA,IAAE,IAAI,EAAE,UAAU,mBAAmB,EAAE,KAAK,SAAS,eAAe;AAGxF,MAAI,UAAkB,KAAK,OAAO,uBAAuB,EAAE,KAAK,EAAE;AAElE,MAAI,MAAM,8BAA8B,OAAO,mBAAmB;AAClE,YAAU,eAAe,SAAS,aAAa,SAAS,OAAO,mBAAmB,CAAC;AAEnF,MAAI,aAAa;AACf,UAAM,QAAQ,KAAK,OAAO,0BAA0B,MAAM,EAAE,KAAK;AACjE,cAAU,cAAc,SAAS,KAAK;AAAA,EACxC,WAAW,CAAC,sBAAsB;AAEhC,cAAU,UAAU,SAAS,SAAS;AAAA,MACpC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,yBAAyB,EAAE,eAAe,KAAK;AAAA,IACjD,CAAC;AAAA,EACH;AAEA,kBAAgB;AAEhB,MAAI,2BAA2B;AAC7B,UAAM;AAAA,EACR;AAEA,qBAAmB;AAEnB,SAAO;AAAA,IACL;AAAA,IACA,KAAK;AAAA,IACL,eAAe,KAAK,GAAG;AAAA,EACzB;AACF,GAzKe;AA8Kf,SAAS,WAAW,cAA6B,CAAC,GAAG;AACnD,QAAM,UAAyB,wBAAgB,CAAC,GAAG,WAAW;AAE9D,MAAI,SAAS,cAAc,CAAC,QAAQ,gBAAgB,YAAY;AAC9D,QAAI,CAAC,QAAQ,gBAAgB;AAC3B,cAAQ,iBAAiB,CAAC;AAAA,IAC5B;AACA,YAAQ,eAAe,aAAa,QAAQ;AAAA,EAC9C;AAGA,EAAU,yBAAyB,OAAO;AAE1C,MAAI,SAAS,SAAS,QAAQ,SAAS,gBAAO;AAE5C,YAAQ,iBAAiB,eAAM,QAAQ,KAA2B,EAAE;AAAA,MAClE,QAAQ;AAAA,IACV;AAAA,EACF,WAAW,SAAS;AAClB,YAAQ,iBAAiB,eAAM,QAAQ,kBAAkB,QAAQ,cAAc;AAAA,EACjF;AAEA,QAAM,SACJ,OAAO,YAAY,WAAqB,cAAc,OAAO,IAAc,cAAc;AAE3F,cAAY,OAAO,QAAQ;AAC3B,cAAY;AACd;AA3BS;AA6BT,IAAM,qBAAqB,wBAAC,MAAc,WAA2C,CAAC,MAAM;AAC1F,QAAM,EAAE,KAAK,IAAI,kBAAkB,IAAI;AACvC,SAAO,QAAQ,SAAS,MAAM,QAAQ;AACxC,GAH2B;AAa3B,SAAS,YACP,aACA,SACA,WACA,WACM;AACN,qBAAmB,SAAS,WAAW;AACvC,6BAA2B,SAAS,WAAW,WAAW,QAAQ,KAAK,IAAI,CAAC;AAC9E;AARS;AAaF,IAAM,aAAa,OAAO,OAAO;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,6BAAM;AACX,IAAU,MAAM;AAAA,EAClB,GAFO;AAAA,EAGP,aAAa,6BAAM;AACjB,IAAU,MAAgB,aAAa;AAAA,EACzC,GAFa;AAAA,EAGb;AACF,CAAC;AAED,YAAsB,UAAU,EAAE,QAAQ;AAChC,MAAgB,UAAU,CAAC;;;AhC7erC,IAAM,cAAc,wBAAC,OAAgB,QAAyB,eAAoC;AAChG,MAAI,KAAK,KAAK;AACd,MAAI,gBAAgB,KAAK,GAAG;AAG1B,QAAI,YAAY;AACd,iBAAW,MAAM,KAAK,MAAM,IAAI;AAAA,IAClC;AACA,WAAO,KAAK,EAAE,GAAG,OAAO,SAAS,MAAM,KAAK,MAAM,CAAC;AAAA,EACrD,OAAO;AAEL,QAAI,YAAY;AACd,iBAAW,KAAK;AAAA,IAClB;AACA,QAAI,iBAAiB,OAAO;AAC1B,aAAO,KAAK;AAAA,QACV,KAAK,MAAM;AAAA,QACX,SAAS,MAAM;AAAA,QACf,MAAM,MAAM;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,GAvBoB;AA6CpB,IAAM,MAAM,sCACV,UAAsB;AAAA,EACpB,eAAe;AACjB,GACA;AACA,MAAI;AACF,UAAM,gBAAgB,OAAO;AAAA,EAC/B,SAAS,GAAG;AACV,QAAI,gBAAgB,CAAC,GAAG;AACtB,UAAI,MAAM,EAAE,GAAG;AAAA,IACjB;AACA,QAAI,QAAQ,YAAY;AACtB,cAAQ,WAAW,CAAW;AAAA,IAChC;AACA,QAAI,CAAC,QAAQ,gBAAgB;AAC3B,UAAI,MAAM,wDAAwD;AAClE,YAAM;AAAA,IACR;AAAA,EACF;AACF,GAnBY;AAqBZ,IAAM,kBAAkB,sCACtB,EAAE,oBAAoB,eAAe,MAAM,IAAwC;AAAA,EACjF,eAAe;AACjB,GACA;AACA,QAAM,OAAO,WAAW,UAAU;AAElC,MAAI,MAAM,GAAG,CAAC,qBAAqB,QAAQ,EAAE,yBAAyB;AAEtE,MAAI;AACJ,MAAI,OAAO;AACT,qBAAiB;AAAA,EACnB,WAAW,eAAe;AACxB,qBAAiB,SAAS,iBAAiB,aAAa;AAAA,EAC1D,OAAO;AACL,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC9D;AAEA,MAAI,MAAM,SAAS,eAAe,MAAM,WAAW;AACnD,MAAI,MAAM,gBAAgB,QAAW;AACnC,QAAI,MAAM,oBAAoB,MAAM,WAAW;AAC/C,eAAW,iBAAiB,EAAE,aAAa,MAAM,YAAY,CAAC;AAAA,EAChE;AAGA,QAAM,cAAc,IAAI,cAAM,gBAAgB,KAAK,kBAAkB,KAAK,mBAAmB;AAE7F,MAAI;AACJ,QAAM,SAA0B,CAAC;AAIjC,aAAW,WAAW,MAAM,KAAK,cAAc,GAAG;AAChD,QAAI,KAAK,wBAAwB,QAAQ,EAAE;AAE3C,QAAI,QAAQ,aAAa,gBAAgB,GAAG;AAC1C;AAAA,IACF;AACA,YAAQ,aAAa,kBAAkB,MAAM;AAE7C,UAAMC,OAAK,WAAW,YAAY,KAAK,CAAC;AAGxC,UAAM,QAAQ;AAGd,UAAM,OAAO,cAAM,aAAa,GAAG,CAAC,EACjC,KAAK,EACL,QAAQ,gBAAgB,OAAO;AAElC,UAAMC,QAAO,cAAM,WAAW,GAAG;AACjC,QAAIA,OAAM;AACR,UAAI,MAAM,2BAA2BA,KAAI;AAAA,IAC3C;AACA,QAAI;AACF,YAAM,EAAE,KAAK,cAAc,IAAI,MAAMC,QAAOF,MAAI,KAAK,OAAO;AAC5D,cAAQ,YAAY;AACpB,UAAI,oBAAoB;AACtB,cAAM,mBAAmBA,IAAE;AAAA,MAC7B;AACA,UAAI,eAAe;AACjB,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF,SAAS,OAAO;AACd,kBAAY,OAAO,QAAQ,QAAQ,UAAU;AAAA,IAC/C;AAAA,EACF;AACA,MAAI,OAAO,SAAS,GAAG;AAErB,UAAM,OAAO,CAAC;AAAA,EAChB;AACF,GAvEwB;AA+ExB,IAAMG,cAAa,gCAAU,QAAuB;AAClD,aAAW,WAAW,MAAM;AAC9B,GAFmB;AAkBnB,IAAM,OAAO,sCACX,QACA,OACA,UACA;AACA,MAAI,KAAK,qDAAqD;AAC9D,MAAI,QAAQ;AACV,IAAAA,YAAW,MAAM;AAAA,EACnB;AACA,QAAM,aAAyB,EAAE,oBAAoB,UAAU,eAAe,WAAW;AACzF,MAAI,OAAO,UAAU,UAAU;AAC7B,eAAW,gBAAgB;AAAA,EAC7B,WAAW,OAAO;AAChB,QAAI,iBAAiB,aAAa;AAChC,iBAAW,QAAQ,CAAC,KAAK;AAAA,IAC3B,OAAO;AACL,iBAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,QAAM,IAAI,UAAU;AACtB,GApBa;AA2Bb,IAAM,2BAA2B,8BAC/B,UACA;AAAA,EACE,WAAW;AACb,IAEI,CAAC,MACF;AACH,cAAY;AACZ,6BAA2B,GAAG,QAAQ;AACtC,MAAI,aAAa,OAAO;AACtB,UAAM,uBAAuB;AAAA,EAC/B;AACF,GAbiC;AAoBjC,IAAM,gBAAgB,kCAAY;AAChC,MAAI,QAAQ,aAAa;AACvB,UAAM,EAAE,YAAY,IAAI,WAAW,UAAU;AAC7C,QAAI,aAAa;AACf,cAAQ,IAAI,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,gCAAgC,GAAG,CAAC;AAAA,IAC7E;AAAA,EACF;AACF,GAPsB;AAStB,IAAI,OAAO,aAAa,aAAa;AAInC,SAAO,iBAAiB,QAAQ,eAAe,KAAK;AACtD;AAgBA,IAAM,uBAAuB,gCAAU,mBAAkD;AACvF,UAAQ,aAAa;AACvB,GAF6B;AAI7B,IAAM,iBAA6C,CAAC;AACpD,IAAI,wBAAwB;AAC5B,IAAM,eAAe,mCAAY;AAC/B,MAAI,uBAAuB;AACzB;AAAA,EACF;AACA,0BAAwB;AACxB,SAAO,eAAe,SAAS,GAAG;AAChC,UAAM,IAAI,eAAe,MAAM;AAC/B,QAAI,GAAG;AACL,UAAI;AACF,cAAM,EAAE;AAAA,MACV,SAAS,GAAG;AACV,YAAI,MAAM,yBAAyB,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACA,0BAAwB;AAC1B,GAhBqB;AAqCrB,IAAMC,SAAiC,8BAAO,MAAM,iBAAiB;AACnE,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAGtC,UAAM,cAAc,6BAClB,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACxB,iBAAW,MAAM,MAAM,YAAY,EAAE;AAAA,QACnC,CAAC,MAAM;AAEL,cAAI,CAAC;AAEL,kBAAQ,CAAC;AAAA,QACX;AAAA,QACA,CAAC,MAAM;AACL,cAAI,MAAM,iBAAiB,CAAC;AAC5B,kBAAQ,aAAa,CAAC;AACtB,cAAI,CAAC;AACL,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC,GAhBiB;AAiBpB,mBAAe,KAAK,WAAW;AAC/B,iBAAa,EAAE,MAAM,MAAM;AAAA,EAC7B,CAAC;AACH,GAxBuC;AAiDvC,IAAMF,UAAmC,wBAACF,MAAI,MAAM,cAAc;AAChE,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAGtC,UAAM,cAAc,6BAClB,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACxB,iBAAW,OAAOA,MAAI,MAAM,SAAS,EAAE;AAAA,QACrC,CAAC,MAAM;AAEL,cAAI,CAAC;AAEL,kBAAQ,CAAC;AAAA,QACX;AAAA,QACA,CAAC,MAAM;AACL,cAAI,MAAM,iBAAiB,CAAC;AAC5B,kBAAQ,aAAa,CAAC;AACtB,cAAI,CAAC;AACL,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC,GAhBiB;AAiBpB,mBAAe,KAAK,WAAW;AAC/B,iBAAa,EAAE,MAAM,MAAM;AAAA,EAC7B,CAAC;AACH,GAxByC;AAkDzC,IAAM,UAAmB;AAAA,EACvB,aAAa;AAAA,EACb;AAAA,EACA,OAAAI;AAAA,EACA,QAAAF;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAC;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAO,kBAAQ;", "names": ["diagram", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "id", "detector", "loader", "diagram", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "id", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "plugin", "detector_default", "id", "detector", "loader", "diagram", "plugin", "detector_default", "id", "detector", "loader", "diagram", "plugin", "detector_default", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "id", "detector", "loader", "diagram", "id", "detector", "loader", "diagram", "plugin", "id", "detector", "loader", "diagram", "id", "detector", "loader", "diagram", "detector_default", "detector", "loader", "diagram", "id", "renderer", "loader", "id", "diagram", "init", "diagram", "id", "id", "init", "render", "initialize", "parse"]}