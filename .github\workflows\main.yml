# .github/workflows/release.yml
name: Build Desktop App (Python Backend + Tauri Frontend)

on:
  push:
    tags:
      - 'v*' # 发布 tag 时触发
  workflow_dispatch:
jobs:
  build:
    strategy:
      matrix:
        platform: [macos-latest, windows-latest]

    runs-on: ${{ matrix.platform }}

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      # 设置 Python 环境
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      # 安装 Python 依赖并执行你的 build.sh
      - name: Install Python dependencies & Build backend
        shell: bash
        run: |
          python -m pip install --upgrade pip
          pip install -r backend/requirements.txt

          if [ "$RUNNER_OS" = "Windows" ]; then
            backend\\build.bat
          else
            chmod +x backend/build.sh
            ./backend/build.sh
          fi

      # 设置 Node 环境 + 安装前端依赖
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Enable Corepack + Install pnpm
        working-directory: BillNote_frontend
        run: |
          corepack enable
          pnpm install 

      # 设置 Rust 环境
      - name: Set up Rust
        uses: dtolnay/rust-toolchain@stable

      # 打包 Tauri 应用
      - name: Build Tauri App
        working-directory: BillNote_frontend
        run: pnpm tauri build

      # 可选：上传构建产物
      - name: Upload Desktop Bundle
        uses: actions/upload-artifact@v4
        with:
          name: app-${{ matrix.platform }}
          path: BillNote_frontend/src-tauri/target/release/bundle/
