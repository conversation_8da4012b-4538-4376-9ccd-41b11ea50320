{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-K7TBWW4H.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  CommonValueConverter,\n  MermaidGeneratedSharedModule,\n  RadarGeneratedModule,\n  __name\n} from \"./chunk-ORCS5NZH.mjs\";\n\n// src/language/radar/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/radar/tokenBuilder.ts\nvar RadarTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"RadarTokenBuilder\");\n  }\n  constructor() {\n    super([\"radar-beta\"]);\n  }\n};\n\n// src/language/radar/module.ts\nvar RadarModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new RadarTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createRadarServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Radar = inject(\n    createDefaultCoreModule({ shared }),\n    RadarGeneratedModule,\n    RadarModule\n  );\n  shared.ServiceRegistry.register(Radar);\n  return { shared, Radar };\n}\n__name(createRadarServices, \"createRadarServices\");\n\nexport {\n  RadarModule,\n  createRadarServices\n};\n"], "mappings": "4IAiBA,IAAIA,EAAoB,cAAcC,CAA4B,CAjBlE,MAiBkE,CAAAC,EAAA,0BAChE,MAAO,CACLA,EAAO,KAAM,mBAAmB,CAClC,CACA,aAAc,CACZ,MAAM,CAAC,YAAY,CAAC,CACtB,CACF,EAGIC,EAAc,CAChB,OAAQ,CACN,aAA8BD,EAAO,IAAM,IAAIF,EAAqB,cAAc,EAClF,eAAgCE,EAAO,IAAM,IAAIE,EAAwB,gBAAgB,CAC3F,CACF,EACA,SAASC,EAAoBC,EAAUC,EAAiB,CACtD,IAAMC,EAASC,EACbC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAAQH,EACZI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAX,CACF,EACA,OAAAK,EAAO,gBAAgB,SAASI,CAAK,EAC9B,CAAE,OAAAJ,EAAQ,MAAAI,CAAM,CACzB,CAZSV,EAAAG,EAAA,uBAaTH,EAAOG,EAAqB,qBAAqB", "names": ["RadarTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "RadarModule", "CommonValueConverter", "createRadarServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Radar", "createDefaultCoreModule", "RadarGeneratedModule"]}