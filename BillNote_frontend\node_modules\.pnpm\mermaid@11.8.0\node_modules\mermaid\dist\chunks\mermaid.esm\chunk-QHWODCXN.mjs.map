{"version": 3, "sources": ["../../../src/diagrams/state/parser/stateDiagram.jison", "../../../src/diagrams/state/stateCommon.ts", "../../../src/diagrams/state/stateRenderer-v3-unified.ts", "../../../src/diagrams/state/dataFetcher.ts", "../../../src/diagrams/state/stateDb.ts", "../../../src/diagrams/state/styles.js"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,2],$V1=[1,3],$V2=[1,4],$V3=[2,4],$V4=[1,9],$V5=[1,11],$V6=[1,16],$V7=[1,17],$V8=[1,18],$V9=[1,19],$Va=[1,33],$Vb=[1,20],$Vc=[1,21],$Vd=[1,22],$Ve=[1,23],$Vf=[1,24],$Vg=[1,26],$Vh=[1,27],$Vi=[1,28],$Vj=[1,29],$Vk=[1,30],$Vl=[1,31],$Vm=[1,32],$Vn=[1,35],$Vo=[1,36],$Vp=[1,37],$Vq=[1,38],$Vr=[1,34],$Vs=[1,4,5,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57],$Vt=[1,4,5,14,15,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,39,40,41,45,48,51,52,53,54,57],$Vu=[4,5,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"SPACE\":4,\"NL\":5,\"SD\":6,\"document\":7,\"line\":8,\"statement\":9,\"classDefStatement\":10,\"styleStatement\":11,\"cssClassStatement\":12,\"idStatement\":13,\"DESCR\":14,\"-->\":15,\"HIDE_EMPTY\":16,\"scale\":17,\"WIDTH\":18,\"COMPOSIT_STATE\":19,\"STRUCT_START\":20,\"STRUCT_STOP\":21,\"STATE_DESCR\":22,\"AS\":23,\"ID\":24,\"FORK\":25,\"JOIN\":26,\"CHOICE\":27,\"CONCURRENT\":28,\"note\":29,\"notePosition\":30,\"NOTE_TEXT\":31,\"direction\":32,\"acc_title\":33,\"acc_title_value\":34,\"acc_descr\":35,\"acc_descr_value\":36,\"acc_descr_multiline_value\":37,\"CLICK\":38,\"STRING\":39,\"HREF\":40,\"classDef\":41,\"CLASSDEF_ID\":42,\"CLASSDEF_STYLEOPTS\":43,\"DEFAULT\":44,\"style\":45,\"STYLE_IDS\":46,\"STYLEDEF_STYLEOPTS\":47,\"class\":48,\"CLASSENTITY_IDS\":49,\"STYLECLASS\":50,\"direction_tb\":51,\"direction_bt\":52,\"direction_rl\":53,\"direction_lr\":54,\"eol\":55,\";\":56,\"EDGE_STATE\":57,\"STYLE_SEPARATOR\":58,\"left_of\":59,\"right_of\":60,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"SPACE\",5:\"NL\",6:\"SD\",14:\"DESCR\",15:\"-->\",16:\"HIDE_EMPTY\",17:\"scale\",18:\"WIDTH\",19:\"COMPOSIT_STATE\",20:\"STRUCT_START\",21:\"STRUCT_STOP\",22:\"STATE_DESCR\",23:\"AS\",24:\"ID\",25:\"FORK\",26:\"JOIN\",27:\"CHOICE\",28:\"CONCURRENT\",29:\"note\",31:\"NOTE_TEXT\",33:\"acc_title\",34:\"acc_title_value\",35:\"acc_descr\",36:\"acc_descr_value\",37:\"acc_descr_multiline_value\",38:\"CLICK\",39:\"STRING\",40:\"HREF\",41:\"classDef\",42:\"CLASSDEF_ID\",43:\"CLASSDEF_STYLEOPTS\",44:\"DEFAULT\",45:\"style\",46:\"STYLE_IDS\",47:\"STYLEDEF_STYLEOPTS\",48:\"class\",49:\"CLASSENTITY_IDS\",50:\"STYLECLASS\",51:\"direction_tb\",52:\"direction_bt\",53:\"direction_rl\",54:\"direction_lr\",56:\";\",57:\"EDGE_STATE\",58:\"STYLE_SEPARATOR\",59:\"left_of\",60:\"right_of\"},\nproductions_: [0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,3],[9,4],[9,1],[9,2],[9,1],[9,4],[9,3],[9,6],[9,1],[9,1],[9,1],[9,1],[9,4],[9,4],[9,1],[9,2],[9,2],[9,1],[9,5],[9,5],[10,3],[10,3],[11,3],[12,3],[32,1],[32,1],[32,1],[32,1],[55,1],[55,1],[13,1],[13,1],[13,3],[13,3],[30,1],[30,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 3:\n /* console.log('--> Root document', $$[$0]); */   yy.setRootDoc($$[$0]); return $$[$0]; \nbreak;\ncase 4:\n /*console.log('empty document'); */ this.$ = [] \nbreak;\ncase 5:\n\n        if($$[$0] !='nl'){\n            /* console.log(' document: 1: ', $$[$0-1], ' pushing 2: ', $$[$0]); */\n            $$[$0-1].push($$[$0]); this.$ = $$[$0-1]\n        }\n        /* console.log('Got document',$$[$0-1], $$[$0]); */\n    \nbreak;\ncase 6: case 7:\n this.$ = $$[$0] \nbreak;\ncase 8:\n this.$='nl';\nbreak;\ncase 12:\n /* console.log('got id', $$[$0]); */\n            this.$=$$[$0];\n\t    \nbreak;\ncase 13:\n\n            const stateStmt = $$[$0-1];\n            stateStmt.description = yy.trimColon($$[$0]);\n            this.$ = stateStmt;\n\t    \nbreak;\ncase 14:\n\n            /* console.info('got ids: 1: ', $$[$0-2], ' 2:', $$[$0-1],'  3: ', $$[$0]); */\n            // console.log(' idStatement --> idStatement : state1 =', $$[$0-2], ' state2 =', $$[$0]);\n            this.$={ stmt: 'relation', state1: $$[$0-2], state2: $$[$0]};\n        \nbreak;\ncase 15:\n\n            const relDescription = yy.trimColon($$[$0]);\n            /* console.log(' idStatement --> idStatement DESCR : state1 =', $$[$0-3], ' state2stmt =', $$[$0-1], '  description: ', relDescription); */\n            this.$={ stmt: 'relation', state1: $$[$0-3], state2: $$[$0-1], description: relDescription};\n        \nbreak;\ncase 19:\n\n        // console.log('Adding document for state without id ', $$[$0-3]);\n        this.$={ stmt: 'state', id: $$[$0-3], type: 'default', description: '', doc: $$[$0-1] }\n    \nbreak;\ncase 20:\n\n        var id=$$[$0];\n        var description = $$[$0-2].trim();\n        if($$[$0].match(':')){\n            var parts = $$[$0].split(':');\n            id=parts[0];\n            description = [description, parts[1]];\n        }\n        this.$={stmt: 'state', id: id, type: 'default', description: description};\n\n    \nbreak;\ncase 21:\n\n         // console.log('state with id ', $$[$0-3],' document = ', $$[$0-1], );\n         this.$={ stmt: 'state', id: $$[$0-3], type: 'default', description: $$[$0-5], doc: $$[$0-1] }\n    \nbreak;\ncase 22:\n\n        this.$={ stmt: 'state', id: $$[$0], type: 'fork' }\n    \nbreak;\ncase 23:\n\n        this.$={ stmt: 'state', id: $$[$0], type: 'join' }\n    \nbreak;\ncase 24:\n\n        this.$={ stmt: 'state', id: $$[$0], type: 'choice' }\n    \nbreak;\ncase 25:\n\n        this.$={ stmt: 'state', id: yy.getDividerId(), type: 'divider' }\n    \nbreak;\ncase 26:\n\n        /* console.warn('got NOTE, position: ', $$[$0-2].trim(), 'id = ', $$[$0-1].trim(), 'note: ', $$[$0]);*/\n        this.$={ stmt: 'state', id: $$[$0-1].trim(), note:{position: $$[$0-2].trim(), text: $$[$0].trim()}};\n    \nbreak;\ncase 29:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 30: case 31:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 32:\n\n        this.$ = {\n            stmt: \"click\",\n            id: $$[$0-3],\n            url: $$[$0-2],\n            tooltip: $$[$0-1]\n        };\n    \nbreak;\ncase 33:\n\n        this.$ = {\n            stmt: \"click\",\n            id: $$[$0-3],\n            url: $$[$0-1],\n            tooltip: \"\"\n        };\n    \nbreak;\ncase 34: case 35:\n\n        this.$ = { stmt: 'classDef', id: $$[$0-1].trim(), classes: $$[$0].trim() };\n        \nbreak;\ncase 36:\n\n        this.$ = { stmt: 'style', id: $$[$0-1].trim(), styleClass: $$[$0].trim() };\n        \nbreak;\ncase 37:\n\n        //console.log('apply class: id(s): ',$$[$0-1], '  style class: ', $$[$0]);\n        this.$={ stmt: 'applyClass', id: $$[$0-1].trim(), styleClass: $$[$0].trim() };\n        \nbreak;\ncase 38:\n yy.setDirection('TB');this.$={stmt:'dir', value:'TB'};\nbreak;\ncase 39:\n yy.setDirection('BT');this.$={stmt:'dir', value:'BT'};\nbreak;\ncase 40:\n yy.setDirection('RL'); this.$={stmt:'dir', value:'RL'};\nbreak;\ncase 41:\n yy.setDirection('LR');this.$={stmt:'dir', value:'LR'};\nbreak;\ncase 44: case 45:\n   /* console.log('idStatement id: ', $$[$0]); */\n            this.$={ stmt: 'state', id: $$[$0].trim(), type: 'default', description: '' };\n        \nbreak;\ncase 46:\n   /*console.log('idStatement ID STYLE_SEPARATOR ID'); */\n            this.$={ stmt: 'state', id: $$[$0-2].trim(), classes: [$$[$0].trim()], type: 'default', description: '' };\n        \nbreak;\ncase 47:\n   /*console.log('idStatement EDGE_STATE STYLE_SEPARATOR ID'); */\n            this.$={ stmt: 'state', id: $$[$0-2].trim(), classes: [$$[$0].trim()], type: 'default', description: '' };\n        \nbreak;\n}\n},\ntable: [{3:1,4:$V0,5:$V1,6:$V2},{1:[3]},{3:5,4:$V0,5:$V1,6:$V2},{3:6,4:$V0,5:$V1,6:$V2},o([1,4,5,16,17,19,22,24,25,26,27,28,29,33,35,37,38,41,45,48,51,52,53,54,57],$V3,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:$V4,5:$V5,8:8,9:10,10:12,11:13,12:14,13:15,16:$V6,17:$V7,19:$V8,22:$V9,24:$Va,25:$Vb,26:$Vc,27:$Vd,28:$Ve,29:$Vf,32:25,33:$Vg,35:$Vh,37:$Vi,38:$Vj,41:$Vk,45:$Vl,48:$Vm,51:$Vn,52:$Vo,53:$Vp,54:$Vq,57:$Vr},o($Vs,[2,5]),{9:39,10:12,11:13,12:14,13:15,16:$V6,17:$V7,19:$V8,22:$V9,24:$Va,25:$Vb,26:$Vc,27:$Vd,28:$Ve,29:$Vf,32:25,33:$Vg,35:$Vh,37:$Vi,38:$Vj,41:$Vk,45:$Vl,48:$Vm,51:$Vn,52:$Vo,53:$Vp,54:$Vq,57:$Vr},o($Vs,[2,7]),o($Vs,[2,8]),o($Vs,[2,9]),o($Vs,[2,10]),o($Vs,[2,11]),o($Vs,[2,12],{14:[1,40],15:[1,41]}),o($Vs,[2,16]),{18:[1,42]},o($Vs,[2,18],{20:[1,43]}),{23:[1,44]},o($Vs,[2,22]),o($Vs,[2,23]),o($Vs,[2,24]),o($Vs,[2,25]),{30:45,31:[1,46],59:[1,47],60:[1,48]},o($Vs,[2,28]),{34:[1,49]},{36:[1,50]},o($Vs,[2,31]),{13:51,24:$Va,57:$Vr},{42:[1,52],44:[1,53]},{46:[1,54]},{49:[1,55]},o($Vt,[2,44],{58:[1,56]}),o($Vt,[2,45],{58:[1,57]}),o($Vs,[2,38]),o($Vs,[2,39]),o($Vs,[2,40]),o($Vs,[2,41]),o($Vs,[2,6]),o($Vs,[2,13]),{13:58,24:$Va,57:$Vr},o($Vs,[2,17]),o($Vu,$V3,{7:59}),{24:[1,60]},{24:[1,61]},{23:[1,62]},{24:[2,48]},{24:[2,49]},o($Vs,[2,29]),o($Vs,[2,30]),{39:[1,63],40:[1,64]},{43:[1,65]},{43:[1,66]},{47:[1,67]},{50:[1,68]},{24:[1,69]},{24:[1,70]},o($Vs,[2,14],{14:[1,71]}),{4:$V4,5:$V5,8:8,9:10,10:12,11:13,12:14,13:15,16:$V6,17:$V7,19:$V8,21:[1,72],22:$V9,24:$Va,25:$Vb,26:$Vc,27:$Vd,28:$Ve,29:$Vf,32:25,33:$Vg,35:$Vh,37:$Vi,38:$Vj,41:$Vk,45:$Vl,48:$Vm,51:$Vn,52:$Vo,53:$Vp,54:$Vq,57:$Vr},o($Vs,[2,20],{20:[1,73]}),{31:[1,74]},{24:[1,75]},{39:[1,76]},{39:[1,77]},o($Vs,[2,34]),o($Vs,[2,35]),o($Vs,[2,36]),o($Vs,[2,37]),o($Vt,[2,46]),o($Vt,[2,47]),o($Vs,[2,15]),o($Vs,[2,19]),o($Vu,$V3,{7:78}),o($Vs,[2,26]),o($Vs,[2,27]),{5:[1,79]},{5:[1,80]},{4:$V4,5:$V5,8:8,9:10,10:12,11:13,12:14,13:15,16:$V6,17:$V7,19:$V8,21:[1,81],22:$V9,24:$Va,25:$Vb,26:$Vc,27:$Vd,28:$Ve,29:$Vf,32:25,33:$Vg,35:$Vh,37:$Vi,38:$Vj,41:$Vk,45:$Vl,48:$Vm,51:$Vn,52:$Vo,53:$Vp,54:$Vq,57:$Vr},o($Vs,[2,32]),o($Vs,[2,33]),o($Vs,[2,21])],\ndefaultActions: {5:[2,1],6:[2,2],47:[2,48],48:[2,49]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:return 38;\nbreak;\ncase 1:return 40;\nbreak;\ncase 2:return 39;   \nbreak;\ncase 3:return 44;\nbreak;\ncase 4:return 51;\nbreak;\ncase 5:return 52;\nbreak;\ncase 6:return 53;\nbreak;\ncase 7:return 54;\nbreak;\ncase 8:/* skip comments */\nbreak;\ncase 9:/* skip comments */{ /*console.log('Crap after close');*/ }\nbreak;\ncase 10:return 5;\nbreak;\ncase 11:/* skip all whitespace */\nbreak;\ncase 12:/* skip same-line whitespace */\nbreak;\ncase 13:/* skip comments */\nbreak;\ncase 14:/* skip comments */\nbreak;\ncase 15: this.pushState('SCALE'); /* console.log('Got scale', yy_.yytext);*/ return 17; \nbreak;\ncase 16:return 18;\nbreak;\ncase 17: this.popState(); \nbreak;\ncase 18: this.begin(\"acc_title\");return 33; \nbreak;\ncase 19: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 20: this.begin(\"acc_descr\");return 35; \nbreak;\ncase 21: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 22: this.begin(\"acc_descr_multiline\"); \nbreak;\ncase 23: this.popState(); \nbreak;\ncase 24:return \"acc_descr_multiline_value\";\nbreak;\ncase 25: this.pushState('CLASSDEF'); return 41; \nbreak;\ncase 26: this.popState(); this.pushState('CLASSDEFID'); return 'DEFAULT_CLASSDEF_ID' \nbreak;\ncase 27: this.popState(); this.pushState('CLASSDEFID'); return 42 \nbreak;\ncase 28: this.popState(); return 43 \nbreak;\ncase 29: this.pushState('CLASS'); return 48; \nbreak;\ncase 30: this.popState(); this.pushState('CLASS_STYLE'); return 49 \nbreak;\ncase 31: this.popState(); return 50 \nbreak;\ncase 32: this.pushState('STYLE'); return 45; \nbreak;\ncase 33: this.popState(); this.pushState('STYLEDEF_STYLES'); return 46 \nbreak;\ncase 34: this.popState(); return 47 \nbreak;\ncase 35: this.pushState('SCALE'); /* console.log('Got scale', yy_.yytext);*/ return 17; \nbreak;\ncase 36:return 18;\nbreak;\ncase 37:this.popState();\nbreak;\ncase 38: /* console.log('Starting STATE '); */ this.pushState('STATE'); \nbreak;\ncase 39:this.popState();yy_.yytext=yy_.yytext.slice(0,-8).trim(); /*console.warn('Fork Fork: ',yy_.yytext);*/return 25;\nbreak;\ncase 40:this.popState();yy_.yytext=yy_.yytext.slice(0,-8).trim();/*console.warn('Fork Join: ',yy_.yytext);*/return 26;\nbreak;\ncase 41:this.popState();yy_.yytext=yy_.yytext.slice(0,-10).trim();/*console.warn('Fork Join: ',yy_.yytext);*/return 27;\nbreak;\ncase 42:this.popState();yy_.yytext=yy_.yytext.slice(0,-8).trim();/*console.warn('Fork Fork: ',yy_.yytext);*/return 25;\nbreak;\ncase 43:this.popState();yy_.yytext=yy_.yytext.slice(0,-8).trim();/*console.warn('Fork Join: ',yy_.yytext);*/return 26;\nbreak;\ncase 44:this.popState();yy_.yytext=yy_.yytext.slice(0,-10).trim();/*console.warn('Fork Join: ',yy_.yytext);*/return 27;\nbreak;\ncase 45: return 51;\nbreak;\ncase 46: return 52;\nbreak;\ncase 47: return 53;\nbreak;\ncase 48: return 54;\nbreak;\ncase 49: /* console.log('Starting STATE_STRING'); */ this.pushState(\"STATE_STRING\"); \nbreak;\ncase 50: this.pushState('STATE_ID'); /* console.log('pushState(STATE_ID)'); */ return \"AS\"; \nbreak;\ncase 51: this.popState(); /* console.log('STATE_ID', yy_.yytext); */ return \"ID\"; \nbreak;\ncase 52: this.popState(); \nbreak;\ncase 53: /* console.log('Long description:', yy_.yytext); */ return \"STATE_DESCR\"; \nbreak;\ncase 54: /* console.log('COMPOSIT_STATE', yy_.yytext); */ return 19; \nbreak;\ncase 55: this.popState(); \nbreak;\ncase 56: this.popState(); this.pushState('struct'); /* console.log('begin struct', yy_.yytext); */ return 20; \nbreak;\ncase 57:/* skip comments inside state*/\nbreak;\ncase 58: /*console.log('Ending struct');*/ this.popState(); return 21;\nbreak;\ncase 59:/* nothing */\nbreak;\ncase 60: this.begin('NOTE'); return 29; \nbreak;\ncase 61: this.popState(); this.pushState('NOTE_ID'); return 59; \nbreak;\ncase 62: this.popState(); this.pushState('NOTE_ID'); return 60; \nbreak;\ncase 63: this.popState(); this.pushState('FLOATING_NOTE'); \nbreak;\ncase 64: this.popState(); this.pushState('FLOATING_NOTE_ID'); return \"AS\"; \nbreak;\ncase 65:/**/\nbreak;\ncase 66: /* console.log('Floating note text: ', yy_.yytext); */ return \"NOTE_TEXT\"; \nbreak;\ncase 67: this.popState(); /* console.log('Floating note ID', yy_.yytext);*/ return \"ID\"; \nbreak;\ncase 68: this.popState(); this.pushState('NOTE_TEXT'); /*console.log('Got ID for note', yy_.yytext);*/ return 24; \nbreak;\ncase 69: this.popState(); /* console.log('Got NOTE_TEXT for note',yy_.yytext);*/yy_.yytext = yy_.yytext.substr(2).trim(); return 31; \nbreak;\ncase 70: this.popState(); /* console.log('Got NOTE_TEXT for note',yy_.yytext);*/yy_.yytext = yy_.yytext.slice(0,-8).trim(); return 31; \nbreak;\ncase 71: /* console.log('Got state diagram', yy_.yytext,'#'); */ return 6; \nbreak;\ncase 72: /* console.log('Got state diagram', yy_.yytext,'#'); */ return 6; \nbreak;\ncase 73: /* console.log('HIDE_EMPTY', yy_.yytext,'#'); */ return 16; \nbreak;\ncase 74: /* console.log('EDGE_STATE=',yy_.yytext); */ return 57; \nbreak;\ncase 75: /* console.log('=>ID=',yy_.yytext); */ return 24; \nbreak;\ncase 76: yy_.yytext = yy_.yytext.trim(); /* console.log('Descr = ', yy_.yytext); */ return 14; \nbreak;\ncase 77:return 15;\nbreak;\ncase 78:return 28;\nbreak;\ncase 79:return 58;\nbreak;\ncase 80:return 5;\nbreak;\ncase 81:return 'INVALID';\nbreak;\n}\n},\nrules: [/^(?:click\\b)/i,/^(?:href\\b)/i,/^(?:\"[^\"]*\")/i,/^(?:default\\b)/i,/^(?:.*direction\\s+TB[^\\n]*)/i,/^(?:.*direction\\s+BT[^\\n]*)/i,/^(?:.*direction\\s+RL[^\\n]*)/i,/^(?:.*direction\\s+LR[^\\n]*)/i,/^(?:%%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:[\\n]+)/i,/^(?:[\\s]+)/i,/^(?:((?!\\n)\\s)+)/i,/^(?:#[^\\n]*)/i,/^(?:%[^\\n]*)/i,/^(?:scale\\s+)/i,/^(?:\\d+)/i,/^(?:\\s+width\\b)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:classDef\\s+)/i,/^(?:DEFAULT\\s+)/i,/^(?:\\w+\\s+)/i,/^(?:[^\\n]*)/i,/^(?:class\\s+)/i,/^(?:(\\w+)+((,\\s*\\w+)*))/i,/^(?:[^\\n]*)/i,/^(?:style\\s+)/i,/^(?:[\\w,]+\\s+)/i,/^(?:[^\\n]*)/i,/^(?:scale\\s+)/i,/^(?:\\d+)/i,/^(?:\\s+width\\b)/i,/^(?:state\\s+)/i,/^(?:.*<<fork>>)/i,/^(?:.*<<join>>)/i,/^(?:.*<<choice>>)/i,/^(?:.*\\[\\[fork\\]\\])/i,/^(?:.*\\[\\[join\\]\\])/i,/^(?:.*\\[\\[choice\\]\\])/i,/^(?:.*direction\\s+TB[^\\n]*)/i,/^(?:.*direction\\s+BT[^\\n]*)/i,/^(?:.*direction\\s+RL[^\\n]*)/i,/^(?:.*direction\\s+LR[^\\n]*)/i,/^(?:[\"])/i,/^(?:\\s*as\\s+)/i,/^(?:[^\\n\\{]*)/i,/^(?:[\"])/i,/^(?:[^\"]*)/i,/^(?:[^\\n\\s\\{]+)/i,/^(?:\\n)/i,/^(?:\\{)/i,/^(?:%%(?!\\{)[^\\n]*)/i,/^(?:\\})/i,/^(?:[\\n])/i,/^(?:note\\s+)/i,/^(?:left of\\b)/i,/^(?:right of\\b)/i,/^(?:\")/i,/^(?:\\s*as\\s*)/i,/^(?:[\"])/i,/^(?:[^\"]*)/i,/^(?:[^\\n]*)/i,/^(?:\\s*[^:\\n\\s\\-]+)/i,/^(?:\\s*:[^:\\n;]+)/i,/^(?:[\\s\\S]*?end note\\b)/i,/^(?:stateDiagram\\s+)/i,/^(?:stateDiagram-v2\\s+)/i,/^(?:hide empty description\\b)/i,/^(?:\\[\\*\\])/i,/^(?:[^:\\n\\s\\-\\{]+)/i,/^(?:\\s*:[^:\\n;]+)/i,/^(?:-->)/i,/^(?:--)/i,/^(?::::)/i,/^(?:$)/i,/^(?:.)/i],\nconditions: {\"LINE\":{\"rules\":[12,13],\"inclusive\":false},\"struct\":{\"rules\":[12,13,25,29,32,38,45,46,47,48,57,58,59,60,74,75,76,77,78],\"inclusive\":false},\"FLOATING_NOTE_ID\":{\"rules\":[67],\"inclusive\":false},\"FLOATING_NOTE\":{\"rules\":[64,65,66],\"inclusive\":false},\"NOTE_TEXT\":{\"rules\":[69,70],\"inclusive\":false},\"NOTE_ID\":{\"rules\":[68],\"inclusive\":false},\"NOTE\":{\"rules\":[61,62,63],\"inclusive\":false},\"STYLEDEF_STYLEOPTS\":{\"rules\":[],\"inclusive\":false},\"STYLEDEF_STYLES\":{\"rules\":[34],\"inclusive\":false},\"STYLE_IDS\":{\"rules\":[],\"inclusive\":false},\"STYLE\":{\"rules\":[33],\"inclusive\":false},\"CLASS_STYLE\":{\"rules\":[31],\"inclusive\":false},\"CLASS\":{\"rules\":[30],\"inclusive\":false},\"CLASSDEFID\":{\"rules\":[28],\"inclusive\":false},\"CLASSDEF\":{\"rules\":[26,27],\"inclusive\":false},\"acc_descr_multiline\":{\"rules\":[23,24],\"inclusive\":false},\"acc_descr\":{\"rules\":[21],\"inclusive\":false},\"acc_title\":{\"rules\":[19],\"inclusive\":false},\"SCALE\":{\"rules\":[16,17,36,37],\"inclusive\":false},\"ALIAS\":{\"rules\":[],\"inclusive\":false},\"STATE_ID\":{\"rules\":[51],\"inclusive\":false},\"STATE_STRING\":{\"rules\":[52,53],\"inclusive\":false},\"FORK_STATE\":{\"rules\":[],\"inclusive\":false},\"STATE\":{\"rules\":[12,13,39,40,41,42,43,44,49,50,54,55,56],\"inclusive\":false},\"ID\":{\"rules\":[12,13],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7,8,9,10,11,13,14,15,18,20,22,25,29,32,35,38,56,60,71,72,73,74,75,76,77,79,80,81],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "/**\n * Constants common to all State Diagram code\n */\n\n// default diagram direction\nexport const DEFAULT_DIAGRAM_DIRECTION = 'TB';\n\n// default direction for any nested documents (composites)\nexport const DEFAULT_NESTED_DOC_DIR = 'TB';\n\n// parsed statement type for a direction\nexport const STMT_DIRECTION = 'dir';\n\n// parsed statement type for a state\nexport const STMT_STATE = 'state';\n\n// parsed statement type for a root\nexport const STMT_ROOT = 'root';\n\n// parsed statement type for a relation\nexport const STMT_RELATION = 'relation';\n// parsed statement type for a classDef\nexport const STMT_CLASSDEF = 'classDef';\nexport const STMT_STYLEDEF = 'style';\n// parsed statement type for applyClass\nexport const STMT_APPLYCLASS = 'applyClass';\n\nexport const DEFAULT_STATE_TYPE = 'default';\nexport const DIVIDER_TYPE = 'divider';\n\n// Graph edge settings\nexport const G_EDGE_STYLE = 'fill:none';\nexport const G_EDGE_ARROWHEADSTYLE = 'fill: #333';\nexport const G_EDGE_LABELPOS = 'c';\nexport const G_EDGE_LABELTYPE = 'text';\nexport const G_EDGE_THICKNESS = 'normal';\n\nexport const SHAPE_STATE = 'rect';\nexport const SHAPE_STATE_WITH_DESC = 'rectWithTitle';\nexport const SHAPE_START = 'stateStart';\nexport const SHAPE_END = 'stateEnd';\nexport const SHAPE_DIVIDER = 'divider';\nexport const SHAPE_GROUP = 'roundedWithTitle';\nexport const SHAPE_NOTE = 'note';\nexport const SHAPE_NOTEGROUP = 'noteGroup';\n\n// CSS classes\nexport const CSS_DIAGRAM = 'statediagram';\nexport const CSS_STATE = 'state';\nexport const CSS_DIAGRAM_STATE = `${CSS_DIAGRAM}-${CSS_STATE}`;\nexport const CSS_EDGE = 'transition';\nexport const CSS_NOTE = 'note';\nexport const CSS_NOTE_EDGE = 'note-edge';\nexport const CSS_EDGE_NOTE_EDGE = `${CSS_EDGE} ${CSS_NOTE_EDGE}`;\nexport const CSS_DIAGRAM_NOTE = `${CSS_DIAGRAM}-${CSS_NOTE}`;\nexport const CSS_CLUSTER = 'cluster';\nexport const CSS_DIAGRAM_CLUSTER = `${CSS_DIAGRAM}-${CSS_CLUSTER}`;\nexport const CSS_CLUSTER_ALT = 'cluster-alt';\nexport const CSS_DIAGRAM_CLUSTER_ALT = `${CSS_DIAGRAM}-${CSS_CLUSTER_ALT}`;\n\nexport const PARENT = 'parent';\nexport const NOTE = 'note';\nexport const DOMID_STATE = 'state';\nexport const DOMID_TYPE_SPACER = '----';\nexport const NOTE_ID = `${DOMID_TYPE_SPACER}${NOTE}`;\nexport const PARENT_ID = `${DOMID_TYPE_SPACER}${PARENT}`;\n// --------------------------------------\n\nexport default {\n  DEFAULT_DIAGRAM_DIRECTION,\n  DEFAULT_NESTED_DOC_DIR,\n  STMT_STATE,\n  STMT_RELATION,\n  STMT_CLASSDEF,\n  STMT_STYLEDEF,\n  STMT_APPLYCLASS,\n  DEFAULT_STATE_TYPE,\n  DIVIDER_TYPE,\n  G_EDGE_STYLE,\n  G_EDGE_ARROWHEADSTYLE,\n  G_EDGE_LABELPOS,\n  G_EDGE_LABELTYPE,\n  G_EDGE_THICKNESS,\n  CSS_EDGE,\n  CSS_DIAGRAM,\n  SHAPE_STATE,\n  SHAPE_STATE_WITH_DESC,\n  SHAPE_START,\n  SHAPE_END,\n  SHAPE_DIVIDER,\n  SHAPE_GROUP,\n  SHAPE_NOTE,\n  SHAPE_NOTEGROUP,\n  CSS_STATE,\n  CSS_DIAGRAM_STATE,\n  CSS_NOTE,\n  CSS_NOTE_EDGE,\n  CSS_EDGE_NOTE_EDGE,\n  CSS_DIAGRAM_NOTE,\n  CSS_CLUSTER,\n  CSS_DIAGRAM_CLUSTER,\n  CSS_CLUSTER_ALT,\n  CSS_DIAGRAM_CLUSTER_ALT,\n  PARENT,\n  NOTE,\n  DOMID_STATE,\n  DOMID_TYPE_SPACER,\n  NOTE_ID,\n  PARENT_ID,\n};\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DiagramStyleClassDef } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { getDiagramElement } from '../../rendering-util/insertElementsForSize.js';\nimport { render } from '../../rendering-util/render.js';\nimport { setupViewPortForSVG } from '../../rendering-util/setupViewPortForSVG.js';\nimport type { LayoutData } from '../../rendering-util/types.js';\nimport utils from '../../utils.js';\nimport { CSS_DIAGRAM, DEFAULT_NESTED_DOC_DIR } from './stateCommon.js';\n\n/**\n * Get the direction from the statement items.\n * Look through all of the documents (docs) in the parsedItems\n * Because is a _document_ direction, the default direction is not necessarily the same as the overall default _diagram_ direction.\n * @param parsedItem - the parsed statement item to look through\n * @param defaultDir - the direction to use if none is found\n * @returns The direction to use\n */\nexport const getDir = (parsedItem: any, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  if (!parsedItem.doc) {\n    return defaultDir;\n  }\n\n  let dir = defaultDir;\n\n  for (const parsedItemDoc of parsedItem.doc) {\n    if (parsedItemDoc.stmt === 'dir') {\n      dir = parsedItemDoc.value;\n    }\n  }\n\n  return dir;\n};\n\nexport const getClasses = function (\n  text: string,\n  diagramObj: any\n): Map<string, DiagramStyleClassDef> {\n  return diagramObj.db.getClasses();\n};\n\nexport const draw = async function (text: string, id: string, _version: string, diag: any) {\n  log.info('REF0:');\n  log.info('Drawing state diagram (v2)', id);\n  const { securityLevel, state: conf, layout } = getConfig();\n  // Extracting the data from the parsed structure into a more usable form\n  // Not related to the refactoring, but this is the first step in the rendering process\n  diag.db.extract(diag.db.getRootDocV2());\n\n  //const DIR = getDir(diag.db.getRootDocV2());\n\n  // The getData method provided in all supported diagrams is used to extract the data from the parsed structure\n  // into the Layout data format\n  const data4Layout = diag.db.getData() as LayoutData;\n\n  // Create the root SVG - the element is the div containing the SVG element\n  const svg = getDiagramElement(id, securityLevel);\n\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = layout;\n\n  // TODO: Should we move these two to baseConfig? These types are not there in StateConfig.\n\n  data4Layout.nodeSpacing = conf?.nodeSpacing || 50;\n  data4Layout.rankSpacing = conf?.rankSpacing || 50;\n  data4Layout.markers = ['barb'];\n  data4Layout.diagramId = id;\n  // console.log('REF1:', data4Layout);\n  await render(data4Layout, svg);\n  const padding = 8;\n\n  // Inject clickable links after nodes are rendered\n  try {\n    const links: Map<string, { url: string; tooltip: string }> =\n      typeof diag.db.getLinks === 'function' ? diag.db.getLinks() : new Map();\n\n    type StateKey = string | { id: string };\n\n    links.forEach((linkInfo, key: StateKey) => {\n      const stateId = typeof key === 'string' ? key : typeof key?.id === 'string' ? key.id : '';\n\n      if (!stateId) {\n        log.warn('⚠️ Invalid or missing stateId from key:', JSON.stringify(key));\n        return;\n      }\n\n      const allNodes = svg.node()?.querySelectorAll('g');\n      let matchedElem: SVGGElement | undefined;\n\n      allNodes?.forEach((g: SVGGElement) => {\n        const text = g.textContent?.trim();\n        if (text === stateId) {\n          matchedElem = g;\n        }\n      });\n\n      if (!matchedElem) {\n        log.warn('⚠️ Could not find node matching text:', stateId);\n        return;\n      }\n\n      const parent = matchedElem.parentNode;\n      if (!parent) {\n        log.warn('⚠️ Node has no parent, cannot wrap:', stateId);\n        return;\n      }\n\n      const a = document.createElementNS('http://www.w3.org/2000/svg', 'a');\n      const cleanedUrl = linkInfo.url.replace(/^\"+|\"+$/g, ''); // remove leading/trailing quotes\n      a.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', cleanedUrl);\n      a.setAttribute('target', '_blank');\n      if (linkInfo.tooltip) {\n        const tooltip = linkInfo.tooltip.replace(/^\"+|\"+$/g, '');\n        a.setAttribute('title', tooltip);\n      }\n\n      parent.replaceChild(a, matchedElem);\n      a.appendChild(matchedElem);\n\n      log.info('🔗 Wrapped node in <a> tag for:', stateId, linkInfo.url);\n    });\n  } catch (err) {\n    log.error('❌ Error injecting clickable links:', err);\n  }\n\n  utils.insertTitle(\n    svg,\n    'statediagramTitleText',\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  setupViewPortForSVG(svg, padding, CSS_DIAGRAM, conf?.useMaxWidth ?? true);\n};\n\nexport default {\n  getClasses,\n  draw,\n  getDir,\n};\n", "import type { MermaidConfig } from '../../config.type.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\nimport common from '../common/common.js';\nimport {\n  CSS_DIAGRAM_CLUSTER,\n  CSS_DIAGRAM_CLUSTER_ALT,\n  CSS_DIAGRAM_NOTE,\n  CSS_DIAGRAM_STATE,\n  CSS_EDGE,\n  CSS_EDGE_NOTE_EDGE,\n  DEFAULT_NESTED_DOC_DIR,\n  DEFAULT_STATE_TYPE,\n  DIVIDER_TYPE,\n  DOMID_STATE,\n  DOMID_TYPE_SPACER,\n  G_EDGE_ARROWHEADSTYLE,\n  G_EDGE_LABELPOS,\n  G_EDGE_LABELTYPE,\n  G_EDGE_STYLE,\n  G_EDGE_THICKNESS,\n  NOTE,\n  NOTE_ID,\n  PARENT,\n  PARENT_ID,\n  SHAPE_DIVIDER,\n  SHAPE_END,\n  SHAPE_GROUP,\n  SHAPE_NOTE,\n  SHAPE_NOTEGROUP,\n  SHAPE_START,\n  SHAPE_STATE,\n  SHAPE_STATE_WITH_DESC,\n  STMT_RELATION,\n  STMT_STATE,\n} from './stateCommon.js';\nimport type { Edge, NodeData, StateStmt, Stmt, StyleClass } from './stateDb.js';\n\n// List of nodes created from the parsed diagram statement items\nconst nodeDb = new Map<string, NodeData>();\n\nlet graphItemCount = 0; // used to construct ids, etc.\n\n/**\n * Create a standard string for the dom ID of an item.\n * If a type is given, insert that before the counter, preceded by the type spacer\n *\n */\nexport function stateDomId(\n  itemId = '',\n  counter = 0,\n  type: string | null = '',\n  typeSpacer = DOMID_TYPE_SPACER\n) {\n  const typeStr = type !== null && type.length > 0 ? `${typeSpacer}${type}` : '';\n  return `${DOMID_STATE}-${itemId}${typeStr}-${counter}`;\n}\n\nconst setupDoc = (\n  parentParsedItem: StateStmt | undefined,\n  doc: Stmt[],\n  diagramStates: Map<string, StateStmt>,\n  nodes: NodeData[],\n  edges: Edge[],\n  altFlag: boolean,\n  look: MermaidConfig['look'],\n  classes: Map<string, StyleClass>\n) => {\n  // graphItemCount = 0;\n  log.trace('items', doc);\n  doc.forEach((item) => {\n    switch (item.stmt) {\n      case STMT_STATE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case DEFAULT_STATE_TYPE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case STMT_RELATION:\n        {\n          dataFetcher(\n            parentParsedItem,\n            item.state1,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          dataFetcher(\n            parentParsedItem,\n            item.state2,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          const edgeData = {\n            id: 'edge' + graphItemCount,\n            start: item.state1.id,\n            end: item.state2.id,\n            arrowhead: 'normal',\n            arrowTypeEnd: 'arrow_barb',\n            style: G_EDGE_STYLE,\n            labelStyle: '',\n            label: common.sanitizeText(item.description ?? '', getConfig()),\n            arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n            labelpos: G_EDGE_LABELPOS,\n            labelType: G_EDGE_LABELTYPE,\n            thickness: G_EDGE_THICKNESS,\n            classes: CSS_EDGE,\n            look,\n          };\n          edges.push(edgeData);\n          graphItemCount++;\n        }\n        break;\n    }\n  });\n};\n\n/**\n * Get the direction from the statement items.\n * Look through all of the documents (docs) in the parsedItems\n * Because is a _document_ direction, the default direction is not necessarily the same as the overall default _diagram_ direction.\n * @param parsedItem - the parsed statement item to look through\n * @param defaultDir - the direction to use if none is found\n */\nconst getDir = (parsedItem: { doc?: Stmt[] }, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  let dir = defaultDir;\n  if (parsedItem.doc) {\n    for (const parsedItemDoc of parsedItem.doc) {\n      if (parsedItemDoc.stmt === 'dir') {\n        dir = parsedItemDoc.value;\n      }\n    }\n  }\n  return dir;\n};\n\nfunction insertOrUpdateNode(\n  nodes: NodeData[],\n  nodeData: NodeData,\n  classes: Map<string, StyleClass>\n) {\n  if (!nodeData.id || nodeData.id === '</join></fork>' || nodeData.id === '</choice>') {\n    return;\n  }\n\n  //Populate node style attributes if nodeData has classes defined\n  if (nodeData.cssClasses) {\n    if (!Array.isArray(nodeData.cssCompiledStyles)) {\n      nodeData.cssCompiledStyles = [];\n    }\n\n    nodeData.cssClasses.split(' ').forEach((cssClass) => {\n      const classDef = classes.get(cssClass);\n      if (classDef) {\n        nodeData.cssCompiledStyles = [...(nodeData.cssCompiledStyles ?? []), ...classDef.styles];\n      }\n    });\n  }\n  const existingNodeData = nodes.find((node) => node.id === nodeData.id);\n  if (existingNodeData) {\n    //update the existing nodeData\n    Object.assign(existingNodeData, nodeData);\n  } else {\n    nodes.push(nodeData);\n  }\n}\n/**\n * Get classes from the db for the info item.\n * If there aren't any or if dbInfoItem isn't defined, return an empty string.\n * Else create 1 string from the list of classes found\n *\n */\nfunction getClassesFromDbInfo(dbInfoItem?: StateStmt): string {\n  return dbInfoItem?.classes?.join(' ') ?? '';\n}\n\nfunction getStylesFromDbInfo(dbInfoItem?: StateStmt): string[] {\n  return dbInfoItem?.styles ?? [];\n}\n\nexport const dataFetcher = (\n  parent: StateStmt | undefined,\n  parsedItem: StateStmt,\n  diagramStates: Map<string, StateStmt>,\n  nodes: NodeData[],\n  edges: Edge[],\n  altFlag: boolean,\n  look: MermaidConfig['look'],\n  classes: Map<string, StyleClass>\n) => {\n  const itemId = parsedItem.id;\n  const dbState = diagramStates.get(itemId);\n  const classStr = getClassesFromDbInfo(dbState);\n  const style = getStylesFromDbInfo(dbState);\n  const config = getConfig();\n\n  log.info('dataFetcher parsedItem', parsedItem, dbState, style);\n\n  if (itemId !== 'root') {\n    let shape = SHAPE_STATE;\n    // The if === true / false can be removed if we can guarantee that the parsedItem.start is always a boolean\n    if (parsedItem.start === true) {\n      shape = SHAPE_START;\n    } else if (parsedItem.start === false) {\n      shape = SHAPE_END;\n    }\n    if (parsedItem.type !== DEFAULT_STATE_TYPE) {\n      shape = parsedItem.type;\n    }\n\n    // Add the node to our list (nodeDb)\n    if (!nodeDb.get(itemId)) {\n      nodeDb.set(itemId, {\n        id: itemId,\n        shape,\n        description: common.sanitizeText(itemId, config),\n        cssClasses: `${classStr} ${CSS_DIAGRAM_STATE}`,\n        cssStyles: style,\n      });\n    }\n\n    const newNode = nodeDb.get(itemId)!;\n\n    // Save data for description and group so that for instance a statement without description overwrites\n    // one with description  @todo TODO What does this mean? If important, add a test for it\n\n    // Build of the array of description strings\n    if (parsedItem.description) {\n      if (Array.isArray(newNode.description)) {\n        // There already is an array of strings,add to it\n        newNode.shape = SHAPE_STATE_WITH_DESC;\n        newNode.description.push(parsedItem.description);\n      } else {\n        if (newNode.description?.length && newNode.description.length > 0) {\n          // if there is a description already transform it to an array\n          newNode.shape = SHAPE_STATE_WITH_DESC;\n          if (newNode.description === itemId) {\n            // If the previous description was this, remove it\n            newNode.description = [parsedItem.description];\n          } else {\n            newNode.description = [newNode.description, parsedItem.description];\n          }\n        } else {\n          newNode.shape = SHAPE_STATE;\n          newNode.description = parsedItem.description;\n        }\n      }\n      newNode.description = common.sanitizeTextOrArray(newNode.description, config);\n    }\n\n    // If there's only 1 description entry, just use a regular state shape\n    if (newNode.description?.length === 1 && newNode.shape === SHAPE_STATE_WITH_DESC) {\n      if (newNode.type === 'group') {\n        newNode.shape = SHAPE_GROUP;\n      } else {\n        newNode.shape = SHAPE_STATE;\n      }\n    }\n\n    // group\n    if (!newNode.type && parsedItem.doc) {\n      log.info('Setting cluster for XCX', itemId, getDir(parsedItem));\n      newNode.type = 'group';\n      newNode.isGroup = true;\n      newNode.dir = getDir(parsedItem);\n      newNode.shape = parsedItem.type === DIVIDER_TYPE ? SHAPE_DIVIDER : SHAPE_GROUP;\n      newNode.cssClasses = `${newNode.cssClasses} ${CSS_DIAGRAM_CLUSTER} ${altFlag ? CSS_DIAGRAM_CLUSTER_ALT : ''}`;\n    }\n\n    // This is what will be added to the graph\n    const nodeData: NodeData = {\n      labelStyle: '',\n      shape: newNode.shape,\n      label: newNode.description,\n      cssClasses: newNode.cssClasses,\n      cssCompiledStyles: [],\n      cssStyles: newNode.cssStyles,\n      id: itemId,\n      dir: newNode.dir,\n      domId: stateDomId(itemId, graphItemCount),\n      type: newNode.type,\n      isGroup: newNode.type === 'group',\n      padding: 8,\n      rx: 10,\n      ry: 10,\n      look,\n    };\n\n    // Clear the label for dividers who have no description\n    if (nodeData.shape === SHAPE_DIVIDER) {\n      nodeData.label = '';\n    }\n\n    if (parent && parent.id !== 'root') {\n      log.trace('Setting node ', itemId, ' to be child of its parent ', parent.id);\n      nodeData.parentId = parent.id;\n    }\n\n    nodeData.centerLabel = true;\n\n    if (parsedItem.note) {\n      // Todo: set random id\n      const noteData: NodeData = {\n        labelStyle: '',\n        shape: SHAPE_NOTE,\n        label: parsedItem.note.text,\n        cssClasses: CSS_DIAGRAM_NOTE,\n        // useHtmlLabels: false,\n        cssStyles: [],\n        cssCompiledStyles: [],\n        id: itemId + NOTE_ID + '-' + graphItemCount,\n        domId: stateDomId(itemId, graphItemCount, NOTE),\n        type: newNode.type,\n        isGroup: newNode.type === 'group',\n        padding: config.flowchart?.padding,\n        look,\n        position: parsedItem.note.position,\n      };\n      const parentNodeId = itemId + PARENT_ID;\n      const groupData = {\n        labelStyle: '',\n        shape: SHAPE_NOTEGROUP,\n        label: parsedItem.note.text,\n        cssClasses: newNode.cssClasses,\n        cssStyles: [],\n        id: itemId + PARENT_ID,\n        domId: stateDomId(itemId, graphItemCount, PARENT),\n        type: 'group',\n        isGroup: true,\n        padding: 16, //getConfig().flowchart.padding\n        look,\n        position: parsedItem.note.position,\n      };\n      graphItemCount++;\n\n      //add parent id to groupData\n      groupData.id = parentNodeId;\n      //add parent id to noteData\n      noteData.parentId = parentNodeId;\n      //nodeData.parentId = parentNodeId;\n\n      //insert groupData\n      insertOrUpdateNode(nodes, groupData, classes);\n      //insert noteData\n      insertOrUpdateNode(nodes, noteData, classes);\n      //insert nodeData\n      insertOrUpdateNode(nodes, nodeData, classes);\n\n      let from = itemId;\n      let to = noteData.id;\n\n      if (parsedItem.note.position === 'left of') {\n        from = noteData.id;\n        to = itemId;\n      }\n\n      edges.push({\n        id: from + '-' + to,\n        start: from,\n        end: to,\n        arrowhead: 'none',\n        arrowTypeEnd: '',\n        style: G_EDGE_STYLE,\n        labelStyle: '',\n        classes: CSS_EDGE_NOTE_EDGE,\n        arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n        labelpos: G_EDGE_LABELPOS,\n        labelType: G_EDGE_LABELTYPE,\n        thickness: G_EDGE_THICKNESS,\n        look,\n      });\n    } else {\n      insertOrUpdateNode(nodes, nodeData, classes);\n    }\n  }\n  if (parsedItem.doc) {\n    log.trace('Adding nodes children ');\n    setupDoc(parsedItem, parsedItem.doc, diagramStates, nodes, edges, !altFlag, look, classes);\n  }\n};\n\nexport const reset = () => {\n  nodeDb.clear();\n  graphItemCount = 0;\n};\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\nimport { generateId } from '../../utils.js';\nimport common from '../common/common.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport { dataFetcher, reset as resetDataFetcher } from './dataFetcher.js';\nimport { getDir } from './stateRenderer-v3-unified.js';\nimport {\n  DEFAULT_DIAGRAM_DIRECTION,\n  DEFAULT_STATE_TYPE,\n  DIVIDER_TYPE,\n  STMT_APPLYCLASS,\n  STMT_CLASSDEF,\n  STMT_RELATION,\n  STMT_ROOT,\n  STMT_DIRECTION,\n  STMT_STATE,\n  STMT_STYLEDEF,\n} from './stateCommon.js';\nimport type { MermaidConfig } from '../../config.type.js';\n\nconst CONSTANTS = {\n  START_NODE: '[*]',\n  START_TYPE: 'start',\n  END_NODE: '[*]',\n  END_TYPE: 'end',\n  COLOR_KEYWORD: 'color',\n  FILL_KEYWORD: 'fill',\n  BG_FILL: 'bgFill',\n  STYLECLASS_SEP: ',',\n} as const;\n\ninterface BaseStmt {\n  stmt:\n    | 'applyClass'\n    | 'classDef'\n    | 'dir'\n    | 'relation'\n    | 'state'\n    | 'style'\n    | 'root'\n    | 'default'\n    | 'click';\n}\n\ninterface ApplyClassStmt extends BaseStmt {\n  stmt: 'applyClass';\n  id: string;\n  styleClass: string;\n}\n\ninterface ClassDefStmt extends BaseStmt {\n  stmt: 'classDef';\n  id: string;\n  classes: string;\n}\n\ninterface DirectionStmt extends BaseStmt {\n  stmt: 'dir';\n  value: 'TB' | 'BT' | 'RL' | 'LR';\n}\n\ninterface RelationStmt extends BaseStmt {\n  stmt: 'relation';\n  state1: StateStmt;\n  state2: StateStmt;\n  description?: string;\n}\n\nexport interface StateStmt extends BaseStmt {\n  stmt: 'state' | 'default';\n  id: string;\n  type: 'default' | 'fork' | 'join' | 'choice' | 'divider' | 'start' | 'end';\n  description?: string;\n  descriptions?: string[];\n  doc?: Stmt[];\n  note?: Note;\n  start?: boolean;\n  classes?: string[];\n  styles?: string[];\n  textStyles?: string[];\n}\n\ninterface StyleStmt extends BaseStmt {\n  stmt: 'style';\n  id: string;\n  styleClass: string;\n}\n\nexport interface RootStmt {\n  id: 'root';\n  stmt: 'root';\n  doc?: Stmt[];\n}\n\nexport interface ClickStmt extends BaseStmt {\n  stmt: 'click';\n  id: string;\n  url: string;\n  tooltip: string;\n}\n\ninterface Note {\n  position?: 'left of' | 'right of';\n  text: string;\n}\n\nexport type Stmt =\n  | ApplyClassStmt\n  | ClassDefStmt\n  | DirectionStmt\n  | RelationStmt\n  | StateStmt\n  | StyleStmt\n  | RootStmt\n  | ClickStmt;\n\ninterface DiagramEdge {\n  id1: string;\n  id2: string;\n  relationTitle?: string;\n}\n\ninterface Document {\n  relations: DiagramEdge[];\n  states: Map<string, StateStmt>;\n  documents: Record<string, Document>;\n}\n\nexport interface StyleClass {\n  id: string;\n  styles: string[];\n  textStyles: string[];\n}\n\nexport interface NodeData {\n  labelStyle?: string;\n  shape: string;\n  label?: string | string[];\n  cssClasses: string;\n  cssCompiledStyles?: string[];\n  cssStyles: string[];\n  id: string;\n  dir?: string;\n  domId?: string;\n  type?: string;\n  isGroup?: boolean;\n  padding?: number;\n  rx?: number;\n  ry?: number;\n  look?: MermaidConfig['look'];\n  parentId?: string;\n  centerLabel?: boolean;\n  position?: string;\n  description?: string | string[];\n}\n\nexport interface Edge {\n  id: string;\n  start: string;\n  end: string;\n  arrowhead: string;\n  arrowTypeEnd: string;\n  style: string;\n  labelStyle: string;\n  label?: string;\n  arrowheadStyle: string;\n  labelpos: string;\n  labelType: string;\n  thickness: string;\n  classes: string;\n  look: MermaidConfig['look'];\n}\n\n/**\n * Returns a new list of classes.\n * In the future, this can be replaced with a class common to all diagrams.\n * ClassDef information = \\{ id: id, styles: [], textStyles: [] \\}\n */\nconst newClassesList = (): Map<string, StyleClass> => new Map();\nconst newDoc = (): Document => ({\n  relations: [],\n  states: new Map(),\n  documents: {},\n});\nconst clone = <T>(o: T): T => JSON.parse(JSON.stringify(o));\n\nexport class StateDB {\n  private nodes: NodeData[] = [];\n  private edges: Edge[] = [];\n  private rootDoc: Stmt[] = [];\n  private classes = newClassesList();\n  private documents = { root: newDoc() };\n  private currentDocument = this.documents.root;\n  private startEndCount = 0;\n  private dividerCnt = 0;\n  private links = new Map<string, { url: string; tooltip: string }>();\n\n  static readonly relationType = {\n    AGGREGATION: 0,\n    EXTENSION: 1,\n    COMPOSITION: 2,\n    DEPENDENCY: 3,\n  } as const;\n\n  constructor(private version: 1 | 2) {\n    this.clear();\n    // Bind methods used by JISON\n    this.setRootDoc = this.setRootDoc.bind(this);\n    this.getDividerId = this.getDividerId.bind(this);\n    this.setDirection = this.setDirection.bind(this);\n    this.trimColon = this.trimColon.bind(this);\n  }\n\n  /**\n   * Convert all of the statements (stmts) that were parsed into states and relationships.\n   * This is done because a state diagram may have nested sections,\n   * where each section is a 'document' and has its own set of statements.\n   * Ex: the section within a fork has its own statements, and incoming and outgoing statements\n   * refer to the fork as a whole (document).\n   * See the parser grammar:  the definition of a document is a document then a 'line', where a line can be a statement.\n   * This will push the statement into the list of statements for the current document.\n   */\n  extract(statements: Stmt[] | { doc: Stmt[] }) {\n    this.clear(true);\n    for (const item of Array.isArray(statements) ? statements : statements.doc) {\n      switch (item.stmt) {\n        case STMT_STATE:\n          this.addState(item.id.trim(), item.type, item.doc, item.description, item.note);\n          break;\n        case STMT_RELATION:\n          this.addRelation(item.state1, item.state2, item.description);\n          break;\n        case STMT_CLASSDEF:\n          this.addStyleClass(item.id.trim(), item.classes);\n          break;\n        case STMT_STYLEDEF:\n          this.handleStyleDef(item);\n          break;\n        case STMT_APPLYCLASS:\n          this.setCssClass(item.id.trim(), item.styleClass);\n          break;\n        case 'click':\n          this.addLink(item.id, item.url, item.tooltip);\n          break;\n      }\n    }\n    const diagramStates = this.getStates();\n    const config = getConfig();\n\n    resetDataFetcher();\n    dataFetcher(\n      undefined,\n      this.getRootDocV2() as StateStmt,\n      diagramStates,\n      this.nodes,\n      this.edges,\n      true,\n      config.look,\n      this.classes\n    );\n\n    // Process node labels\n    for (const node of this.nodes) {\n      if (!Array.isArray(node.label)) {\n        continue;\n      }\n\n      node.description = node.label.slice(1);\n      if (node.isGroup && node.description.length > 0) {\n        throw new Error(\n          `Group nodes can only have label. Remove the additional description for node [${node.id}]`\n        );\n      }\n      node.label = node.label[0];\n    }\n  }\n\n  private handleStyleDef(item: StyleStmt) {\n    const ids = item.id.trim().split(',');\n    const styles = item.styleClass.split(',');\n\n    for (const id of ids) {\n      let state = this.getState(id);\n      if (!state) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        state = this.getState(trimmedId);\n      }\n      if (state) {\n        state.styles = styles.map((s) => s.replace(/;/g, '')?.trim());\n      }\n    }\n  }\n\n  setRootDoc(o: Stmt[]) {\n    log.info('Setting root doc', o);\n    this.rootDoc = o;\n    if (this.version === 1) {\n      this.extract(o);\n    } else {\n      this.extract(this.getRootDocV2());\n    }\n  }\n\n  docTranslator(parent: RootStmt | StateStmt, node: Stmt, first: boolean) {\n    if (node.stmt === STMT_RELATION) {\n      this.docTranslator(parent, node.state1, true);\n      this.docTranslator(parent, node.state2, false);\n      return;\n    }\n\n    if (node.stmt === STMT_STATE) {\n      if (node.id === CONSTANTS.START_NODE) {\n        node.id = parent.id + (first ? '_start' : '_end');\n        node.start = first;\n      } else {\n        // This is just a plain state, not a start or end\n        node.id = node.id.trim();\n      }\n    }\n\n    if ((node.stmt !== STMT_ROOT && node.stmt !== STMT_STATE) || !node.doc) {\n      return;\n    }\n\n    const doc = [];\n    // Check for concurrency\n    let currentDoc = [];\n    for (const stmt of node.doc) {\n      if ((stmt as StateStmt).type === DIVIDER_TYPE) {\n        const newNode = clone(stmt as StateStmt);\n        newNode.doc = clone(currentDoc);\n        doc.push(newNode);\n        currentDoc = [];\n      } else {\n        currentDoc.push(stmt);\n      }\n    }\n\n    // If any divider was encountered\n    if (doc.length > 0 && currentDoc.length > 0) {\n      const newNode = {\n        stmt: STMT_STATE,\n        id: generateId(),\n        type: 'divider',\n        doc: clone(currentDoc),\n      } satisfies StateStmt;\n      doc.push(clone(newNode));\n      node.doc = doc;\n    }\n\n    node.doc.forEach((docNode) => this.docTranslator(node, docNode, true));\n  }\n\n  private getRootDocV2() {\n    this.docTranslator(\n      { id: STMT_ROOT, stmt: STMT_ROOT },\n      { id: STMT_ROOT, stmt: STMT_ROOT, doc: this.rootDoc },\n      true\n    );\n    return { id: STMT_ROOT, doc: this.rootDoc };\n  }\n\n  /**\n   * Function called by parser when a node definition has been found.\n   *\n   * @param descr - description for the state. Can be a string or a list or strings\n   * @param classes - class styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 class, convert it to an array of that 1 class.\n   * @param styles - styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 style, convert it to an array of that 1 style.\n   * @param textStyles - text styles to apply to this state. Can be a string (1 text test) or an array of text styles. If it's just 1 text style, convert it to an array of that 1 text style.\n   */\n  addState(\n    id: string,\n    type: StateStmt['type'] = DEFAULT_STATE_TYPE,\n    doc: Stmt[] | undefined = undefined,\n    descr: string | string[] | undefined = undefined,\n    note: Note | undefined = undefined,\n    classes: string | string[] | undefined = undefined,\n    styles: string | string[] | undefined = undefined,\n    textStyles: string | string[] | undefined = undefined\n  ) {\n    const trimmedId = id?.trim();\n    if (!this.currentDocument.states.has(trimmedId)) {\n      log.info('Adding state ', trimmedId, descr);\n      this.currentDocument.states.set(trimmedId, {\n        stmt: STMT_STATE,\n        id: trimmedId,\n        descriptions: [],\n        type,\n        doc,\n        note,\n        classes: [],\n        styles: [],\n        textStyles: [],\n      });\n    } else {\n      const state = this.currentDocument.states.get(trimmedId);\n      if (!state) {\n        throw new Error(`State not found: ${trimmedId}`);\n      }\n      if (!state.doc) {\n        state.doc = doc;\n      }\n      if (!state.type) {\n        state.type = type;\n      }\n    }\n\n    if (descr) {\n      log.info('Setting state description', trimmedId, descr);\n      const descriptions = Array.isArray(descr) ? descr : [descr];\n      descriptions.forEach((des) => this.addDescription(trimmedId, des.trim()));\n    }\n\n    if (note) {\n      const doc2 = this.currentDocument.states.get(trimmedId);\n      if (!doc2) {\n        throw new Error(`State not found: ${trimmedId}`);\n      }\n      doc2.note = note;\n      doc2.note.text = common.sanitizeText(doc2.note.text, getConfig());\n    }\n\n    if (classes) {\n      log.info('Setting state classes', trimmedId, classes);\n      const classesList = Array.isArray(classes) ? classes : [classes];\n      classesList.forEach((cssClass) => this.setCssClass(trimmedId, cssClass.trim()));\n    }\n\n    if (styles) {\n      log.info('Setting state styles', trimmedId, styles);\n      const stylesList = Array.isArray(styles) ? styles : [styles];\n      stylesList.forEach((style) => this.setStyle(trimmedId, style.trim()));\n    }\n\n    if (textStyles) {\n      log.info('Setting state styles', trimmedId, styles);\n      const textStylesList = Array.isArray(textStyles) ? textStyles : [textStyles];\n      textStylesList.forEach((textStyle) => this.setTextStyle(trimmedId, textStyle.trim()));\n    }\n  }\n\n  clear(saveCommon?: boolean) {\n    this.nodes = [];\n    this.edges = [];\n    this.documents = { root: newDoc() };\n    this.currentDocument = this.documents.root;\n\n    // number of start and end nodes; used to construct ids\n    this.startEndCount = 0;\n    this.classes = newClassesList();\n    if (!saveCommon) {\n      this.links = new Map(); // <-- add here\n      commonClear();\n    }\n  }\n\n  getState(id: string) {\n    return this.currentDocument.states.get(id);\n  }\n\n  getStates() {\n    return this.currentDocument.states;\n  }\n\n  logDocuments() {\n    log.info('Documents = ', this.documents);\n  }\n\n  getRelations() {\n    return this.currentDocument.relations;\n  }\n\n  /**\n   * Adds a clickable link to a state.\n   */\n  addLink(stateId: string, url: string, tooltip: string): void {\n    this.links.set(stateId, { url, tooltip });\n    log.warn('Adding link', stateId, url, tooltip);\n  }\n\n  /**\n   * Get all registered links.\n   */\n  getLinks(): Map<string, { url: string; tooltip: string }> {\n    return this.links;\n  }\n\n  /**\n   * If the id is a start node ( [*] ), then return a new id constructed from\n   * the start node name and the current start node count.\n   * else return the given id\n   */\n  startIdIfNeeded(id = '') {\n    if (id === CONSTANTS.START_NODE) {\n      this.startEndCount++;\n      return `${CONSTANTS.START_TYPE}${this.startEndCount}`;\n    }\n    return id;\n  }\n\n  /**\n   * If the id is a start node ( [*] ), then return the start type ('start')\n   * else return the given type\n   */\n  startTypeIfNeeded(id = '', type: StateStmt['type'] = DEFAULT_STATE_TYPE) {\n    return id === CONSTANTS.START_NODE ? CONSTANTS.START_TYPE : type;\n  }\n\n  /**\n   * If the id is an end node ( [*] ), then return a new id constructed from\n   * the end node name and the current start_end node count.\n   * else return the given id\n   */\n  endIdIfNeeded(id = '') {\n    if (id === CONSTANTS.END_NODE) {\n      this.startEndCount++;\n      return `${CONSTANTS.END_TYPE}${this.startEndCount}`;\n    }\n    return id;\n  }\n\n  /**\n   * If the id is an end node ( [*] ), then return the end type\n   * else return the given type\n   *\n   */\n  endTypeIfNeeded(id = '', type: StateStmt['type'] = DEFAULT_STATE_TYPE) {\n    return id === CONSTANTS.END_NODE ? CONSTANTS.END_TYPE : type;\n  }\n\n  addRelationObjs(item1: StateStmt, item2: StateStmt, relationTitle = '') {\n    const id1 = this.startIdIfNeeded(item1.id.trim());\n    const type1 = this.startTypeIfNeeded(item1.id.trim(), item1.type);\n    const id2 = this.startIdIfNeeded(item2.id.trim());\n    const type2 = this.startTypeIfNeeded(item2.id.trim(), item2.type);\n    this.addState(\n      id1,\n      type1,\n      item1.doc,\n      item1.description,\n      item1.note,\n      item1.classes,\n      item1.styles,\n      item1.textStyles\n    );\n    this.addState(\n      id2,\n      type2,\n      item2.doc,\n      item2.description,\n      item2.note,\n      item2.classes,\n      item2.styles,\n      item2.textStyles\n    );\n    this.currentDocument.relations.push({\n      id1,\n      id2,\n      relationTitle: common.sanitizeText(relationTitle, getConfig()),\n    });\n  }\n\n  /**\n   * Add a relation between two items.  The items may be full objects or just the string id of a state.\n   */\n  addRelation(item1: string | StateStmt, item2: string | StateStmt, title?: string) {\n    if (typeof item1 === 'object' && typeof item2 === 'object') {\n      this.addRelationObjs(item1, item2, title);\n    } else if (typeof item1 === 'string' && typeof item2 === 'string') {\n      const id1 = this.startIdIfNeeded(item1.trim());\n      const type1 = this.startTypeIfNeeded(item1);\n      const id2 = this.endIdIfNeeded(item2.trim());\n      const type2 = this.endTypeIfNeeded(item2);\n\n      this.addState(id1, type1);\n      this.addState(id2, type2);\n      this.currentDocument.relations.push({\n        id1,\n        id2,\n        relationTitle: title ? common.sanitizeText(title, getConfig()) : undefined,\n      });\n    }\n  }\n\n  addDescription(id: string, descr: string) {\n    const theState = this.currentDocument.states.get(id);\n    const _descr = descr.startsWith(':') ? descr.replace(':', '').trim() : descr;\n    theState?.descriptions?.push(common.sanitizeText(_descr, getConfig()));\n  }\n\n  cleanupLabel(label: string) {\n    return label.startsWith(':') ? label.slice(2).trim() : label.trim();\n  }\n\n  getDividerId() {\n    this.dividerCnt++;\n    return `divider-id-${this.dividerCnt}`;\n  }\n\n  /**\n   * Called when the parser comes across a (style) class definition\n   * @example classDef my-style fill:#f96;\n   *\n   * @param id - the id of this (style) class\n   * @param styleAttributes - the string with 1 or more style attributes (each separated by a comma)\n   */\n  addStyleClass(id: string, styleAttributes = '') {\n    // create a new style class object with this id\n    if (!this.classes.has(id)) {\n      this.classes.set(id, { id, styles: [], textStyles: [] });\n    }\n    const foundClass = this.classes.get(id);\n    if (styleAttributes && foundClass) {\n      styleAttributes.split(CONSTANTS.STYLECLASS_SEP).forEach((attrib) => {\n        const fixedAttrib = attrib.replace(/([^;]*);/, '$1').trim();\n        if (RegExp(CONSTANTS.COLOR_KEYWORD).exec(attrib)) {\n          const newStyle1 = fixedAttrib.replace(CONSTANTS.FILL_KEYWORD, CONSTANTS.BG_FILL);\n          const newStyle2 = newStyle1.replace(CONSTANTS.COLOR_KEYWORD, CONSTANTS.FILL_KEYWORD);\n          foundClass.textStyles.push(newStyle2);\n        }\n        foundClass.styles.push(fixedAttrib);\n      });\n    }\n  }\n\n  getClasses() {\n    return this.classes;\n  }\n\n  /**\n   * Add a (style) class or css class to a state with the given id.\n   * If the state isn't already in the list of known states, add it.\n   * Might be called by parser when a style class or CSS class should be applied to a state\n   *\n   * @param itemIds - The id or a list of ids of the item(s) to apply the css class to\n   * @param cssClassName - CSS class name\n   */\n  setCssClass(itemIds: string, cssClassName: string) {\n    itemIds.split(',').forEach((id) => {\n      let foundState = this.getState(id);\n      if (!foundState) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        foundState = this.getState(trimmedId);\n      }\n      foundState?.classes?.push(cssClassName);\n    });\n  }\n\n  /**\n   * Add a style to a state with the given id.\n   * @example style stateId fill:#f9f,stroke:#333,stroke-width:4px\n   *   where 'style' is the keyword\n   *   stateId is the id of a state\n   *   the rest of the string is the styleText (all of the attributes to be applied to the state)\n   *\n   * @param itemId - The id of item to apply the style to\n   * @param styleText - the text of the attributes for the style\n   */\n  setStyle(itemId: string, styleText: string) {\n    this.getState(itemId)?.styles?.push(styleText);\n  }\n\n  /**\n   * Add a text style to a state with the given id\n   *\n   * @param itemId - The id of item to apply the css class to\n   * @param cssClassName - CSS class name\n   */\n  setTextStyle(itemId: string, cssClassName: string) {\n    this.getState(itemId)?.textStyles?.push(cssClassName);\n  }\n\n  /**\n   * Finds the direction statement in the root document.\n   * @returns the direction statement if present\n   */\n  private getDirectionStatement() {\n    return this.rootDoc.find((doc): doc is DirectionStmt => doc.stmt === STMT_DIRECTION);\n  }\n\n  getDirection() {\n    return this.getDirectionStatement()?.value ?? DEFAULT_DIAGRAM_DIRECTION;\n  }\n\n  setDirection(dir: DirectionStmt['value']) {\n    const doc = this.getDirectionStatement();\n    if (doc) {\n      doc.value = dir;\n    } else {\n      this.rootDoc.unshift({ stmt: STMT_DIRECTION, value: dir });\n    }\n  }\n\n  trimColon(str: string) {\n    return str.startsWith(':') ? str.slice(1).trim() : str.trim();\n  }\n\n  getData() {\n    const config = getConfig();\n    return {\n      nodes: this.nodes,\n      edges: this.edges,\n      other: {},\n      config,\n      direction: getDir(this.getRootDocV2()),\n    };\n  }\n\n  getConfig() {\n    return getConfig().state;\n  }\n\n  getAccTitle = getAccTitle;\n  setAccTitle = setAccTitle;\n  getAccDescription = getAccDescription;\n  setAccDescription = setAccDescription;\n  setDiagramTitle = setDiagramTitle;\n  getDiagramTitle = getDiagramTitle;\n}\n", "const getStyles = (options) =>\n  `\ndefs #statediagram-barbEnd {\n    fill: ${options.transitionColor};\n    stroke: ${options.transitionColor};\n  }\ng.stateGroup text {\n  fill: ${options.nodeBorder};\n  stroke: none;\n  font-size: 10px;\n}\ng.stateGroup text {\n  fill: ${options.textColor};\n  stroke: none;\n  font-size: 10px;\n\n}\ng.stateGroup .state-title {\n  font-weight: bolder;\n  fill: ${options.stateLabelColor};\n}\n\ng.stateGroup rect {\n  fill: ${options.mainBkg};\n  stroke: ${options.nodeBorder};\n}\n\ng.stateGroup line {\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.transition {\n  stroke: ${options.transitionColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.stateGroup .composit {\n  fill: ${options.background};\n  border-bottom: 1px\n}\n\n.stateGroup .alt-composit {\n  fill: #e0e0e0;\n  border-bottom: 1px\n}\n\n.state-note {\n  stroke: ${options.noteBorderColor};\n  fill: ${options.noteBkgColor};\n\n  text {\n    fill: ${options.noteTextColor};\n    stroke: none;\n    font-size: 10px;\n  }\n}\n\n.stateLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${options.mainBkg};\n  opacity: 0.5;\n}\n\n.edgeLabel .label rect {\n  fill: ${options.labelBackgroundColor};\n  opacity: 0.5;\n}\n.edgeLabel {\n  background-color: ${options.edgeLabelBackground};\n  p {\n    background-color: ${options.edgeLabelBackground};\n  }\n  rect {\n    opacity: 0.5;\n    background-color: ${options.edgeLabelBackground};\n    fill: ${options.edgeLabelBackground};\n  }\n  text-align: center;\n}\n.edgeLabel .label text {\n  fill: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n.label div .edgeLabel {\n  color: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n\n.stateLabel text {\n  fill: ${options.stateLabelColor};\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.node circle.state-start {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node .fork-join {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node circle.state-end {\n  fill: ${options.innerEndBackground};\n  stroke: ${options.background};\n  stroke-width: 1.5\n}\n.end-state-inner {\n  fill: ${options.compositeBackground || options.background};\n  // stroke: ${options.background};\n  stroke-width: 1.5\n}\n\n.node rect {\n  fill: ${options.stateBkg || options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n.node polygon {\n  fill: ${options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};;\n  stroke-width: 1px;\n}\n#statediagram-barbEnd {\n  fill: ${options.lineColor};\n}\n\n.statediagram-cluster rect {\n  fill: ${options.compositeTitleBackground};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n\n.cluster-label, .nodeLabel {\n  color: ${options.stateLabelColor};\n  // line-height: 1;\n}\n\n.statediagram-cluster rect.outer {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state .divider {\n  stroke: ${options.stateBorder || options.nodeBorder};\n}\n\n.statediagram-state .title-state {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-cluster.statediagram-cluster .inner {\n  fill: ${options.compositeBackground || options.background};\n}\n.statediagram-cluster.statediagram-cluster-alt .inner {\n  fill: ${options.altBackground ? options.altBackground : '#efefef'};\n}\n\n.statediagram-cluster .inner {\n  rx:0;\n  ry:0;\n}\n\n.statediagram-state rect.basic {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state rect.divider {\n  stroke-dasharray: 10,10;\n  fill: ${options.altBackground ? options.altBackground : '#efefef'};\n}\n\n.note-edge {\n  stroke-dasharray: 5;\n}\n\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n\n.statediagram-note text {\n  fill: ${options.noteTextColor};\n}\n\n.statediagram-note .nodeLabel {\n  color: ${options.noteTextColor};\n}\n.statediagram .edgeLabel {\n  color: red; // ${options.noteTextColor};\n}\n\n#dependencyStart, #dependencyEnd {\n  fill: ${options.lineColor};\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.statediagramTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${options.textColor};\n}\n`;\n\n// todo: change composit to composite\n// cspell:ignore composit\n\nexport default getStyles;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAClnB,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,SAAQ,GAAE,MAAK,GAAE,MAAK,GAAE,YAAW,GAAE,QAAO,GAAE,aAAY,GAAE,qBAAoB,IAAG,kBAAiB,IAAG,qBAAoB,IAAG,eAAc,IAAG,SAAQ,IAAG,OAAM,IAAG,cAAa,IAAG,SAAQ,IAAG,SAAQ,IAAG,kBAAiB,IAAG,gBAAe,IAAG,eAAc,IAAG,eAAc,IAAG,MAAK,IAAG,MAAK,IAAG,QAAO,IAAG,QAAO,IAAG,UAAS,IAAG,cAAa,IAAG,QAAO,IAAG,gBAAe,IAAG,aAAY,IAAG,aAAY,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,SAAQ,IAAG,UAAS,IAAG,QAAO,IAAG,YAAW,IAAG,eAAc,IAAG,sBAAqB,IAAG,WAAU,IAAG,SAAQ,IAAG,aAAY,IAAG,sBAAqB,IAAG,SAAQ,IAAG,mBAAkB,IAAG,cAAa,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,OAAM,IAAG,KAAI,IAAG,cAAa,IAAG,mBAAkB,IAAG,WAAU,IAAG,YAAW,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IACp4B,YAAY,EAAC,GAAE,SAAQ,GAAE,SAAQ,GAAE,MAAK,GAAE,MAAK,IAAG,SAAQ,IAAG,OAAM,IAAG,cAAa,IAAG,SAAQ,IAAG,SAAQ,IAAG,kBAAiB,IAAG,gBAAe,IAAG,eAAc,IAAG,eAAc,IAAG,MAAK,IAAG,MAAK,IAAG,QAAO,IAAG,QAAO,IAAG,UAAS,IAAG,cAAa,IAAG,QAAO,IAAG,aAAY,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,SAAQ,IAAG,UAAS,IAAG,QAAO,IAAG,YAAW,IAAG,eAAc,IAAG,sBAAqB,IAAG,WAAU,IAAG,SAAQ,IAAG,aAAY,IAAG,sBAAqB,IAAG,SAAQ,IAAG,mBAAkB,IAAG,cAAa,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,KAAI,IAAG,cAAa,IAAG,mBAAkB,IAAG,WAAU,IAAG,WAAU;AAAA,IACtsB,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IACtU,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AAC8C,aAAG,WAAW,GAAG,EAAE,CAAC;AAAG,iBAAO,GAAG,EAAE;AACtF;AAAA,QACA,KAAK;AACgC,eAAK,IAAI,CAAC;AAC/C;AAAA,QACA,KAAK;AAEG,cAAG,GAAG,EAAE,KAAI,MAAK;AAEb,eAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AAAG,iBAAK,IAAI,GAAG,KAAG,CAAC;AAAA,UAC3C;AAGR;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAI,GAAG,EAAE;AACf;AAAA,QACA,KAAK;AACJ,eAAK,IAAE;AACR;AAAA,QACA,KAAK;AAEO,eAAK,IAAE,GAAG,EAAE;AAExB;AAAA,QACA,KAAK;AAEO,gBAAM,YAAY,GAAG,KAAG,CAAC;AACzB,oBAAU,cAAc,GAAG,UAAU,GAAG,EAAE,CAAC;AAC3C,eAAK,IAAI;AAErB;AAAA,QACA,KAAK;AAIO,eAAK,IAAE,EAAE,MAAM,YAAY,QAAQ,GAAG,KAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAC;AAEvE;AAAA,QACA,KAAK;AAEO,gBAAM,iBAAiB,GAAG,UAAU,GAAG,EAAE,CAAC;AAE1C,eAAK,IAAE,EAAE,MAAM,YAAY,QAAQ,GAAG,KAAG,CAAC,GAAG,QAAQ,GAAG,KAAG,CAAC,GAAG,aAAa,eAAc;AAEtG;AAAA,QACA,KAAK;AAGG,eAAK,IAAE,EAAE,MAAM,SAAS,IAAI,GAAG,KAAG,CAAC,GAAG,MAAM,WAAW,aAAa,IAAI,KAAK,GAAG,KAAG,CAAC,EAAE;AAE9F;AAAA,QACA,KAAK;AAEG,cAAI,KAAG,GAAG,EAAE;AACZ,cAAI,cAAc,GAAG,KAAG,CAAC,EAAE,KAAK;AAChC,cAAG,GAAG,EAAE,EAAE,MAAM,GAAG,GAAE;AACjB,gBAAI,QAAQ,GAAG,EAAE,EAAE,MAAM,GAAG;AAC5B,iBAAG,MAAM,CAAC;AACV,0BAAc,CAAC,aAAa,MAAM,CAAC,CAAC;AAAA,UACxC;AACA,eAAK,IAAE,EAAC,MAAM,SAAS,IAAQ,MAAM,WAAW,YAAwB;AAGhF;AAAA,QACA,KAAK;AAGI,eAAK,IAAE,EAAE,MAAM,SAAS,IAAI,GAAG,KAAG,CAAC,GAAG,MAAM,WAAW,aAAa,GAAG,KAAG,CAAC,GAAG,KAAK,GAAG,KAAG,CAAC,EAAE;AAErG;AAAA,QACA,KAAK;AAEG,eAAK,IAAE,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,GAAG,MAAM,OAAO;AAEzD;AAAA,QACA,KAAK;AAEG,eAAK,IAAE,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,GAAG,MAAM,OAAO;AAEzD;AAAA,QACA,KAAK;AAEG,eAAK,IAAE,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,GAAG,MAAM,SAAS;AAE3D;AAAA,QACA,KAAK;AAEG,eAAK,IAAE,EAAE,MAAM,SAAS,IAAI,GAAG,aAAa,GAAG,MAAM,UAAU;AAEvE;AAAA,QACA,KAAK;AAGG,eAAK,IAAE,EAAE,MAAM,SAAS,IAAI,GAAG,KAAG,CAAC,EAAE,KAAK,GAAG,MAAK,EAAC,UAAU,GAAG,KAAG,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,EAAE,EAAE,KAAK,EAAC,EAAC;AAE1G;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,YAAY,KAAK,CAAC;AAC3C;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,kBAAkB,KAAK,CAAC;AACjD;AAAA,QACA,KAAK;AAEG,eAAK,IAAI;AAAA,YACL,MAAM;AAAA,YACN,IAAI,GAAG,KAAG,CAAC;AAAA,YACX,KAAK,GAAG,KAAG,CAAC;AAAA,YACZ,SAAS,GAAG,KAAG,CAAC;AAAA,UACpB;AAER;AAAA,QACA,KAAK;AAEG,eAAK,IAAI;AAAA,YACL,MAAM;AAAA,YACN,IAAI,GAAG,KAAG,CAAC;AAAA,YACX,KAAK,GAAG,KAAG,CAAC;AAAA,YACZ,SAAS;AAAA,UACb;AAER;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AAEN,eAAK,IAAI,EAAE,MAAM,YAAY,IAAI,GAAG,KAAG,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,EAAE,EAAE,KAAK,EAAE;AAEjF;AAAA,QACA,KAAK;AAEG,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAG,CAAC,EAAE,KAAK,GAAG,YAAY,GAAG,EAAE,EAAE,KAAK,EAAE;AAEjF;AAAA,QACA,KAAK;AAGG,eAAK,IAAE,EAAE,MAAM,cAAc,IAAI,GAAG,KAAG,CAAC,EAAE,KAAK,GAAG,YAAY,GAAG,EAAE,EAAE,KAAK,EAAE;AAEpF;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AAAE,eAAK,IAAE,EAAC,MAAK,OAAO,OAAM,KAAI;AACrD;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AAAE,eAAK,IAAE,EAAC,MAAK,OAAO,OAAM,KAAI;AACrD;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AAAG,eAAK,IAAE,EAAC,MAAK,OAAO,OAAM,KAAI;AACtD;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AAAE,eAAK,IAAE,EAAC,MAAK,OAAO,OAAM,KAAI;AACrD;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AAEF,eAAK,IAAE,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,EAAE,KAAK,GAAG,MAAM,WAAW,aAAa,GAAG;AAExF;AAAA,QACA,KAAK;AAEO,eAAK,IAAE,EAAE,MAAM,SAAS,IAAI,GAAG,KAAG,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM,WAAW,aAAa,GAAG;AAEpH;AAAA,QACA,KAAK;AAEO,eAAK,IAAE,EAAE,MAAM,SAAS,IAAI,GAAG,KAAG,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM,WAAW,aAAa,GAAG;AAEpH;AAAA,MACA;AAAA,IACA,GA7Ke;AAAA,IA8Kf,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,KAAI,GAAE,KAAI,GAAE,IAAG,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,GAAE,GAAE,GAAE,KAAI,GAAE,KAAI,GAAE,IAAG,GAAE,EAAC,GAAE,GAAE,GAAE,KAAI,GAAE,KAAI,GAAE,IAAG,GAAE,EAAE,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,KAAI,EAAC,GAAE,EAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,GAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,GAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IACzkE,gBAAgB,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC;AAAA,IACpD,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAqB;AAAA,YAAuC;AACjE;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,OAAO;AAA8C,mBAAO;AACpF;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACxC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACxC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,qBAAqB;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,UAAU;AAAG,mBAAO;AAC5C;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,YAAY;AAAG,mBAAO;AAC/D;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,YAAY;AAAG,mBAAO;AAC/D;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,OAAO;AAAG,mBAAO;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,aAAa;AAAG,mBAAO;AAChE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,OAAO;AAAG,mBAAO;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,iBAAiB;AAAG,mBAAO;AACpE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,OAAO;AAA8C,mBAAO;AACpF;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAA0C,iBAAK,UAAU,OAAO;AACrE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,gBAAI,SAAO,IAAI,OAAO,MAAM,GAAE,EAAE,EAAE,KAAK;AAA8C,mBAAO;AACpH;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,gBAAI,SAAO,IAAI,OAAO,MAAM,GAAE,EAAE,EAAE,KAAK;AAA6C,mBAAO;AACnH;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,gBAAI,SAAO,IAAI,OAAO,MAAM,GAAE,GAAG,EAAE,KAAK;AAA6C,mBAAO;AACpH;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,gBAAI,SAAO,IAAI,OAAO,MAAM,GAAE,EAAE,EAAE,KAAK;AAA6C,mBAAO;AACnH;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,gBAAI,SAAO,IAAI,OAAO,MAAM,GAAE,EAAE,EAAE,KAAK;AAA6C,mBAAO;AACnH;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,gBAAI,SAAO,IAAI,OAAO,MAAM,GAAE,GAAG,EAAE,KAAK;AAA6C,mBAAO;AACpH;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAgD,iBAAK,UAAU,cAAc;AAClF;AAAA,UACA,KAAK;AAAI,iBAAK,UAAU,UAAU;AAA6C,mBAAO;AACtF;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAA8C,mBAAO;AAC5E;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAwD,mBAAO;AACpE;AAAA,UACA,KAAK;AAAqD,mBAAO;AACjE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,QAAQ;AAAkD,mBAAO;AAC1G;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAsC,iBAAK,SAAS;AAAG,mBAAO;AACnE;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,SAAS;AAAG,mBAAO;AAC5D;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,SAAS;AAAG,mBAAO;AAC5D;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,eAAe;AACxD;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,kBAAkB;AAAG,mBAAO;AACrE;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAA2D,mBAAO;AACvE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAqD,mBAAO;AACnF;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,UAAU,WAAW;AAAmD,mBAAO;AAC9G;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAyD,gBAAI,SAAS,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK;AAAG,mBAAO;AACjI;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAyD,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAE,EAAE,EAAE,KAAK;AAAG,mBAAO;AACnI;AAAA,UACA,KAAK;AAA4D,mBAAO;AACxE;AAAA,UACA,KAAK;AAA4D,mBAAO;AACxE;AAAA,UACA,KAAK;AAAqD,mBAAO;AACjE;AAAA,UACA,KAAK;AAAiD,mBAAO;AAC7D;AAAA,UACA,KAAK;AAA2C,mBAAO;AACvD;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS,IAAI,OAAO,KAAK;AAA8C,mBAAO;AAC3F;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GAxKe;AAAA,MAyKf,OAAO,CAAC,iBAAgB,gBAAe,iBAAgB,mBAAkB,gCAA+B,gCAA+B,gCAA+B,gCAA+B,wBAAuB,uBAAsB,eAAc,eAAc,qBAAoB,iBAAgB,iBAAgB,kBAAiB,aAAY,oBAAmB,yBAAwB,yBAAwB,yBAAwB,yBAAwB,0BAAyB,cAAa,gBAAe,qBAAoB,oBAAmB,gBAAe,gBAAe,kBAAiB,4BAA2B,gBAAe,kBAAiB,mBAAkB,gBAAe,kBAAiB,aAAY,oBAAmB,kBAAiB,oBAAmB,oBAAmB,sBAAqB,wBAAuB,wBAAuB,0BAAyB,gCAA+B,gCAA+B,gCAA+B,gCAA+B,aAAY,kBAAiB,kBAAiB,aAAY,eAAc,oBAAmB,YAAW,YAAW,wBAAuB,YAAW,cAAa,iBAAgB,mBAAkB,oBAAmB,WAAU,kBAAiB,aAAY,eAAc,gBAAe,wBAAuB,sBAAqB,4BAA2B,yBAAwB,4BAA2B,kCAAiC,gBAAe,uBAAsB,sBAAqB,aAAY,YAAW,aAAY,WAAU,SAAS;AAAA,MAC5hD,YAAY,EAAC,QAAO,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,oBAAmB,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,iBAAgB,EAAC,SAAQ,CAAC,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,sBAAqB,EAAC,SAAQ,CAAC,GAAE,aAAY,MAAK,GAAE,mBAAkB,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,eAAc,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,cAAa,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,YAAW,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,uBAAsB,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,GAAE,aAAY,MAAK,GAAE,YAAW,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,gBAAe,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,cAAa,EAAC,SAAQ,CAAC,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,MAAK,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IAC/2C;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,uBAAQ;;;AC74BT,IAAM,4BAA4B;AAGlC,IAAM,yBAAyB;AAG/B,IAAM,iBAAiB;AAGvB,IAAM,aAAa;AAGnB,IAAM,YAAY;AAGlB,IAAM,gBAAgB;AAEtB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AAEtB,IAAM,kBAAkB;AAExB,IAAM,qBAAqB;AAC3B,IAAM,eAAe;AAGrB,IAAM,eAAe;AACrB,IAAM,wBAAwB;AAC9B,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AAEzB,IAAM,cAAc;AACpB,IAAM,wBAAwB;AAC9B,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,kBAAkB;AAGxB,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,oBAAoB,GAAG,WAAW,IAAI,SAAS;AACrD,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,gBAAgB;AACtB,IAAM,qBAAqB,GAAG,QAAQ,IAAI,aAAa;AACvD,IAAM,mBAAmB,GAAG,WAAW,IAAI,QAAQ;AACnD,IAAM,cAAc;AACpB,IAAM,sBAAsB,GAAG,WAAW,IAAI,WAAW;AACzD,IAAM,kBAAkB;AACxB,IAAM,0BAA0B,GAAG,WAAW,IAAI,eAAe;AAEjE,IAAM,SAAS;AACf,IAAM,OAAO;AACb,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,UAAU,GAAG,iBAAiB,GAAG,IAAI;AAC3C,IAAM,YAAY,GAAG,iBAAiB,GAAG,MAAM;;;AC/C/C,IAAM,SAAS,wBAAC,YAAiB,aAAa,2BAA2B;AAC9E,MAAI,CAAC,WAAW,KAAK;AACnB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM;AAEV,aAAW,iBAAiB,WAAW,KAAK;AAC1C,QAAI,cAAc,SAAS,OAAO;AAChC,YAAM,cAAc;AAAA,IACtB;AAAA,EACF;AAEA,SAAO;AACT,GAdsB;AAgBf,IAAM,aAAa,gCACxB,MACA,YACmC;AACnC,SAAO,WAAW,GAAG,WAAW;AAClC,GAL0B;AAOnB,IAAM,OAAO,sCAAgB,MAAc,IAAY,UAAkB,MAAW;AACzF,MAAI,KAAK,OAAO;AAChB,MAAI,KAAK,8BAA8B,EAAE;AACzC,QAAM,EAAE,eAAe,OAAO,MAAM,OAAO,IAAI,UAAU;AAGzD,OAAK,GAAG,QAAQ,KAAK,GAAG,aAAa,CAAC;AAMtC,QAAM,cAAc,KAAK,GAAG,QAAQ;AAGpC,QAAM,MAAM,kBAAkB,IAAI,aAAa;AAE/C,cAAY,OAAO,KAAK;AACxB,cAAY,kBAAkB;AAI9B,cAAY,cAAc,MAAM,eAAe;AAC/C,cAAY,cAAc,MAAM,eAAe;AAC/C,cAAY,UAAU,CAAC,MAAM;AAC7B,cAAY,YAAY;AAExB,QAAM,OAAO,aAAa,GAAG;AAC7B,QAAM,UAAU;AAGhB,MAAI;AACF,UAAM,QACJ,OAAO,KAAK,GAAG,aAAa,aAAa,KAAK,GAAG,SAAS,IAAI,oBAAI,IAAI;AAIxE,UAAM,QAAQ,CAAC,UAAU,QAAkB;AACzC,YAAM,UAAU,OAAO,QAAQ,WAAW,MAAM,OAAO,KAAK,OAAO,WAAW,IAAI,KAAK;AAEvF,UAAI,CAAC,SAAS;AACZ,YAAI,KAAK,qDAA2C,KAAK,UAAU,GAAG,CAAC;AACvE;AAAA,MACF;AAEA,YAAM,WAAW,IAAI,KAAK,GAAG,iBAAiB,GAAG;AACjD,UAAI;AAEJ,gBAAU,QAAQ,CAAC,MAAmB;AACpC,cAAME,QAAO,EAAE,aAAa,KAAK;AACjC,YAAIA,UAAS,SAAS;AACpB,wBAAc;AAAA,QAChB;AAAA,MACF,CAAC;AAED,UAAI,CAAC,aAAa;AAChB,YAAI,KAAK,mDAAyC,OAAO;AACzD;AAAA,MACF;AAEA,YAAM,SAAS,YAAY;AAC3B,UAAI,CAAC,QAAQ;AACX,YAAI,KAAK,iDAAuC,OAAO;AACvD;AAAA,MACF;AAEA,YAAM,IAAI,SAAS,gBAAgB,8BAA8B,GAAG;AACpE,YAAM,aAAa,SAAS,IAAI,QAAQ,YAAY,EAAE;AACtD,QAAE,eAAe,gCAAgC,cAAc,UAAU;AACzE,QAAE,aAAa,UAAU,QAAQ;AACjC,UAAI,SAAS,SAAS;AACpB,cAAM,UAAU,SAAS,QAAQ,QAAQ,YAAY,EAAE;AACvD,UAAE,aAAa,SAAS,OAAO;AAAA,MACjC;AAEA,aAAO,aAAa,GAAG,WAAW;AAClC,QAAE,YAAY,WAAW;AAEzB,UAAI,KAAK,0CAAmC,SAAS,SAAS,GAAG;AAAA,IACnE,CAAC;AAAA,EACH,SAAS,KAAK;AACZ,QAAI,MAAM,2CAAsC,GAAG;AAAA,EACrD;AAEA,gBAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM,kBAAkB;AAAA,IACxB,KAAK,GAAG,gBAAgB;AAAA,EAC1B;AACA,sBAAoB,KAAK,SAAS,aAAa,MAAM,eAAe,IAAI;AAC1E,GA3FoB;AA6FpB,IAAO,mCAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF;;;ACnGA,IAAM,SAAS,oBAAI,IAAsB;AAEzC,IAAI,iBAAiB;AAOd,SAAS,WACd,SAAS,IACT,UAAU,GACV,OAAsB,IACtB,aAAa,mBACb;AACA,QAAM,UAAU,SAAS,QAAQ,KAAK,SAAS,IAAI,GAAG,UAAU,GAAG,IAAI,KAAK;AAC5E,SAAO,GAAG,WAAW,IAAI,MAAM,GAAG,OAAO,IAAI,OAAO;AACtD;AARgB;AAUhB,IAAM,WAAW,wBACf,kBACA,KACA,eACA,OACA,OACA,SACA,MACA,YACG;AAEH,MAAI,MAAM,SAAS,GAAG;AACtB,MAAI,QAAQ,CAAC,SAAS;AACpB,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK;AACH,oBAAY,kBAAkB,MAAM,eAAe,OAAO,OAAO,SAAS,MAAM,OAAO;AACvF;AAAA,MACF,KAAK;AACH,oBAAY,kBAAkB,MAAM,eAAe,OAAO,OAAO,SAAS,MAAM,OAAO;AACvF;AAAA,MACF,KAAK;AACH;AACE;AAAA,YACE;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA;AAAA,YACE;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,gBAAM,WAAW;AAAA,YACf,IAAI,SAAS;AAAA,YACb,OAAO,KAAK,OAAO;AAAA,YACnB,KAAK,KAAK,OAAO;AAAA,YACjB,WAAW;AAAA,YACX,cAAc;AAAA,YACd,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,OAAO,eAAO,aAAa,KAAK,eAAe,IAAI,UAAU,CAAC;AAAA,YAC9D,gBAAgB;AAAA,YAChB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,WAAW;AAAA,YACX,SAAS;AAAA,YACT;AAAA,UACF;AACA,gBAAM,KAAK,QAAQ;AACnB;AAAA,QACF;AACA;AAAA,IACJ;AAAA,EACF,CAAC;AACH,GAhEiB;AAyEjB,IAAMC,UAAS,wBAAC,YAA8B,aAAa,2BAA2B;AACpF,MAAI,MAAM;AACV,MAAI,WAAW,KAAK;AAClB,eAAW,iBAAiB,WAAW,KAAK;AAC1C,UAAI,cAAc,SAAS,OAAO;AAChC,cAAM,cAAc;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GAVe;AAYf,SAAS,mBACP,OACA,UACA,SACA;AACA,MAAI,CAAC,SAAS,MAAM,SAAS,OAAO,oBAAoB,SAAS,OAAO,aAAa;AACnF;AAAA,EACF;AAGA,MAAI,SAAS,YAAY;AACvB,QAAI,CAAC,MAAM,QAAQ,SAAS,iBAAiB,GAAG;AAC9C,eAAS,oBAAoB,CAAC;AAAA,IAChC;AAEA,aAAS,WAAW,MAAM,GAAG,EAAE,QAAQ,CAAC,aAAa;AACnD,YAAM,WAAW,QAAQ,IAAI,QAAQ;AACrC,UAAI,UAAU;AACZ,iBAAS,oBAAoB,CAAC,GAAI,SAAS,qBAAqB,CAAC,GAAI,GAAG,SAAS,MAAM;AAAA,MACzF;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,MAAM,KAAK,CAAC,SAAS,KAAK,OAAO,SAAS,EAAE;AACrE,MAAI,kBAAkB;AAEpB,WAAO,OAAO,kBAAkB,QAAQ;AAAA,EAC1C,OAAO;AACL,UAAM,KAAK,QAAQ;AAAA,EACrB;AACF;AA7BS;AAoCT,SAAS,qBAAqB,YAAgC;AAC5D,SAAO,YAAY,SAAS,KAAK,GAAG,KAAK;AAC3C;AAFS;AAIT,SAAS,oBAAoB,YAAkC;AAC7D,SAAO,YAAY,UAAU,CAAC;AAChC;AAFS;AAIF,IAAM,cAAc,wBACzB,QACA,YACA,eACA,OACA,OACA,SACA,MACA,YACG;AACH,QAAM,SAAS,WAAW;AAC1B,QAAM,UAAU,cAAc,IAAI,MAAM;AACxC,QAAM,WAAW,qBAAqB,OAAO;AAC7C,QAAM,QAAQ,oBAAoB,OAAO;AACzC,QAAM,SAAS,UAAU;AAEzB,MAAI,KAAK,0BAA0B,YAAY,SAAS,KAAK;AAE7D,MAAI,WAAW,QAAQ;AACrB,QAAI,QAAQ;AAEZ,QAAI,WAAW,UAAU,MAAM;AAC7B,cAAQ;AAAA,IACV,WAAW,WAAW,UAAU,OAAO;AACrC,cAAQ;AAAA,IACV;AACA,QAAI,WAAW,SAAS,oBAAoB;AAC1C,cAAQ,WAAW;AAAA,IACrB;AAGA,QAAI,CAAC,OAAO,IAAI,MAAM,GAAG;AACvB,aAAO,IAAI,QAAQ;AAAA,QACjB,IAAI;AAAA,QACJ;AAAA,QACA,aAAa,eAAO,aAAa,QAAQ,MAAM;AAAA,QAC/C,YAAY,GAAG,QAAQ,IAAI,iBAAiB;AAAA,QAC5C,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAEA,UAAM,UAAU,OAAO,IAAI,MAAM;AAMjC,QAAI,WAAW,aAAa;AAC1B,UAAI,MAAM,QAAQ,QAAQ,WAAW,GAAG;AAEtC,gBAAQ,QAAQ;AAChB,gBAAQ,YAAY,KAAK,WAAW,WAAW;AAAA,MACjD,OAAO;AACL,YAAI,QAAQ,aAAa,UAAU,QAAQ,YAAY,SAAS,GAAG;AAEjE,kBAAQ,QAAQ;AAChB,cAAI,QAAQ,gBAAgB,QAAQ;AAElC,oBAAQ,cAAc,CAAC,WAAW,WAAW;AAAA,UAC/C,OAAO;AACL,oBAAQ,cAAc,CAAC,QAAQ,aAAa,WAAW,WAAW;AAAA,UACpE;AAAA,QACF,OAAO;AACL,kBAAQ,QAAQ;AAChB,kBAAQ,cAAc,WAAW;AAAA,QACnC;AAAA,MACF;AACA,cAAQ,cAAc,eAAO,oBAAoB,QAAQ,aAAa,MAAM;AAAA,IAC9E;AAGA,QAAI,QAAQ,aAAa,WAAW,KAAK,QAAQ,UAAU,uBAAuB;AAChF,UAAI,QAAQ,SAAS,SAAS;AAC5B,gBAAQ,QAAQ;AAAA,MAClB,OAAO;AACL,gBAAQ,QAAQ;AAAA,MAClB;AAAA,IACF;AAGA,QAAI,CAAC,QAAQ,QAAQ,WAAW,KAAK;AACnC,UAAI,KAAK,2BAA2B,QAAQA,QAAO,UAAU,CAAC;AAC9D,cAAQ,OAAO;AACf,cAAQ,UAAU;AAClB,cAAQ,MAAMA,QAAO,UAAU;AAC/B,cAAQ,QAAQ,WAAW,SAAS,eAAe,gBAAgB;AACnE,cAAQ,aAAa,GAAG,QAAQ,UAAU,IAAI,mBAAmB,IAAI,UAAU,0BAA0B,EAAE;AAAA,IAC7G;AAGA,UAAM,WAAqB;AAAA,MACzB,YAAY;AAAA,MACZ,OAAO,QAAQ;AAAA,MACf,OAAO,QAAQ;AAAA,MACf,YAAY,QAAQ;AAAA,MACpB,mBAAmB,CAAC;AAAA,MACpB,WAAW,QAAQ;AAAA,MACnB,IAAI;AAAA,MACJ,KAAK,QAAQ;AAAA,MACb,OAAO,WAAW,QAAQ,cAAc;AAAA,MACxC,MAAM,QAAQ;AAAA,MACd,SAAS,QAAQ,SAAS;AAAA,MAC1B,SAAS;AAAA,MACT,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ;AAAA,IACF;AAGA,QAAI,SAAS,UAAU,eAAe;AACpC,eAAS,QAAQ;AAAA,IACnB;AAEA,QAAI,UAAU,OAAO,OAAO,QAAQ;AAClC,UAAI,MAAM,iBAAiB,QAAQ,+BAA+B,OAAO,EAAE;AAC3E,eAAS,WAAW,OAAO;AAAA,IAC7B;AAEA,aAAS,cAAc;AAEvB,QAAI,WAAW,MAAM;AAEnB,YAAM,WAAqB;AAAA,QACzB,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,OAAO,WAAW,KAAK;AAAA,QACvB,YAAY;AAAA;AAAA,QAEZ,WAAW,CAAC;AAAA,QACZ,mBAAmB,CAAC;AAAA,QACpB,IAAI,SAAS,UAAU,MAAM;AAAA,QAC7B,OAAO,WAAW,QAAQ,gBAAgB,IAAI;AAAA,QAC9C,MAAM,QAAQ;AAAA,QACd,SAAS,QAAQ,SAAS;AAAA,QAC1B,SAAS,OAAO,WAAW;AAAA,QAC3B;AAAA,QACA,UAAU,WAAW,KAAK;AAAA,MAC5B;AACA,YAAM,eAAe,SAAS;AAC9B,YAAM,YAAY;AAAA,QAChB,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,OAAO,WAAW,KAAK;AAAA,QACvB,YAAY,QAAQ;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,IAAI,SAAS;AAAA,QACb,OAAO,WAAW,QAAQ,gBAAgB,MAAM;AAAA,QAChD,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA;AAAA,QACT;AAAA,QACA,UAAU,WAAW,KAAK;AAAA,MAC5B;AACA;AAGA,gBAAU,KAAK;AAEf,eAAS,WAAW;AAIpB,yBAAmB,OAAO,WAAW,OAAO;AAE5C,yBAAmB,OAAO,UAAU,OAAO;AAE3C,yBAAmB,OAAO,UAAU,OAAO;AAE3C,UAAI,OAAO;AACX,UAAI,KAAK,SAAS;AAElB,UAAI,WAAW,KAAK,aAAa,WAAW;AAC1C,eAAO,SAAS;AAChB,aAAK;AAAA,MACP;AAEA,YAAM,KAAK;AAAA,QACT,IAAI,OAAO,MAAM;AAAA,QACjB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,cAAc;AAAA,QACd,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,yBAAmB,OAAO,UAAU,OAAO;AAAA,IAC7C;AAAA,EACF;AACA,MAAI,WAAW,KAAK;AAClB,QAAI,MAAM,wBAAwB;AAClC,aAAS,YAAY,WAAW,KAAK,eAAe,OAAO,OAAO,CAAC,SAAS,MAAM,OAAO;AAAA,EAC3F;AACF,GAvM2B;AAyMpB,IAAM,QAAQ,6BAAM;AACzB,SAAO,MAAM;AACb,mBAAiB;AACnB,GAHqB;;;ACvWrB,IAAM,YAAY;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,cAAc;AAAA,EACd,SAAS;AAAA,EACT,gBAAgB;AAClB;AAqJA,IAAM,iBAAiB,6BAA+B,oBAAI,IAAI,GAAvC;AACvB,IAAM,SAAS,8BAAiB;AAAA,EAC9B,WAAW,CAAC;AAAA,EACZ,QAAQ,oBAAI,IAAI;AAAA,EAChB,WAAW,CAAC;AACd,IAJe;AAKf,IAAM,QAAQ,wBAAI,MAAY,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC,GAA5C;AAEP,IAAM,UAAN,MAAc;AAAA,EAkBnB,YAAoB,SAAgB;AAAhB;AAjBpB,SAAQ,QAAoB,CAAC;AAC7B,SAAQ,QAAgB,CAAC;AACzB,SAAQ,UAAkB,CAAC;AAC3B,SAAQ,UAAU,eAAe;AACjC,SAAQ,YAAY,EAAE,MAAM,OAAO,EAAE;AACrC,SAAQ,kBAAkB,KAAK,UAAU;AACzC,SAAQ,gBAAgB;AACxB,SAAQ,aAAa;AACrB,SAAQ,QAAQ,oBAAI,IAA8C;AAugBlE,uBAAc;AACd,uBAAc;AACd,6BAAoB;AACpB,6BAAoB;AACpB,2BAAkB;AAClB,2BAAkB;AAlgBhB,SAAK,MAAM;AAEX,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AAAA,EAC3C;AAAA,EA5NF,OAmMqB;AAAA;AAAA;AAAA,EAWnB;AAAA,SAAgB,eAAe;AAAA,MAC7B,aAAa;AAAA,MACb,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,QAAQ,YAAsC;AAC5C,SAAK,MAAM,IAAI;AACf,eAAW,QAAQ,MAAM,QAAQ,UAAU,IAAI,aAAa,WAAW,KAAK;AAC1E,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACH,eAAK,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,MAAM,KAAK,KAAK,KAAK,aAAa,KAAK,IAAI;AAC9E;AAAA,QACF,KAAK;AACH,eAAK,YAAY,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;AAC3D;AAAA,QACF,KAAK;AACH,eAAK,cAAc,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO;AAC/C;AAAA,QACF,KAAK;AACH,eAAK,eAAe,IAAI;AACxB;AAAA,QACF,KAAK;AACH,eAAK,YAAY,KAAK,GAAG,KAAK,GAAG,KAAK,UAAU;AAChD;AAAA,QACF,KAAK;AACH,eAAK,QAAQ,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO;AAC5C;AAAA,MACJ;AAAA,IACF;AACA,UAAM,gBAAgB,KAAK,UAAU;AACrC,UAAM,SAAS,UAAU;AAEzB,UAAiB;AACjB;AAAA,MACE;AAAA,MACA,KAAK,aAAa;AAAA,MAClB;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAGA,eAAW,QAAQ,KAAK,OAAO;AAC7B,UAAI,CAAC,MAAM,QAAQ,KAAK,KAAK,GAAG;AAC9B;AAAA,MACF;AAEA,WAAK,cAAc,KAAK,MAAM,MAAM,CAAC;AACrC,UAAI,KAAK,WAAW,KAAK,YAAY,SAAS,GAAG;AAC/C,cAAM,IAAI;AAAA,UACR,gFAAgF,KAAK,EAAE;AAAA,QACzF;AAAA,MACF;AACA,WAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EAEQ,eAAe,MAAiB;AACtC,UAAM,MAAM,KAAK,GAAG,KAAK,EAAE,MAAM,GAAG;AACpC,UAAM,SAAS,KAAK,WAAW,MAAM,GAAG;AAExC,eAAW,MAAM,KAAK;AACpB,UAAI,QAAQ,KAAK,SAAS,EAAE;AAC5B,UAAI,CAAC,OAAO;AACV,cAAM,YAAY,GAAG,KAAK;AAC1B,aAAK,SAAS,SAAS;AACvB,gBAAQ,KAAK,SAAS,SAAS;AAAA,MACjC;AACA,UAAI,OAAO;AACT,cAAM,SAAS,OAAO,IAAI,CAAC,MAAM,EAAE,QAAQ,MAAM,EAAE,GAAG,KAAK,CAAC;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,GAAW;AACpB,QAAI,KAAK,oBAAoB,CAAC;AAC9B,SAAK,UAAU;AACf,QAAI,KAAK,YAAY,GAAG;AACtB,WAAK,QAAQ,CAAC;AAAA,IAChB,OAAO;AACL,WAAK,QAAQ,KAAK,aAAa,CAAC;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,cAAc,QAA8B,MAAY,OAAgB;AACtE,QAAI,KAAK,SAAS,eAAe;AAC/B,WAAK,cAAc,QAAQ,KAAK,QAAQ,IAAI;AAC5C,WAAK,cAAc,QAAQ,KAAK,QAAQ,KAAK;AAC7C;AAAA,IACF;AAEA,QAAI,KAAK,SAAS,YAAY;AAC5B,UAAI,KAAK,OAAO,UAAU,YAAY;AACpC,aAAK,KAAK,OAAO,MAAM,QAAQ,WAAW;AAC1C,aAAK,QAAQ;AAAA,MACf,OAAO;AAEL,aAAK,KAAK,KAAK,GAAG,KAAK;AAAA,MACzB;AAAA,IACF;AAEA,QAAK,KAAK,SAAS,aAAa,KAAK,SAAS,cAAe,CAAC,KAAK,KAAK;AACtE;AAAA,IACF;AAEA,UAAM,MAAM,CAAC;AAEb,QAAI,aAAa,CAAC;AAClB,eAAW,QAAQ,KAAK,KAAK;AAC3B,UAAK,KAAmB,SAAS,cAAc;AAC7C,cAAM,UAAU,MAAM,IAAiB;AACvC,gBAAQ,MAAM,MAAM,UAAU;AAC9B,YAAI,KAAK,OAAO;AAChB,qBAAa,CAAC;AAAA,MAChB,OAAO;AACL,mBAAW,KAAK,IAAI;AAAA,MACtB;AAAA,IACF;AAGA,QAAI,IAAI,SAAS,KAAK,WAAW,SAAS,GAAG;AAC3C,YAAM,UAAU;AAAA,QACd,MAAM;AAAA,QACN,IAAI,WAAW;AAAA,QACf,MAAM;AAAA,QACN,KAAK,MAAM,UAAU;AAAA,MACvB;AACA,UAAI,KAAK,MAAM,OAAO,CAAC;AACvB,WAAK,MAAM;AAAA,IACb;AAEA,SAAK,IAAI,QAAQ,CAAC,YAAY,KAAK,cAAc,MAAM,SAAS,IAAI,CAAC;AAAA,EACvE;AAAA,EAEQ,eAAe;AACrB,SAAK;AAAA,MACH,EAAE,IAAI,WAAW,MAAM,UAAU;AAAA,MACjC,EAAE,IAAI,WAAW,MAAM,WAAW,KAAK,KAAK,QAAQ;AAAA,MACpD;AAAA,IACF;AACA,WAAO,EAAE,IAAI,WAAW,KAAK,KAAK,QAAQ;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,SACE,IACA,OAA0B,oBAC1B,MAA0B,QAC1B,QAAuC,QACvC,OAAyB,QACzB,UAAyC,QACzC,SAAwC,QACxC,aAA4C,QAC5C;AACA,UAAM,YAAY,IAAI,KAAK;AAC3B,QAAI,CAAC,KAAK,gBAAgB,OAAO,IAAI,SAAS,GAAG;AAC/C,UAAI,KAAK,iBAAiB,WAAW,KAAK;AAC1C,WAAK,gBAAgB,OAAO,IAAI,WAAW;AAAA,QACzC,MAAM;AAAA,QACN,IAAI;AAAA,QACJ,cAAc,CAAC;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,CAAC;AAAA,QACV,QAAQ,CAAC;AAAA,QACT,YAAY,CAAC;AAAA,MACf,CAAC;AAAA,IACH,OAAO;AACL,YAAM,QAAQ,KAAK,gBAAgB,OAAO,IAAI,SAAS;AACvD,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,oBAAoB,SAAS,EAAE;AAAA,MACjD;AACA,UAAI,CAAC,MAAM,KAAK;AACd,cAAM,MAAM;AAAA,MACd;AACA,UAAI,CAAC,MAAM,MAAM;AACf,cAAM,OAAO;AAAA,MACf;AAAA,IACF;AAEA,QAAI,OAAO;AACT,UAAI,KAAK,6BAA6B,WAAW,KAAK;AACtD,YAAM,eAAe,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC1D,mBAAa,QAAQ,CAAC,QAAQ,KAAK,eAAe,WAAW,IAAI,KAAK,CAAC,CAAC;AAAA,IAC1E;AAEA,QAAI,MAAM;AACR,YAAM,OAAO,KAAK,gBAAgB,OAAO,IAAI,SAAS;AACtD,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,oBAAoB,SAAS,EAAE;AAAA,MACjD;AACA,WAAK,OAAO;AACZ,WAAK,KAAK,OAAO,eAAO,aAAa,KAAK,KAAK,MAAM,UAAU,CAAC;AAAA,IAClE;AAEA,QAAI,SAAS;AACX,UAAI,KAAK,yBAAyB,WAAW,OAAO;AACpD,YAAM,cAAc,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAC/D,kBAAY,QAAQ,CAAC,aAAa,KAAK,YAAY,WAAW,SAAS,KAAK,CAAC,CAAC;AAAA,IAChF;AAEA,QAAI,QAAQ;AACV,UAAI,KAAK,wBAAwB,WAAW,MAAM;AAClD,YAAM,aAAa,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAC3D,iBAAW,QAAQ,CAAC,UAAU,KAAK,SAAS,WAAW,MAAM,KAAK,CAAC,CAAC;AAAA,IACtE;AAEA,QAAI,YAAY;AACd,UAAI,KAAK,wBAAwB,WAAW,MAAM;AAClD,YAAM,iBAAiB,MAAM,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU;AAC3E,qBAAe,QAAQ,CAAC,cAAc,KAAK,aAAa,WAAW,UAAU,KAAK,CAAC,CAAC;AAAA,IACtF;AAAA,EACF;AAAA,EAEA,MAAM,YAAsB;AAC1B,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,CAAC;AACd,SAAK,YAAY,EAAE,MAAM,OAAO,EAAE;AAClC,SAAK,kBAAkB,KAAK,UAAU;AAGtC,SAAK,gBAAgB;AACrB,SAAK,UAAU,eAAe;AAC9B,QAAI,CAAC,YAAY;AACf,WAAK,QAAQ,oBAAI,IAAI;AACrB,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EAEA,SAAS,IAAY;AACnB,WAAO,KAAK,gBAAgB,OAAO,IAAI,EAAE;AAAA,EAC3C;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EAEA,eAAe;AACb,QAAI,KAAK,gBAAgB,KAAK,SAAS;AAAA,EACzC;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,SAAiB,KAAa,SAAuB;AAC3D,SAAK,MAAM,IAAI,SAAS,EAAE,KAAK,QAAQ,CAAC;AACxC,QAAI,KAAK,eAAe,SAAS,KAAK,OAAO;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKA,WAA0D;AACxD,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,KAAK,IAAI;AACvB,QAAI,OAAO,UAAU,YAAY;AAC/B,WAAK;AACL,aAAO,GAAG,UAAU,UAAU,GAAG,KAAK,aAAa;AAAA,IACrD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,KAAK,IAAI,OAA0B,oBAAoB;AACvE,WAAO,OAAO,UAAU,aAAa,UAAU,aAAa;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,KAAK,IAAI;AACrB,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK;AACL,aAAO,GAAG,UAAU,QAAQ,GAAG,KAAK,aAAa;AAAA,IACnD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,KAAK,IAAI,OAA0B,oBAAoB;AACrE,WAAO,OAAO,UAAU,WAAW,UAAU,WAAW;AAAA,EAC1D;AAAA,EAEA,gBAAgB,OAAkB,OAAkB,gBAAgB,IAAI;AACtE,UAAM,MAAM,KAAK,gBAAgB,MAAM,GAAG,KAAK,CAAC;AAChD,UAAM,QAAQ,KAAK,kBAAkB,MAAM,GAAG,KAAK,GAAG,MAAM,IAAI;AAChE,UAAM,MAAM,KAAK,gBAAgB,MAAM,GAAG,KAAK,CAAC;AAChD,UAAM,QAAQ,KAAK,kBAAkB,MAAM,GAAG,KAAK,GAAG,MAAM,IAAI;AAChE,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AACA,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AACA,SAAK,gBAAgB,UAAU,KAAK;AAAA,MAClC;AAAA,MACA;AAAA,MACA,eAAe,eAAO,aAAa,eAAe,UAAU,CAAC;AAAA,IAC/D,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,OAA2B,OAA2B,OAAgB;AAChF,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAC1D,WAAK,gBAAgB,OAAO,OAAO,KAAK;AAAA,IAC1C,WAAW,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AACjE,YAAM,MAAM,KAAK,gBAAgB,MAAM,KAAK,CAAC;AAC7C,YAAM,QAAQ,KAAK,kBAAkB,KAAK;AAC1C,YAAM,MAAM,KAAK,cAAc,MAAM,KAAK,CAAC;AAC3C,YAAM,QAAQ,KAAK,gBAAgB,KAAK;AAExC,WAAK,SAAS,KAAK,KAAK;AACxB,WAAK,SAAS,KAAK,KAAK;AACxB,WAAK,gBAAgB,UAAU,KAAK;AAAA,QAClC;AAAA,QACA;AAAA,QACA,eAAe,QAAQ,eAAO,aAAa,OAAO,UAAU,CAAC,IAAI;AAAA,MACnE,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,eAAe,IAAY,OAAe;AACxC,UAAM,WAAW,KAAK,gBAAgB,OAAO,IAAI,EAAE;AACnD,UAAM,SAAS,MAAM,WAAW,GAAG,IAAI,MAAM,QAAQ,KAAK,EAAE,EAAE,KAAK,IAAI;AACvE,cAAU,cAAc,KAAK,eAAO,aAAa,QAAQ,UAAU,CAAC,CAAC;AAAA,EACvE;AAAA,EAEA,aAAa,OAAe;AAC1B,WAAO,MAAM,WAAW,GAAG,IAAI,MAAM,MAAM,CAAC,EAAE,KAAK,IAAI,MAAM,KAAK;AAAA,EACpE;AAAA,EAEA,eAAe;AACb,SAAK;AACL,WAAO,cAAc,KAAK,UAAU;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,IAAY,kBAAkB,IAAI;AAE9C,QAAI,CAAC,KAAK,QAAQ,IAAI,EAAE,GAAG;AACzB,WAAK,QAAQ,IAAI,IAAI,EAAE,IAAI,QAAQ,CAAC,GAAG,YAAY,CAAC,EAAE,CAAC;AAAA,IACzD;AACA,UAAM,aAAa,KAAK,QAAQ,IAAI,EAAE;AACtC,QAAI,mBAAmB,YAAY;AACjC,sBAAgB,MAAM,UAAU,cAAc,EAAE,QAAQ,CAAC,WAAW;AAClE,cAAM,cAAc,OAAO,QAAQ,YAAY,IAAI,EAAE,KAAK;AAC1D,YAAI,OAAO,UAAU,aAAa,EAAE,KAAK,MAAM,GAAG;AAChD,gBAAM,YAAY,YAAY,QAAQ,UAAU,cAAc,UAAU,OAAO;AAC/E,gBAAM,YAAY,UAAU,QAAQ,UAAU,eAAe,UAAU,YAAY;AACnF,qBAAW,WAAW,KAAK,SAAS;AAAA,QACtC;AACA,mBAAW,OAAO,KAAK,WAAW;AAAA,MACpC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,SAAiB,cAAsB;AACjD,YAAQ,MAAM,GAAG,EAAE,QAAQ,CAAC,OAAO;AACjC,UAAI,aAAa,KAAK,SAAS,EAAE;AACjC,UAAI,CAAC,YAAY;AACf,cAAM,YAAY,GAAG,KAAK;AAC1B,aAAK,SAAS,SAAS;AACvB,qBAAa,KAAK,SAAS,SAAS;AAAA,MACtC;AACA,kBAAY,SAAS,KAAK,YAAY;AAAA,IACxC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,SAAS,QAAgB,WAAmB;AAC1C,SAAK,SAAS,MAAM,GAAG,QAAQ,KAAK,SAAS;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,QAAgB,cAAsB;AACjD,SAAK,SAAS,MAAM,GAAG,YAAY,KAAK,YAAY;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMQ,wBAAwB;AAC9B,WAAO,KAAK,QAAQ,KAAK,CAAC,QAA8B,IAAI,SAAS,cAAc;AAAA,EACrF;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,sBAAsB,GAAG,SAAS;AAAA,EAChD;AAAA,EAEA,aAAa,KAA6B;AACxC,UAAM,MAAM,KAAK,sBAAsB;AACvC,QAAI,KAAK;AACP,UAAI,QAAQ;AAAA,IACd,OAAO;AACL,WAAK,QAAQ,QAAQ,EAAE,MAAM,gBAAgB,OAAO,IAAI,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EAEA,UAAU,KAAa;AACrB,WAAO,IAAI,WAAW,GAAG,IAAI,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI,IAAI,KAAK;AAAA,EAC9D;AAAA,EAEA,UAAU;AACR,UAAM,SAAS,UAAU;AACzB,WAAO;AAAA,MACL,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,OAAO,CAAC;AAAA,MACR;AAAA,MACA,WAAW,OAAO,KAAK,aAAa,CAAC;AAAA,IACvC;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO,UAAU,EAAE;AAAA,EACrB;AAQF;;;ACztBA,IAAM,YAAY,wBAAC,YACjB;AAAA;AAAA,YAEU,QAAQ,eAAe;AAAA,cACrB,QAAQ,eAAe;AAAA;AAAA;AAAA,UAG3B,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,UAKlB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOjB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA,UAIvB,QAAQ,OAAO;AAAA,YACb,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAIlB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,YAKjB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMzB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAUhB,QAAQ,eAAe;AAAA,UACzB,QAAQ,YAAY;AAAA;AAAA;AAAA,YAGlB,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASvB,QAAQ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,UAKf,QAAQ,oBAAoB;AAAA;AAAA;AAAA;AAAA,sBAIhB,QAAQ,mBAAmB;AAAA;AAAA,wBAEzB,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA,wBAI3B,QAAQ,mBAAmB;AAAA,YACvC,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,UAK7B,QAAQ,wBAAwB,QAAQ,iBAAiB;AAAA;AAAA;AAAA,WAGxD,QAAQ,wBAAwB,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA,UAI1D,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMvB,QAAQ,iBAAiB;AAAA,YACvB,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA,UAI3B,QAAQ,iBAAiB;AAAA,YACvB,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA,UAI3B,QAAQ,kBAAkB;AAAA,YACxB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,UAIpB,QAAQ,uBAAuB,QAAQ,UAAU;AAAA,eAC5C,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,UAKvB,QAAQ,YAAY,QAAQ,OAAO;AAAA,YACjC,QAAQ,eAAe,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,UAI3C,QAAQ,OAAO;AAAA,YACb,QAAQ,eAAe,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,UAI3C,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,UAIjB,QAAQ,wBAAwB;AAAA,YAC9B,QAAQ,eAAe,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,WAK1C,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAStB,QAAQ,eAAe,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQ3C,QAAQ,uBAAuB,QAAQ,UAAU;AAAA;AAAA;AAAA,UAGjD,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAczD,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQzD,QAAQ,YAAY;AAAA,YAClB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMzB,QAAQ,YAAY;AAAA,YAClB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOzB,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA,WAIpB,QAAQ,aAAa;AAAA;AAAA;AAAA,mBAGb,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA,UAI9B,QAAQ,SAAS;AAAA,YACf,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOnB,QAAQ,SAAS;AAAA;AAAA,GArNT;AA4NlB,IAAO,iBAAQ;", "names": ["o", "parser", "lexer", "text", "getDir"]}