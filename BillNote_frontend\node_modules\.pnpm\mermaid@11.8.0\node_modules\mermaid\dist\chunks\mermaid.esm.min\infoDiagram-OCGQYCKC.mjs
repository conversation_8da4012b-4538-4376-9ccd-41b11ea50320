import{a as s}from"./chunk-VXOIP5OY.mjs";import"./chunk-PYF77AR6.mjs";import"./chunk-Y34LE73S.mjs";import"./chunk-N45264FF.mjs";import"./chunk-T5S727EL.mjs";import"./chunk-2C4AIEI3.mjs";import{a as p}from"./chunk-4SMFJXQ7.mjs";import{a}from"./chunk-ZKOTWRZ5.mjs";import{N as n,b as o}from"./chunk-63ZE7VZ5.mjs";import"./chunk-2KWOQFVZ.mjs";import"./chunk-JQ47BUXN.mjs";import"./chunk-NPFRQMEE.mjs";import"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as r}from"./chunk-GTKDMUJJ.mjs";var m={parse:r(async t=>{let e=await s("info",t);o.debug(e)},"parse")};var c={version:p.version+""},y=r(()=>c.version,"getVersion"),f={getVersion:y};var D=r((t,e,d)=>{o.debug(`rendering info diagram
`+t);let i=a(e);n(i,100,400,!0),i.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${d}`)},"draw"),g={draw:D};var L={parser:m,db:f,renderer:g};export{L as diagram};
