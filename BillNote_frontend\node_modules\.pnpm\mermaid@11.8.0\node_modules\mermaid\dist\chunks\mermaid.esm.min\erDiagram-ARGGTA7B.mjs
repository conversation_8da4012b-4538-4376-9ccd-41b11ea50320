import{a as Pt}from"./chunk-YEYRPSRY.mjs";import{a as Kt}from"./chunk-R4PCWW2Q.mjs";import{b as Gt,c as zt}from"./chunk-G4RV2GLT.mjs";import"./chunk-JLLLWTDQ.mjs";import"./chunk-S67DUUA5.mjs";import"./chunk-JD2HUS54.mjs";import"./chunk-LM6QDVU5.mjs";import"./chunk-LMGRA235.mjs";import"./chunk-TK7VX7YV.mjs";import{m as Bt,p as Yt}from"./chunk-CRSA2SMT.mjs";import"./chunk-TI4EEUUG.mjs";import{Q as It,R as vt,S as Dt,T as wt,U as Lt,V as Vt,W as Mt,Y as w,b as C,ha as Ft,l as Ct,m as xt}from"./chunk-63ZE7VZ5.mjs";import"./chunk-6BY5RJGC.mjs";import{a as h,c as Ht}from"./chunk-GTKDMUJJ.mjs";var pt=function(){var i=h(function(R,n,a,c){for(a=a||{},c=R.length;c--;a[R[c]]=n);return a},"o"),s=[6,8,10,22,24,26,28,33,34,35,36,37,40,43,44,50],o=[1,10],d=[1,11],l=[1,12],u=[1,13],f=[1,20],y=[1,21],_=[1,22],L=[1,23],j=[1,24],S=[1,19],tt=[1,25],Z=[1,26],T=[1,18],V=[1,33],et=[1,34],it=[1,35],st=[1,36],rt=[1,37],ft=[6,8,10,13,15,17,20,21,22,24,26,28,33,34,35,36,37,40,43,44,50,63,64,65,66,67],O=[1,42],N=[1,43],M=[1,52],F=[40,50,68,69],B=[1,63],Y=[1,61],A=[1,58],P=[1,62],G=[1,64],U=[6,8,10,13,17,22,24,26,28,33,34,35,36,37,40,41,42,43,44,48,49,50,63,64,65,66,67],yt=[63,64,65,66,67],gt=[1,81],kt=[1,80],mt=[1,78],_t=[1,79],Et=[6,10,42,47],v=[6,10,13,41,42,47,48,49],W=[1,89],Q=[1,88],X=[1,87],z=[19,56],St=[1,98],Tt=[1,97],nt=[19,56,58,60],at={trace:h(function(){},"trace"),yy:{},symbols_:{error:2,start:3,ER_DIAGRAM:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,entityName:11,relSpec:12,COLON:13,role:14,STYLE_SEPARATOR:15,idList:16,BLOCK_START:17,attributes:18,BLOCK_STOP:19,SQS:20,SQE:21,title:22,title_value:23,acc_title:24,acc_title_value:25,acc_descr:26,acc_descr_value:27,acc_descr_multiline_value:28,direction:29,classDefStatement:30,classStatement:31,styleStatement:32,direction_tb:33,direction_bt:34,direction_rl:35,direction_lr:36,CLASSDEF:37,stylesOpt:38,separator:39,UNICODE_TEXT:40,STYLE_TEXT:41,COMMA:42,CLASS:43,STYLE:44,style:45,styleComponent:46,SEMI:47,NUM:48,BRKT:49,ENTITY_NAME:50,attribute:51,attributeType:52,attributeName:53,attributeKeyTypeList:54,attributeComment:55,ATTRIBUTE_WORD:56,attributeKeyType:57,",":58,ATTRIBUTE_KEY:59,COMMENT:60,cardinality:61,relType:62,ZERO_OR_ONE:63,ZERO_OR_MORE:64,ONE_OR_MORE:65,ONLY_ONE:66,MD_PARENT:67,NON_IDENTIFYING:68,IDENTIFYING:69,WORD:70,$accept:0,$end:1},terminals_:{2:"error",4:"ER_DIAGRAM",6:"EOF",8:"SPACE",10:"NEWLINE",13:"COLON",15:"STYLE_SEPARATOR",17:"BLOCK_START",19:"BLOCK_STOP",20:"SQS",21:"SQE",22:"title",23:"title_value",24:"acc_title",25:"acc_title_value",26:"acc_descr",27:"acc_descr_value",28:"acc_descr_multiline_value",33:"direction_tb",34:"direction_bt",35:"direction_rl",36:"direction_lr",37:"CLASSDEF",40:"UNICODE_TEXT",41:"STYLE_TEXT",42:"COMMA",43:"CLASS",44:"STYLE",47:"SEMI",48:"NUM",49:"BRKT",50:"ENTITY_NAME",56:"ATTRIBUTE_WORD",58:",",59:"ATTRIBUTE_KEY",60:"COMMENT",63:"ZERO_OR_ONE",64:"ZERO_OR_MORE",65:"ONE_OR_MORE",66:"ONLY_ONE",67:"MD_PARENT",68:"NON_IDENTIFYING",69:"IDENTIFYING",70:"WORD"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,5],[9,9],[9,7],[9,7],[9,4],[9,6],[9,3],[9,5],[9,1],[9,3],[9,7],[9,9],[9,6],[9,8],[9,4],[9,6],[9,2],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[9,1],[29,1],[29,1],[29,1],[29,1],[30,4],[16,1],[16,1],[16,3],[16,3],[31,3],[32,4],[38,1],[38,3],[45,1],[45,2],[39,1],[39,1],[39,1],[46,1],[46,1],[46,1],[46,1],[11,1],[11,1],[18,1],[18,2],[51,2],[51,3],[51,3],[51,4],[52,1],[53,1],[54,1],[54,3],[57,1],[55,1],[12,3],[61,1],[61,1],[61,1],[61,1],[61,1],[62,1],[62,1],[14,1],[14,1],[14,1]],performAction:h(function(n,a,c,r,p,t,K){var e=t.length-1;switch(p){case 1:break;case 2:this.$=[];break;case 3:t[e-1].push(t[e]),this.$=t[e-1];break;case 4:case 5:this.$=t[e];break;case 6:case 7:this.$=[];break;case 8:r.addEntity(t[e-4]),r.addEntity(t[e-2]),r.addRelationship(t[e-4],t[e],t[e-2],t[e-3]);break;case 9:r.addEntity(t[e-8]),r.addEntity(t[e-4]),r.addRelationship(t[e-8],t[e],t[e-4],t[e-5]),r.setClass([t[e-8]],t[e-6]),r.setClass([t[e-4]],t[e-2]);break;case 10:r.addEntity(t[e-6]),r.addEntity(t[e-2]),r.addRelationship(t[e-6],t[e],t[e-2],t[e-3]),r.setClass([t[e-6]],t[e-4]);break;case 11:r.addEntity(t[e-6]),r.addEntity(t[e-4]),r.addRelationship(t[e-6],t[e],t[e-4],t[e-5]),r.setClass([t[e-4]],t[e-2]);break;case 12:r.addEntity(t[e-3]),r.addAttributes(t[e-3],t[e-1]);break;case 13:r.addEntity(t[e-5]),r.addAttributes(t[e-5],t[e-1]),r.setClass([t[e-5]],t[e-3]);break;case 14:r.addEntity(t[e-2]);break;case 15:r.addEntity(t[e-4]),r.setClass([t[e-4]],t[e-2]);break;case 16:r.addEntity(t[e]);break;case 17:r.addEntity(t[e-2]),r.setClass([t[e-2]],t[e]);break;case 18:r.addEntity(t[e-6],t[e-4]),r.addAttributes(t[e-6],t[e-1]);break;case 19:r.addEntity(t[e-8],t[e-6]),r.addAttributes(t[e-8],t[e-1]),r.setClass([t[e-8]],t[e-3]);break;case 20:r.addEntity(t[e-5],t[e-3]);break;case 21:r.addEntity(t[e-7],t[e-5]),r.setClass([t[e-7]],t[e-2]);break;case 22:r.addEntity(t[e-3],t[e-1]);break;case 23:r.addEntity(t[e-5],t[e-3]),r.setClass([t[e-5]],t[e]);break;case 24:case 25:this.$=t[e].trim(),r.setAccTitle(this.$);break;case 26:case 27:this.$=t[e].trim(),r.setAccDescription(this.$);break;case 32:r.setDirection("TB");break;case 33:r.setDirection("BT");break;case 34:r.setDirection("RL");break;case 35:r.setDirection("LR");break;case 36:this.$=t[e-3],r.addClass(t[e-2],t[e-1]);break;case 37:case 38:case 56:case 64:this.$=[t[e]];break;case 39:case 40:this.$=t[e-2].concat([t[e]]);break;case 41:this.$=t[e-2],r.setClass(t[e-1],t[e]);break;case 42:this.$=t[e-3],r.addCssStyles(t[e-2],t[e-1]);break;case 43:this.$=[t[e]];break;case 44:t[e-2].push(t[e]),this.$=t[e-2];break;case 46:this.$=t[e-1]+t[e];break;case 54:case 76:case 77:this.$=t[e].replace(/"/g,"");break;case 55:case 78:this.$=t[e];break;case 57:t[e].push(t[e-1]),this.$=t[e];break;case 58:this.$={type:t[e-1],name:t[e]};break;case 59:this.$={type:t[e-2],name:t[e-1],keys:t[e]};break;case 60:this.$={type:t[e-2],name:t[e-1],comment:t[e]};break;case 61:this.$={type:t[e-3],name:t[e-2],keys:t[e-1],comment:t[e]};break;case 62:case 63:case 66:this.$=t[e];break;case 65:t[e-2].push(t[e]),this.$=t[e-2];break;case 67:this.$=t[e].replace(/"/g,"");break;case 68:this.$={cardA:t[e],relType:t[e-1],cardB:t[e-2]};break;case 69:this.$=r.Cardinality.ZERO_OR_ONE;break;case 70:this.$=r.Cardinality.ZERO_OR_MORE;break;case 71:this.$=r.Cardinality.ONE_OR_MORE;break;case 72:this.$=r.Cardinality.ONLY_ONE;break;case 73:this.$=r.Cardinality.MD_PARENT;break;case 74:this.$=r.Identification.NON_IDENTIFYING;break;case 75:this.$=r.Identification.IDENTIFYING;break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},i(s,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:9,22:o,24:d,26:l,28:u,29:14,30:15,31:16,32:17,33:f,34:y,35:_,36:L,37:j,40:S,43:tt,44:Z,50:T},i(s,[2,7],{1:[2,1]}),i(s,[2,3]),{9:27,11:9,22:o,24:d,26:l,28:u,29:14,30:15,31:16,32:17,33:f,34:y,35:_,36:L,37:j,40:S,43:tt,44:Z,50:T},i(s,[2,5]),i(s,[2,6]),i(s,[2,16],{12:28,61:32,15:[1,29],17:[1,30],20:[1,31],63:V,64:et,65:it,66:st,67:rt}),{23:[1,38]},{25:[1,39]},{27:[1,40]},i(s,[2,27]),i(s,[2,28]),i(s,[2,29]),i(s,[2,30]),i(s,[2,31]),i(ft,[2,54]),i(ft,[2,55]),i(s,[2,32]),i(s,[2,33]),i(s,[2,34]),i(s,[2,35]),{16:41,40:O,41:N},{16:44,40:O,41:N},{16:45,40:O,41:N},i(s,[2,4]),{11:46,40:S,50:T},{16:47,40:O,41:N},{18:48,19:[1,49],51:50,52:51,56:M},{11:53,40:S,50:T},{62:54,68:[1,55],69:[1,56]},i(F,[2,69]),i(F,[2,70]),i(F,[2,71]),i(F,[2,72]),i(F,[2,73]),i(s,[2,24]),i(s,[2,25]),i(s,[2,26]),{13:B,38:57,41:Y,42:A,45:59,46:60,48:P,49:G},i(U,[2,37]),i(U,[2,38]),{16:65,40:O,41:N,42:A},{13:B,38:66,41:Y,42:A,45:59,46:60,48:P,49:G},{13:[1,67],15:[1,68]},i(s,[2,17],{61:32,12:69,17:[1,70],42:A,63:V,64:et,65:it,66:st,67:rt}),{19:[1,71]},i(s,[2,14]),{18:72,19:[2,56],51:50,52:51,56:M},{53:73,56:[1,74]},{56:[2,62]},{21:[1,75]},{61:76,63:V,64:et,65:it,66:st,67:rt},i(yt,[2,74]),i(yt,[2,75]),{6:gt,10:kt,39:77,42:mt,47:_t},{40:[1,82],41:[1,83]},i(Et,[2,43],{46:84,13:B,41:Y,48:P,49:G}),i(v,[2,45]),i(v,[2,50]),i(v,[2,51]),i(v,[2,52]),i(v,[2,53]),i(s,[2,41],{42:A}),{6:gt,10:kt,39:85,42:mt,47:_t},{14:86,40:W,50:Q,70:X},{16:90,40:O,41:N},{11:91,40:S,50:T},{18:92,19:[1,93],51:50,52:51,56:M},i(s,[2,12]),{19:[2,57]},i(z,[2,58],{54:94,55:95,57:96,59:St,60:Tt}),i([19,56,59,60],[2,63]),i(s,[2,22],{15:[1,100],17:[1,99]}),i([40,50],[2,68]),i(s,[2,36]),{13:B,41:Y,45:101,46:60,48:P,49:G},i(s,[2,47]),i(s,[2,48]),i(s,[2,49]),i(U,[2,39]),i(U,[2,40]),i(v,[2,46]),i(s,[2,42]),i(s,[2,8]),i(s,[2,76]),i(s,[2,77]),i(s,[2,78]),{13:[1,102],42:A},{13:[1,104],15:[1,103]},{19:[1,105]},i(s,[2,15]),i(z,[2,59],{55:106,58:[1,107],60:Tt}),i(z,[2,60]),i(nt,[2,64]),i(z,[2,67]),i(nt,[2,66]),{18:108,19:[1,109],51:50,52:51,56:M},{16:110,40:O,41:N},i(Et,[2,44],{46:84,13:B,41:Y,48:P,49:G}),{14:111,40:W,50:Q,70:X},{16:112,40:O,41:N},{14:113,40:W,50:Q,70:X},i(s,[2,13]),i(z,[2,61]),{57:114,59:St},{19:[1,115]},i(s,[2,20]),i(s,[2,23],{17:[1,116],42:A}),i(s,[2,11]),{13:[1,117],42:A},i(s,[2,10]),i(nt,[2,65]),i(s,[2,18]),{18:118,19:[1,119],51:50,52:51,56:M},{14:120,40:W,50:Q,70:X},{19:[1,121]},i(s,[2,21]),i(s,[2,9]),i(s,[2,19])],defaultActions:{52:[2,62],72:[2,57]},parseError:h(function(n,a){if(a.recoverable)this.trace(n);else{var c=new Error(n);throw c.hash=a,c}},"parseError"),parse:h(function(n){var a=this,c=[0],r=[],p=[null],t=[],K=this.table,e="",q=0,Ot=0,Nt=0,Wt=2,At=1,Qt=t.slice.call(arguments,1),b=Object.create(this.lexer),x={yy:{}};for(var ot in this.yy)Object.prototype.hasOwnProperty.call(this.yy,ot)&&(x.yy[ot]=this.yy[ot]);b.setInput(n,x.yy),x.yy.lexer=b,x.yy.parser=this,typeof b.yylloc>"u"&&(b.yylloc={});var lt=b.yylloc;t.push(lt);var Xt=b.options&&b.options.ranges;typeof x.yy.parseError=="function"?this.parseError=x.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function ee(k){c.length=c.length-2*k,p.length=p.length-k,t.length=t.length-k}h(ee,"popStack");function qt(){var k;return k=r.pop()||b.lex()||At,typeof k!="number"&&(k instanceof Array&&(r=k,k=r.pop()),k=a.symbols_[k]||k),k}h(qt,"lex");for(var g,ht,I,m,ie,ut,D={},H,E,Rt,J;;){if(I=c[c.length-1],this.defaultActions[I]?m=this.defaultActions[I]:((g===null||typeof g>"u")&&(g=qt()),m=K[I]&&K[I][g]),typeof m>"u"||!m.length||!m[0]){var dt="";J=[];for(H in K[I])this.terminals_[H]&&H>Wt&&J.push("'"+this.terminals_[H]+"'");b.showPosition?dt="Parse error on line "+(q+1)+`:
`+b.showPosition()+`
Expecting `+J.join(", ")+", got '"+(this.terminals_[g]||g)+"'":dt="Parse error on line "+(q+1)+": Unexpected "+(g==At?"end of input":"'"+(this.terminals_[g]||g)+"'"),this.parseError(dt,{text:b.match,token:this.terminals_[g]||g,line:b.yylineno,loc:lt,expected:J})}if(m[0]instanceof Array&&m.length>1)throw new Error("Parse Error: multiple actions possible at state: "+I+", token: "+g);switch(m[0]){case 1:c.push(g),p.push(b.yytext),t.push(b.yylloc),c.push(m[1]),g=null,ht?(g=ht,ht=null):(Ot=b.yyleng,e=b.yytext,q=b.yylineno,lt=b.yylloc,Nt>0&&Nt--);break;case 2:if(E=this.productions_[m[1]][1],D.$=p[p.length-E],D._$={first_line:t[t.length-(E||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(E||1)].first_column,last_column:t[t.length-1].last_column},Xt&&(D._$.range=[t[t.length-(E||1)].range[0],t[t.length-1].range[1]]),ut=this.performAction.apply(D,[e,Ot,q,x.yy,m[1],p,t].concat(Qt)),typeof ut<"u")return ut;E&&(c=c.slice(0,-1*E*2),p=p.slice(0,-1*E),t=t.slice(0,-1*E)),c.push(this.productions_[m[1]][0]),p.push(D.$),t.push(D._$),Rt=K[c[c.length-2]][c[c.length-1]],c.push(Rt);break;case 3:return!0}}return!0},"parse")},Ut=function(){var R={EOF:1,parseError:h(function(a,c){if(this.yy.parser)this.yy.parser.parseError(a,c);else throw new Error(a)},"parseError"),setInput:h(function(n,a){return this.yy=a||this.yy||{},this._input=n,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:h(function(){var n=this._input[0];this.yytext+=n,this.yyleng++,this.offset++,this.match+=n,this.matched+=n;var a=n.match(/(?:\r\n?|\n).*/g);return a?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),n},"input"),unput:h(function(n){var a=n.length,c=n.split(/(?:\r\n?|\n)/g);this._input=n+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-a),this.offset-=a;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var p=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===r.length?this.yylloc.first_column:0)+r[r.length-c.length].length-c[0].length:this.yylloc.first_column-a},this.options.ranges&&(this.yylloc.range=[p[0],p[0]+this.yyleng-a]),this.yyleng=this.yytext.length,this},"unput"),more:h(function(){return this._more=!0,this},"more"),reject:h(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:h(function(n){this.unput(this.match.slice(n))},"less"),pastInput:h(function(){var n=this.matched.substr(0,this.matched.length-this.match.length);return(n.length>20?"...":"")+n.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:h(function(){var n=this.match;return n.length<20&&(n+=this._input.substr(0,20-n.length)),(n.substr(0,20)+(n.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:h(function(){var n=this.pastInput(),a=new Array(n.length+1).join("-");return n+this.upcomingInput()+`
`+a+"^"},"showPosition"),test_match:h(function(n,a){var c,r,p;if(this.options.backtrack_lexer&&(p={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(p.yylloc.range=this.yylloc.range.slice(0))),r=n[0].match(/(?:\r\n?|\n).*/g),r&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+n[0].length},this.yytext+=n[0],this.match+=n[0],this.matches=n,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(n[0].length),this.matched+=n[0],c=this.performAction.call(this,this.yy,this,a,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var t in p)this[t]=p[t];return!1}return!1},"test_match"),next:h(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var n,a,c,r;this._more||(this.yytext="",this.match="");for(var p=this._currentRules(),t=0;t<p.length;t++)if(c=this._input.match(this.rules[p[t]]),c&&(!a||c[0].length>a[0].length)){if(a=c,r=t,this.options.backtrack_lexer){if(n=this.test_match(c,p[t]),n!==!1)return n;if(this._backtrack){a=!1;continue}else return!1}else if(!this.options.flex)break}return a?(n=this.test_match(a,p[r]),n!==!1?n:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:h(function(){var a=this.next();return a||this.lex()},"lex"),begin:h(function(a){this.conditionStack.push(a)},"begin"),popState:h(function(){var a=this.conditionStack.length-1;return a>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:h(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:h(function(a){return a=this.conditionStack.length-1-Math.abs(a||0),a>=0?this.conditionStack[a]:"INITIAL"},"topState"),pushState:h(function(a){this.begin(a)},"pushState"),stateStackSize:h(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:h(function(a,c,r,p){var t=p;switch(r){case 0:return this.begin("acc_title"),24;break;case 1:return this.popState(),"acc_title_value";break;case 2:return this.begin("acc_descr"),26;break;case 3:return this.popState(),"acc_descr_value";break;case 4:this.begin("acc_descr_multiline");break;case 5:this.popState();break;case 6:return"acc_descr_multiline_value";case 7:return 33;case 8:return 34;case 9:return 35;case 10:return 36;case 11:return 10;case 12:break;case 13:return 8;case 14:return 50;case 15:return 70;case 16:return 4;case 17:return this.begin("block"),17;break;case 18:return 49;case 19:return 49;case 20:return 42;case 21:return 15;case 22:return 13;case 23:break;case 24:return 59;case 25:return 56;case 26:return 56;case 27:return 60;case 28:break;case 29:return this.popState(),19;break;case 30:return c.yytext[0];case 31:return 20;case 32:return 21;case 33:return this.begin("style"),44;break;case 34:return this.popState(),10;break;case 35:break;case 36:return 13;case 37:return 42;case 38:return 49;case 39:return this.begin("style"),37;break;case 40:return 43;case 41:return 63;case 42:return 65;case 43:return 65;case 44:return 65;case 45:return 63;case 46:return 63;case 47:return 64;case 48:return 64;case 49:return 64;case 50:return 64;case 51:return 64;case 52:return 65;case 53:return 64;case 54:return 65;case 55:return 66;case 56:return 66;case 57:return 66;case 58:return 66;case 59:return 63;case 60:return 64;case 61:return 65;case 62:return 67;case 63:return 68;case 64:return 69;case 65:return 69;case 66:return 68;case 67:return 68;case 68:return 68;case 69:return 41;case 70:return 47;case 71:return 40;case 72:return 48;case 73:return c.yytext[0];case 74:return 6}},"anonymous"),rules:[/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:[\s]+)/i,/^(?:"[^"%\r\n\v\b\\]+")/i,/^(?:"[^"]*")/i,/^(?:erDiagram\b)/i,/^(?:\{)/i,/^(?:#)/i,/^(?:#)/i,/^(?:,)/i,/^(?::::)/i,/^(?::)/i,/^(?:\s+)/i,/^(?:\b((?:PK)|(?:FK)|(?:UK))\b)/i,/^(?:([^\s]*)[~].*[~]([^\s]*))/i,/^(?:([\*A-Za-z_\u00C0-\uFFFF][A-Za-z0-9\-\_\[\]\(\)\u00C0-\uFFFF\*]*))/i,/^(?:"[^"]*")/i,/^(?:[\n]+)/i,/^(?:\})/i,/^(?:.)/i,/^(?:\[)/i,/^(?:\])/i,/^(?:style\b)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?::)/i,/^(?:,)/i,/^(?:#)/i,/^(?:classDef\b)/i,/^(?:class\b)/i,/^(?:one or zero\b)/i,/^(?:one or more\b)/i,/^(?:one or many\b)/i,/^(?:1\+)/i,/^(?:\|o\b)/i,/^(?:zero or one\b)/i,/^(?:zero or more\b)/i,/^(?:zero or many\b)/i,/^(?:0\+)/i,/^(?:\}o\b)/i,/^(?:many\(0\))/i,/^(?:many\(1\))/i,/^(?:many\b)/i,/^(?:\}\|)/i,/^(?:one\b)/i,/^(?:only one\b)/i,/^(?:1\b)/i,/^(?:\|\|)/i,/^(?:o\|)/i,/^(?:o\{)/i,/^(?:\|\{)/i,/^(?:\s*u\b)/i,/^(?:\.\.)/i,/^(?:--)/i,/^(?:to\b)/i,/^(?:optionally to\b)/i,/^(?:\.-)/i,/^(?:-\.)/i,/^(?:([^\x00-\x7F]|\w|-|\*)+)/i,/^(?:;)/i,/^(?:([^\x00-\x7F]|\w|-|\*)+)/i,/^(?:[0-9])/i,/^(?:.)/i,/^(?:$)/i],conditions:{style:{rules:[34,35,36,37,38,69,70],inclusive:!1},acc_descr_multiline:{rules:[5,6],inclusive:!1},acc_descr:{rules:[3],inclusive:!1},acc_title:{rules:[1],inclusive:!1},block:{rules:[23,24,25,26,27,28,29,30],inclusive:!1},INITIAL:{rules:[0,2,4,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,31,32,33,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,71,72,73,74],inclusive:!0}}};return R}();at.lexer=Ut;function ct(){this.yy={}}return h(ct,"Parser"),ct.prototype=at,at.Parser=ct,new ct}();pt.parser=pt;var jt=pt;var $=class{constructor(){this.entities=new Map;this.relationships=[];this.classes=new Map;this.direction="TB";this.Cardinality={ZERO_OR_ONE:"ZERO_OR_ONE",ZERO_OR_MORE:"ZERO_OR_MORE",ONE_OR_MORE:"ONE_OR_MORE",ONLY_ONE:"ONLY_ONE",MD_PARENT:"MD_PARENT"};this.Identification={NON_IDENTIFYING:"NON_IDENTIFYING",IDENTIFYING:"IDENTIFYING"};this.setAccTitle=vt;this.getAccTitle=Dt;this.setAccDescription=wt;this.getAccDescription=Lt;this.setDiagramTitle=Vt;this.getDiagramTitle=Mt;this.getConfig=h(()=>w().er,"getConfig");this.clear(),this.addEntity=this.addEntity.bind(this),this.addAttributes=this.addAttributes.bind(this),this.addRelationship=this.addRelationship.bind(this),this.setDirection=this.setDirection.bind(this),this.addCssStyles=this.addCssStyles.bind(this),this.addClass=this.addClass.bind(this),this.setClass=this.setClass.bind(this),this.setAccTitle=this.setAccTitle.bind(this),this.setAccDescription=this.setAccDescription.bind(this)}static{h(this,"ErDB")}addEntity(s,o=""){return this.entities.has(s)?!this.entities.get(s)?.alias&&o&&(this.entities.get(s).alias=o,C.info(`Add alias '${o}' to entity '${s}'`)):(this.entities.set(s,{id:`entity-${s}-${this.entities.size}`,label:s,attributes:[],alias:o,shape:"erBox",look:w().look??"default",cssClasses:"default",cssStyles:[]}),C.info("Added new entity :",s)),this.entities.get(s)}getEntity(s){return this.entities.get(s)}getEntities(){return this.entities}getClasses(){return this.classes}addAttributes(s,o){let d=this.addEntity(s),l;for(l=o.length-1;l>=0;l--)o[l].keys||(o[l].keys=[]),o[l].comment||(o[l].comment=""),d.attributes.push(o[l]),C.debug("Added attribute ",o[l].name)}addRelationship(s,o,d,l){let u=this.entities.get(s),f=this.entities.get(d);if(!u||!f)return;let y={entityA:u.id,roleA:o,entityB:f.id,relSpec:l};this.relationships.push(y),C.debug("Added new relationship :",y)}getRelationships(){return this.relationships}getDirection(){return this.direction}setDirection(s){this.direction=s}getCompiledStyles(s){let o=[];for(let d of s){let l=this.classes.get(d);l?.styles&&(o=[...o,...l.styles??[]].map(u=>u.trim())),l?.textStyles&&(o=[...o,...l.textStyles??[]].map(u=>u.trim()))}return o}addCssStyles(s,o){for(let d of s){let l=this.entities.get(d);if(!o||!l)return;for(let u of o)l.cssStyles.push(u)}}addClass(s,o){s.forEach(d=>{let l=this.classes.get(d);l===void 0&&(l={id:d,styles:[],textStyles:[]},this.classes.set(d,l)),o&&o.forEach(function(u){if(/color/.exec(u)){let f=u.replace("fill","bgFill");l.textStyles.push(f)}l.styles.push(u)})})}setClass(s,o){for(let d of s){let l=this.entities.get(d);if(l)for(let u of o)l.cssClasses+=" "+u}}clear(){this.entities=new Map,this.classes=new Map,this.relationships=[],It()}getData(){let s=[],o=[],d=w();for(let u of this.entities.keys()){let f=this.entities.get(u);f&&(f.cssCompiledStyles=this.getCompiledStyles(f.cssClasses.split(" ")),s.push(f))}let l=0;for(let u of this.relationships){let f={id:Yt(u.entityA,u.entityB,{prefix:"id",counter:l++}),type:"normal",curve:"basis",start:u.entityA,end:u.entityB,label:u.roleA,labelpos:"c",thickness:"normal",classes:"relationshipLine",arrowTypeStart:u.relSpec.cardB.toLowerCase(),arrowTypeEnd:u.relSpec.cardA.toLowerCase(),pattern:u.relSpec.relType=="IDENTIFYING"?"solid":"dashed",look:d.look};o.push(f)}return{nodes:s,edges:o,other:{},config:d,direction:"TB"}}};var bt={};Ht(bt,{draw:()=>Jt});var Jt=h(async function(i,s,o,d){C.info("REF0:"),C.info("Drawing er diagram (unified)",s);let{securityLevel:l,er:u,layout:f}=w(),y=d.db.getData(),_=Pt(s,l);y.type=d.type,y.layoutAlgorithm=zt(f),y.config.flowchart.nodeSpacing=u?.nodeSpacing||140,y.config.flowchart.rankSpacing=u?.rankSpacing||80,y.direction=d.db.getDirection(),y.markers=["only_one","zero_or_one","one_or_more","zero_or_more"],y.diagramId=s,await Gt(y,_),y.layoutAlgorithm==="elk"&&_.select(".edges").lower();let L=_.selectAll('[id*="-background"]');Array.from(L).length>0&&L.each(function(){let S=Ft(this),Z=S.attr("id").replace("-background",""),T=_.select(`#${CSS.escape(Z)}`);if(!T.empty()){let V=T.attr("transform");S.attr("transform",V)}});let j=8;Bt.insertTitle(_,"erDiagramTitleText",u?.titleTopMargin??25,d.db.getDiagramTitle()),Kt(_,j,"erDiagram",u?.useMaxWidth??!0)},"draw");var $t=h((i,s)=>{let o=xt,d=o(i,"r"),l=o(i,"g"),u=o(i,"b");return Ct(d,l,u,s)},"fade"),te=h(i=>`
  .entityBox {
    fill: ${i.mainBkg};
    stroke: ${i.nodeBorder};
  }

  .relationshipLabelBox {
    fill: ${i.tertiaryColor};
    opacity: 0.7;
    background-color: ${i.tertiaryColor};
      rect {
        opacity: 0.5;
      }
  }

  .labelBkg {
    background-color: ${$t(i.tertiaryColor,.5)};
  }

  .edgeLabel .label {
    fill: ${i.nodeBorder};
    font-size: 14px;
  }

  .label {
    font-family: ${i.fontFamily};
    color: ${i.nodeTextColor||i.textColor};
  }

  .edge-pattern-dashed {
    stroke-dasharray: 8,8;
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon
  {
    fill: ${i.mainBkg};
    stroke: ${i.nodeBorder};
    stroke-width: 1px;
  }

  .relationshipLine {
    stroke: ${i.lineColor};
    stroke-width: 1;
    fill: none;
  }

  .marker {
    fill: none !important;
    stroke: ${i.lineColor} !important;
    stroke-width: 1;
  }
`,"getStyles"),Zt=te;var Ne={parser:jt,get db(){return new $},renderer:bt,styles:Zt};export{Ne as diagram};
