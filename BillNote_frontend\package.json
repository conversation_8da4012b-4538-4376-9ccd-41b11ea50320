{"name": "bili_note", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@lobehub/icons": "^1.97.1", "@lobehub/icons-static-svg": "^1.45.0", "@lottiefiles/dotlottie-react": "^0.13.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/vite": "^4.1.3", "@tauri-apps/plugin-shell": "~2.2.2", "@uiw/react-markdown-preview": "^5.1.3", "antd": "^5.24.8", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fuse.js": "^7.1.0", "github-markdown-css": "^5.8.1", "katex": "^0.16.22", "lottie-react": "^2.4.1", "lucide-react": "^0.487.0", "markdown-navbar": "^1.4.3", "markmap-common": "^0.18.9", "markmap-lib": "^0.18.11", "markmap-toolbar": "^0.18.10", "markmap-view": "^0.18.10", "next-themes": "^0.4.6", "pinyin-match": "^1.2.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "react-markdown": "^8.0.7", "react-medium-image-zoom": "^5.2.14", "react-resizable-panels": "^2.1.8", "react-router-dom": "^7.5.1", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^6.0.2", "remark-gfm": "3.0.1", "remark-math": "^5.1.1", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.1.3", "@tauri-apps/cli": "^2.5.0", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}