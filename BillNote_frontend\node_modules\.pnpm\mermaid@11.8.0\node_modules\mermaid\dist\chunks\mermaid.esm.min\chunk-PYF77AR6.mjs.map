{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-SI5TIVW2.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  MermaidGeneratedSharedModule,\n  PieGeneratedModule,\n  __name\n} from \"./chunk-ORCS5NZH.mjs\";\n\n// src/language/pie/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/pie/tokenBuilder.ts\nvar PieTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"PieTokenBuilder\");\n  }\n  constructor() {\n    super([\"pie\", \"showData\"]);\n  }\n};\n\n// src/language/pie/valueConverter.ts\nvar PieValueConverter = class extends AbstractMermaidValueConverter {\n  static {\n    __name(this, \"PieValueConverter\");\n  }\n  runCustomConverter(rule, input, _cstNode) {\n    if (rule.name !== \"PIE_SECTION_LABEL\") {\n      return void 0;\n    }\n    return input.replace(/\"/g, \"\").trim();\n  }\n};\n\n// src/language/pie/module.ts\nvar PieModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new PieTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new PieValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createPieServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Pie = inject(\n    createDefaultCoreModule({ shared }),\n    PieGeneratedModule,\n    PieModule\n  );\n  shared.ServiceRegistry.register(Pie);\n  return { shared, Pie };\n}\n__name(createPieServices, \"createPieServices\");\n\nexport {\n  PieModule,\n  createPieServices\n};\n"], "mappings": "iJAiBA,IAAIA,EAAkB,cAAcC,CAA4B,CAjBhE,MAiBgE,CAAAC,EAAA,wBAC9D,MAAO,CACLA,EAAO,KAAM,iBAAiB,CAChC,CACA,aAAc,CACZ,MAAM,CAAC,MAAO,UAAU,CAAC,CAC3B,CACF,EAGIC,EAAoB,cAAcC,CAA8B,CA3BpE,MA2BoE,CAAAF,EAAA,0BAClE,MAAO,CACLA,EAAO,KAAM,mBAAmB,CAClC,CACA,mBAAmBG,EAAMC,EAAOC,EAAU,CACxC,GAAIF,EAAK,OAAS,oBAGlB,OAAOC,EAAM,QAAQ,KAAM,EAAE,EAAE,KAAK,CACtC,CACF,EAGIE,EAAY,CACd,OAAQ,CACN,aAA8BN,EAAO,IAAM,IAAIF,EAAmB,cAAc,EAChF,eAAgCE,EAAO,IAAM,IAAIC,EAAqB,gBAAgB,CACxF,CACF,EACA,SAASM,EAAkBC,EAAUC,EAAiB,CACpD,IAAMC,EAASC,EACbC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAAMH,EACVI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAV,CACF,EACA,OAAAI,EAAO,gBAAgB,SAASI,CAAG,EAC5B,CAAE,OAAAJ,EAAQ,IAAAI,CAAI,CACvB,CAZSd,EAAAO,EAAA,qBAaTP,EAAOO,EAAmB,mBAAmB", "names": ["PieTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "PieValueConverter", "AbstractMermaidValueConverter", "rule", "input", "_cstNode", "PieModule", "createPieServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Pie", "createDefaultCoreModule", "PieGeneratedModule"]}