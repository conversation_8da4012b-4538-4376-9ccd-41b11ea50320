{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-YDWR4PMV.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  CommonValueConverter,\n  GitGraphGeneratedModule,\n  MermaidGeneratedSharedModule,\n  __name\n} from \"./chunk-ORCS5NZH.mjs\";\n\n// src/language/gitGraph/module.ts\nimport {\n  inject,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  EmptyFileSystem\n} from \"langium\";\n\n// src/language/gitGraph/tokenBuilder.ts\nvar GitGraphTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"GitGraphTokenBuilder\");\n  }\n  constructor() {\n    super([\"gitGraph\"]);\n  }\n};\n\n// src/language/gitGraph/module.ts\nvar GitGraphModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new GitGraphTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createGitGraphServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const GitGraph = inject(\n    createDefaultCoreModule({ shared }),\n    GitGraphGeneratedModule,\n    GitGraphModule\n  );\n  shared.ServiceRegistry.register(GitGraph);\n  return { shared, GitGraph };\n}\n__name(createGitGraphServices, \"createGitGraphServices\");\n\nexport {\n  GitGraphModule,\n  createGitGraphServices\n};\n"], "mappings": "4IAiBA,IAAIA,EAAuB,cAAcC,CAA4B,CAjBrE,MAiBqE,CAAAC,EAAA,6BACnE,MAAO,CACLA,EAAO,KAAM,sBAAsB,CACrC,CACA,aAAc,CACZ,MAAM,CAAC,UAAU,CAAC,CACpB,CACF,EAGIC,EAAiB,CACnB,OAAQ,CACN,aAA8BD,EAAO,IAAM,IAAIF,EAAwB,cAAc,EACrF,eAAgCE,EAAO,IAAM,IAAIE,EAAwB,gBAAgB,CAC3F,CACF,EACA,SAASC,EAAuBC,EAAUC,EAAiB,CACzD,IAAMC,EAASC,EACbC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAAWH,EACfI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAX,CACF,EACA,OAAAK,EAAO,gBAAgB,SAASI,CAAQ,EACjC,CAAE,OAAAJ,EAAQ,SAAAI,CAAS,CAC5B,CAZSV,EAAAG,EAAA,0BAaTH,EAAOG,EAAwB,wBAAwB", "names": ["GitGraphTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "GitGraphModule", "CommonValueConverter", "createGitGraphServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "GitGraph", "createDefaultCoreModule", "GitGraphGeneratedModule"]}