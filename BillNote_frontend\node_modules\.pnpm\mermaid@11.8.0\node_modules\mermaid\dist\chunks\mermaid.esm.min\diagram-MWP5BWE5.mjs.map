{"version": 3, "sources": ["../../../src/diagrams/treemap/db.ts", "../../../src/diagrams/treemap/utils.ts", "../../../src/diagrams/treemap/parser.ts", "../../../src/diagrams/treemap/renderer.ts", "../../../src/diagrams/treemap/styles.ts", "../../../src/diagrams/treemap/diagram.ts"], "sourcesContent": ["import { getConfig as commonGetConfig } from '../../config.js';\nimport DEFAULT_CONFIG from '../../defaultConfig.js';\nimport type { DiagramStyleClassDef } from '../../diagram-api/types.js';\nimport { isLabelStyle } from '../../rendering-util/rendering-elements/shapes/handDrawnShapeStyles.js';\n\nimport { cleanAndMerge } from '../../utils.js';\nimport { ImperativeState } from '../../utils/imperativeState.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport type { TreemapDB, TreemapData, TreemapDiagramConfig, TreemapNode } from './types.js';\n\nconst defaultTreemapData: TreemapData = {\n  nodes: [],\n  levels: new Map(),\n  outerNodes: [],\n  classes: new Map(),\n};\n\nconst state = new ImperativeState<TreemapData>(() => structuredClone(defaultTreemapData));\n\nconst getConfig = (): Required<TreemapDiagramConfig> => {\n  // Use type assertion with unknown as intermediate step\n  const defaultConfig = DEFAULT_CONFIG as unknown as { treemap: Required<TreemapDiagramConfig> };\n  const userConfig = commonGetConfig() as unknown as { treemap?: Partial<TreemapDiagramConfig> };\n\n  return cleanAndMerge({\n    ...defaultConfig.treemap,\n    ...(userConfig.treemap ?? {}),\n  }) as Required<TreemapDiagramConfig>;\n};\n\nconst getNodes = (): TreemapNode[] => state.records.nodes;\n\nconst addNode = (node: TreemapNode, level: number) => {\n  const data = state.records;\n  data.nodes.push(node);\n  data.levels.set(node, level);\n\n  if (level === 0) {\n    data.outerNodes.push(node);\n  }\n\n  // Set the root node if this is a level 0 node and we don't have a root yet\n  if (level === 0 && !data.root) {\n    data.root = node;\n  }\n};\n\nconst getRoot = (): TreemapNode | undefined => ({ name: '', children: state.records.outerNodes });\n\nconst addClass = (id: string, _style: string) => {\n  const classes = state.records.classes;\n  const styleClass = classes.get(id) ?? { id, styles: [], textStyles: [] };\n  classes.set(id, styleClass);\n\n  const styles = _style.replace(/\\\\,/g, '§§§').replace(/,/g, ';').replace(/§§§/g, ',').split(';');\n\n  if (styles) {\n    styles.forEach((s) => {\n      if (isLabelStyle(s)) {\n        if (styleClass?.textStyles) {\n          styleClass.textStyles.push(s);\n        } else {\n          styleClass.textStyles = [s];\n        }\n      }\n      if (styleClass?.styles) {\n        styleClass.styles.push(s);\n      } else {\n        styleClass.styles = [s];\n      }\n    });\n  }\n\n  classes.set(id, styleClass);\n};\nconst getClasses = (): Map<string, DiagramStyleClassDef> => {\n  return state.records.classes;\n};\n\nconst getStylesForClass = (classSelector: string): string[] => {\n  return state.records.classes.get(classSelector)?.styles ?? [];\n};\n\nconst clear = () => {\n  commonClear();\n  state.reset();\n};\n\nexport const db: TreemapDB = {\n  getNodes,\n  addNode,\n  getRoot,\n  getConfig,\n  clear,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  addClass,\n  getClasses,\n  getStylesForClass,\n};\n", "import type { TreemapNode } from './types.js';\n\n/**\n * Converts a flat array of treemap items into a hierarchical structure\n * @param items - Array of flat treemap items with level, name, type, and optional value\n * @returns A hierarchical tree structure\n */\nexport function buildHierarchy(\n  items: {\n    level: number;\n    name: string;\n    type: string;\n    value?: number;\n    classSelector?: string;\n    cssCompiledStyles?: string;\n  }[]\n): TreemapNode[] {\n  if (!items.length) {\n    return [];\n  }\n\n  const root: TreemapNode[] = [];\n  const stack: { node: TreemapNode; level: number }[] = [];\n\n  items.forEach((item) => {\n    const node: TreemapNode = {\n      name: item.name,\n      children: item.type === 'Leaf' ? undefined : [],\n    };\n    node.classSelector = item?.classSelector;\n    if (item?.cssCompiledStyles) {\n      node.cssCompiledStyles = [item.cssCompiledStyles];\n    }\n\n    if (item.type === 'Leaf' && item.value !== undefined) {\n      node.value = item.value;\n    }\n\n    // Find the right parent for this node\n    while (stack.length > 0 && stack[stack.length - 1].level >= item.level) {\n      stack.pop();\n    }\n\n    if (stack.length === 0) {\n      // This is a root node\n      root.push(node);\n    } else {\n      // Add as child to the parent\n      const parent = stack[stack.length - 1].node;\n      if (parent.children) {\n        parent.children.push(node);\n      } else {\n        parent.children = [node];\n      }\n    }\n\n    // Only add to stack if it can have children\n    if (item.type !== 'Leaf') {\n      stack.push({ node, level: item.level });\n    }\n  });\n\n  return root;\n}\n", "import { parse } from '@mermaid-js/parser';\nimport type { ParserDefinition } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { populateCommonDb } from '../common/populateCommonDb.js';\nimport { db } from './db.js';\nimport type { TreemapNode, TreemapAst } from './types.js';\nimport { buildHierarchy } from './utils.js';\n\n/**\n * Populates the database with data from the Treemap AST\n * @param ast - The Treemap AST\n */\nconst populate = (ast: TreemapAst) => {\n  // We need to bypass the type checking for populateCommonDb\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  populateCommonDb(ast as any, db);\n\n  const items: {\n    level: number;\n    name: string;\n    type: string;\n    value?: number;\n    classSelector?: string;\n    cssCompiledStyles?: string;\n  }[] = [];\n\n  // Extract classes and styles from the treemap\n  for (const row of ast.TreemapRows ?? []) {\n    if (row.$type === 'ClassDefStatement') {\n      db.addClass(row.className ?? '', row.styleText ?? '');\n    }\n  }\n\n  // Extract data from each row in the treemap\n  for (const row of ast.TreemapRows ?? []) {\n    const item = row.item;\n\n    if (!item) {\n      continue;\n    }\n\n    const level = row.indent ? parseInt(row.indent) : 0;\n    const name = getItemName(item);\n\n    // Get styles as a string if they exist\n    const styles = item.classSelector ? db.getStylesForClass(item.classSelector) : [];\n    const cssCompiledStyles = styles.length > 0 ? styles.join(';') : undefined;\n\n    const itemData = {\n      level,\n      name,\n      type: item.$type,\n      value: item.value,\n      classSelector: item.classSelector,\n      cssCompiledStyles,\n    };\n\n    items.push(itemData);\n  }\n\n  // Convert flat structure to hierarchical\n  const hierarchyNodes = buildHierarchy(items);\n\n  // Add all nodes to the database\n  const addNodesRecursively = (nodes: TreemapNode[], level: number) => {\n    for (const node of nodes) {\n      db.addNode(node, level);\n      if (node.children && node.children.length > 0) {\n        addNodesRecursively(node.children, level + 1);\n      }\n    }\n  };\n\n  addNodesRecursively(hierarchyNodes, 0);\n};\n\n/**\n * Gets the name of a treemap item\n * @param item - The treemap item\n * @returns The name of the item\n */\nconst getItemName = (item: { name?: string | number }): string => {\n  return item.name ? String(item.name) : '';\n};\n\nexport const parser: ParserDefinition = {\n  parse: async (text: string): Promise<void> => {\n    try {\n      // Use a generic parse that accepts any diagram type\n\n      const parseFunc = parse as (diagramType: string, text: string) => Promise<TreemapAst>;\n      const ast = await parseFunc('treemap', text);\n      log.debug('Treemap AST:', ast);\n      populate(ast);\n    } catch (error) {\n      log.error('Error parsing treemap:', error);\n      throw error;\n    }\n  },\n};\n", "import type { Diagram } from '../../Diagram.js';\nimport type {\n  Diagram<PERSON><PERSON>er,\n  DiagramStyleClassDef,\n  DrawDefinition,\n} from '../../diagram-api/types.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { setupViewPortForSVG } from '../../rendering-util/setupViewPortForSVG.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type { TreemapDB, TreemapNode } from './types.js';\nimport { scaleOrdinal, treemap, hierarchy, format, select } from 'd3';\nimport { styles2String } from '../../rendering-util/rendering-elements/shapes/handDrawnShapeStyles.js';\nimport { getConfig } from '../../config.js';\nimport { log } from '../../logger.js';\nimport type { Node } from '../../rendering-util/types.js';\n\nconst DEFAULT_INNER_PADDING = 10; // Default for inner padding between cells/sections\nconst SECTION_INNER_PADDING = 10; // Default for inner padding between cells/sections\nconst SECTION_HEADER_HEIGHT = 25;\n\n/**\n * Draws the treemap diagram\n */\nconst draw: DrawDefinition = (_text, id, _version, diagram: Diagram) => {\n  const treemapDb = diagram.db as TreemapDB;\n  const config = treemapDb.getConfig();\n  const treemapInnerPadding = config.padding ?? DEFAULT_INNER_PADDING;\n  const title = treemapDb.getDiagramTitle();\n  const root = treemapDb.getRoot();\n  const { themeVariables } = getConfig();\n  if (!root) {\n    return;\n  }\n\n  // Define dimensions\n  const titleHeight = title ? 30 : 0;\n\n  const svg = selectSvgElement(id);\n  // Use config dimensions or defaults\n  const width = config.nodeWidth ? config.nodeWidth * SECTION_INNER_PADDING : 960;\n  const height = config.nodeHeight ? config.nodeHeight * SECTION_INNER_PADDING : 500;\n\n  const svgWidth = width;\n  const svgHeight = height + titleHeight;\n\n  // Set the SVG size\n  svg.attr('viewBox', `0 0 ${svgWidth} ${svgHeight}`);\n  configureSvgSize(svg, svgHeight, svgWidth, config.useMaxWidth);\n\n  // Format for displaying values\n  let valueFormat;\n  try {\n    // Handle special format patterns\n    const formatStr = config.valueFormat || ',';\n\n    // Handle special cases that aren't directly supported by D3 format\n    if (formatStr === '$0,0') {\n      // Currency with thousands separator\n      valueFormat = (value: number) => '$' + format(',')(value);\n    } else if (formatStr.startsWith('$') && formatStr.includes(',')) {\n      // Other dollar formats with commas\n      const precision = /\\.\\d+/.exec(formatStr);\n      const precisionStr = precision ? precision[0] : '';\n      valueFormat = (value: number) => '$' + format(',' + precisionStr)(value);\n    } else if (formatStr.startsWith('$')) {\n      // Simple dollar sign prefix\n      const restOfFormat = formatStr.substring(1);\n      valueFormat = (value: number) => '$' + format(restOfFormat || '')(value);\n    } else {\n      // Standard D3 format\n      valueFormat = format(formatStr);\n    }\n  } catch (error) {\n    log.error('Error creating format function:', error);\n    // Fallback to default format\n    valueFormat = format(',');\n  }\n\n  // Create color scale\n  const colorScale = scaleOrdinal<string>().range([\n    'transparent',\n    themeVariables.cScale0,\n    themeVariables.cScale1,\n    themeVariables.cScale2,\n    themeVariables.cScale3,\n    themeVariables.cScale4,\n    themeVariables.cScale5,\n    themeVariables.cScale6,\n    themeVariables.cScale7,\n    themeVariables.cScale8,\n    themeVariables.cScale9,\n    themeVariables.cScale10,\n    themeVariables.cScale11,\n  ]);\n  const colorScalePeer = scaleOrdinal<string>().range([\n    'transparent',\n    themeVariables.cScalePeer0,\n    themeVariables.cScalePeer1,\n    themeVariables.cScalePeer2,\n    themeVariables.cScalePeer3,\n    themeVariables.cScalePeer4,\n    themeVariables.cScalePeer5,\n    themeVariables.cScalePeer6,\n    themeVariables.cScalePeer7,\n    themeVariables.cScalePeer8,\n    themeVariables.cScalePeer9,\n    themeVariables.cScalePeer10,\n    themeVariables.cScalePeer11,\n  ]);\n  const colorScaleLabel = scaleOrdinal<string>().range([\n    themeVariables.cScaleLabel0,\n    themeVariables.cScaleLabel1,\n    themeVariables.cScaleLabel2,\n    themeVariables.cScaleLabel3,\n    themeVariables.cScaleLabel4,\n    themeVariables.cScaleLabel5,\n    themeVariables.cScaleLabel6,\n    themeVariables.cScaleLabel7,\n    themeVariables.cScaleLabel8,\n    themeVariables.cScaleLabel9,\n    themeVariables.cScaleLabel10,\n    themeVariables.cScaleLabel11,\n  ]);\n\n  // Draw the title if it exists\n  if (title) {\n    svg\n      .append('text')\n      .attr('x', svgWidth / 2)\n      .attr('y', titleHeight / 2)\n      .attr('class', 'treemapTitle')\n      .attr('text-anchor', 'middle')\n      .attr('dominant-baseline', 'middle')\n      .text(title);\n  }\n\n  // Create a main container for the treemap, translated below the title\n  const g = svg\n    .append('g')\n    .attr('transform', `translate(0, ${titleHeight})`)\n    .attr('class', 'treemapContainer');\n\n  // Create the hierarchical structure\n  const hierarchyRoot = hierarchy<TreemapNode>(root)\n    .sum((d) => d.value ?? 0)\n    .sort((a, b) => (b.value ?? 0) - (a.value ?? 0));\n\n  // Create treemap layout\n  const treemapLayout = treemap<TreemapNode>()\n    .size([width, height])\n    .paddingTop((d) =>\n      d.children && d.children.length > 0 ? SECTION_HEADER_HEIGHT + SECTION_INNER_PADDING : 0\n    )\n    .paddingInner(treemapInnerPadding)\n    .paddingLeft((d) => (d.children && d.children.length > 0 ? SECTION_INNER_PADDING : 0))\n    .paddingRight((d) => (d.children && d.children.length > 0 ? SECTION_INNER_PADDING : 0))\n    .paddingBottom((d) => (d.children && d.children.length > 0 ? SECTION_INNER_PADDING : 0))\n    .round(true);\n\n  // Apply the treemap layout to the hierarchy\n  const treemapData = treemapLayout(hierarchyRoot);\n\n  // Draw section nodes (branches - nodes with children)\n  const branchNodes = treemapData.descendants().filter((d) => d.children && d.children.length > 0);\n  const sections = g\n    .selectAll('.treemapSection')\n    .data(branchNodes)\n    .enter()\n    .append('g')\n    .attr('class', 'treemapSection')\n    .attr('transform', (d) => `translate(${d.x0},${d.y0})`);\n\n  // Add section header background\n  sections\n    .append('rect')\n    .attr('width', (d) => d.x1 - d.x0)\n    .attr('height', SECTION_HEADER_HEIGHT)\n    .attr('class', 'treemapSectionHeader')\n    .attr('fill', 'none')\n    .attr('fill-opacity', 0.6)\n    .attr('stroke-width', 0.6)\n    .attr('style', (d) => {\n      // Hide the label for the root section\n      if (d.depth === 0) {\n        return 'display: none;';\n      }\n      return '';\n    });\n\n  // Add clip paths for section headers to prevent text overflow\n  sections\n    .append('clipPath')\n    .attr('id', (_d, i) => `clip-section-${id}-${i}`)\n    .append('rect')\n    .attr('width', (d) => Math.max(0, d.x1 - d.x0 - 12)) // 6px padding on each side\n    .attr('height', SECTION_HEADER_HEIGHT);\n\n  sections\n    .append('rect')\n    .attr('width', (d) => d.x1 - d.x0)\n    .attr('height', (d) => d.y1 - d.y0)\n    .attr('class', (_d, i) => {\n      return `treemapSection section${i}`;\n    })\n    .attr('fill', (d) => colorScale(d.data.name))\n    .attr('fill-opacity', 0.6)\n    .attr('stroke', (d) => colorScalePeer(d.data.name))\n    .attr('stroke-width', 2.0)\n    .attr('stroke-opacity', 0.4)\n    .attr('style', (d) => {\n      // Hide the label for the root section\n      if (d.depth === 0) {\n        return 'display: none;';\n      }\n      const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n      return styles.nodeStyles + ';' + styles.borderStyles.join(';');\n    });\n  // Add section labels\n  sections\n    .append('text')\n    .attr('class', 'treemapSectionLabel')\n    .attr('x', 6) // Keep original left padding\n    .attr('y', SECTION_HEADER_HEIGHT / 2)\n    .attr('dominant-baseline', 'middle')\n    .text((d) => (d.depth === 0 ? '' : d.data.name)) // Skip label for root section\n    .attr('font-weight', 'bold')\n    .attr('style', (d) => {\n      // Hide the label for the root section\n      if (d.depth === 0) {\n        return 'display: none;';\n      }\n      const labelStyles =\n        'dominant-baseline: middle; font-size: 12px; fill:' +\n        colorScaleLabel(d.data.name) +\n        '; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;';\n      const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n      return labelStyles + styles.labelStyles.replace('color:', 'fill:');\n    })\n    .each(function (d) {\n      // Skip processing for root section\n      if (d.depth === 0) {\n        return;\n      }\n      const self = select(this);\n      const originalText = d.data.name;\n      self.text(originalText);\n      const totalHeaderWidth = d.x1 - d.x0;\n      const labelXPosition = 6;\n      let spaceForTextContent;\n      if (config.showValues !== false && d.value) {\n        const valueEndsAtXRelative = totalHeaderWidth - 10;\n        const estimatedValueTextActualWidth = 30;\n        const gapBetweenLabelAndValue = 10;\n        const labelMustEndBeforeX =\n          valueEndsAtXRelative - estimatedValueTextActualWidth - gapBetweenLabelAndValue;\n        spaceForTextContent = labelMustEndBeforeX - labelXPosition;\n      } else {\n        const labelOwnRightPadding = 6;\n        spaceForTextContent = totalHeaderWidth - labelXPosition - labelOwnRightPadding;\n      }\n      const minimumWidthToDisplay = 15;\n      const actualAvailableWidth = Math.max(minimumWidthToDisplay, spaceForTextContent);\n      const textNode = self.node()!;\n      const currentTextContentLength = textNode.getComputedTextLength();\n      if (currentTextContentLength > actualAvailableWidth) {\n        const ellipsis = '...';\n        let currentTruncatedText = originalText;\n        while (currentTruncatedText.length > 0) {\n          currentTruncatedText = originalText.substring(0, currentTruncatedText.length - 1);\n          if (currentTruncatedText.length === 0) {\n            self.text(ellipsis);\n            if (textNode.getComputedTextLength() > actualAvailableWidth) {\n              self.text('');\n            }\n            break;\n          }\n          self.text(currentTruncatedText + ellipsis);\n          if (textNode.getComputedTextLength() <= actualAvailableWidth) {\n            break;\n          }\n        }\n      }\n    });\n\n  // Add section values if enabled\n  if (config.showValues !== false) {\n    sections\n      .append('text')\n      .attr('class', 'treemapSectionValue')\n      .attr('x', (d) => d.x1 - d.x0 - 10)\n      .attr('y', SECTION_HEADER_HEIGHT / 2)\n      .attr('text-anchor', 'end')\n      .attr('dominant-baseline', 'middle')\n      .text((d) => (d.value ? valueFormat(d.value) : ''))\n      .attr('font-style', 'italic')\n      .attr('style', (d) => {\n        // Hide the value for the root section\n        if (d.depth === 0) {\n          return 'display: none;';\n        }\n        const labelStyles =\n          'text-anchor: end; dominant-baseline: middle; font-size: 10px; fill:' +\n          colorScaleLabel(d.data.name) +\n          '; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;';\n        const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n        return labelStyles + styles.labelStyles.replace('color:', 'fill:');\n      });\n  }\n\n  // Draw the leaf nodes\n  const leafNodes = treemapData.leaves();\n  const cell = g\n    .selectAll('.treemapLeafGroup')\n    .data(leafNodes)\n    .enter()\n    .append('g')\n    .attr('class', (d, i) => {\n      return `treemapNode treemapLeafGroup leaf${i}${d.data.classSelector ? ` ${d.data.classSelector}` : ''}x`;\n    })\n    .attr('transform', (d) => `translate(${d.x0},${d.y0})`);\n\n  // Add rectangle for each leaf node\n  cell\n    .append('rect')\n    .attr('width', (d) => d.x1 - d.x0)\n    .attr('height', (d) => d.y1 - d.y0)\n    .attr('class', 'treemapLeaf')\n    .attr('fill', (d) => {\n      // Leaves inherit color from their immediate parent section's name.\n      // If a leaf is the root itself (no parent), it uses its own name.\n      return d.parent ? colorScale(d.parent.data.name) : colorScale(d.data.name);\n    })\n    .attr('style', (d) => {\n      const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n      return styles.nodeStyles;\n    })\n    .attr('fill-opacity', 0.3)\n    .attr('stroke', (d) => {\n      // Leaves inherit color from their immediate parent section's name.\n      // If a leaf is the root itself (no parent), it uses its own name.\n      return d.parent ? colorScale(d.parent.data.name) : colorScale(d.data.name);\n    })\n    .attr('stroke-width', 3.0);\n\n  // Add clip paths to prevent text from extending outside nodes\n  cell\n    .append('clipPath')\n    .attr('id', (_d, i) => `clip-${id}-${i}`)\n    .append('rect')\n    .attr('width', (d) => Math.max(0, d.x1 - d.x0 - 4))\n    .attr('height', (d) => Math.max(0, d.y1 - d.y0 - 4));\n\n  // Add node labels with clipping\n  const leafLabels = cell\n    .append('text')\n    .attr('class', 'treemapLabel')\n    .attr('x', (d) => (d.x1 - d.x0) / 2)\n    .attr('y', (d) => (d.y1 - d.y0) / 2)\n    // .style('fill', (d) => colorScaleLabel(d.data.name))\n    .attr('style', (d) => {\n      const labelStyles =\n        'text-anchor: middle; dominant-baseline: middle; font-size: 38px;fill:' +\n        colorScaleLabel(d.data.name) +\n        ';';\n      const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n      return labelStyles + styles.labelStyles.replace('color:', 'fill:');\n    })\n    .attr('clip-path', (_d, i) => `url(#clip-${id}-${i})`)\n    .text((d) => d.data.name);\n\n  leafLabels.each(function (d) {\n    const self = select(this);\n    const nodeWidth = d.x1 - d.x0;\n    const nodeHeight = d.y1 - d.y0;\n    const textNode = self.node()!;\n\n    const padding = 4;\n    const availableWidth = nodeWidth - 2 * padding;\n    const availableHeight = nodeHeight - 2 * padding;\n\n    if (availableWidth < 10 || availableHeight < 10) {\n      self.style('display', 'none');\n      return;\n    }\n\n    let currentLabelFontSize = parseInt(self.style('font-size'), 10);\n    const minLabelFontSize = 8;\n    const originalValueRelFontSize = 28; // Original font size of value, for max cap\n    const valueScaleFactor = 0.6; // Value font size as a factor of label font size\n    const minValueFontSize = 6;\n    const spacingBetweenLabelAndValue = 2;\n\n    // 1. Adjust label font size to fit width\n    while (\n      textNode.getComputedTextLength() > availableWidth &&\n      currentLabelFontSize > minLabelFontSize\n    ) {\n      currentLabelFontSize--;\n      self.style('font-size', `${currentLabelFontSize}px`);\n    }\n\n    // 2. Adjust both label and prospective value font size to fit combined height\n    let prospectiveValueFontSize = Math.max(\n      minValueFontSize,\n      Math.min(originalValueRelFontSize, Math.round(currentLabelFontSize * valueScaleFactor))\n    );\n    let combinedHeight =\n      currentLabelFontSize + spacingBetweenLabelAndValue + prospectiveValueFontSize;\n\n    while (combinedHeight > availableHeight && currentLabelFontSize > minLabelFontSize) {\n      currentLabelFontSize--;\n      prospectiveValueFontSize = Math.max(\n        minValueFontSize,\n        Math.min(originalValueRelFontSize, Math.round(currentLabelFontSize * valueScaleFactor))\n      );\n      if (\n        prospectiveValueFontSize < minValueFontSize &&\n        currentLabelFontSize === minLabelFontSize\n      ) {\n        break;\n      } // Avoid shrinking label if value is already at min\n      self.style('font-size', `${currentLabelFontSize}px`);\n      combinedHeight =\n        currentLabelFontSize + spacingBetweenLabelAndValue + prospectiveValueFontSize;\n      if (prospectiveValueFontSize <= minValueFontSize && combinedHeight > availableHeight) {\n        // If value is at min and still doesn't fit, label might need to shrink more alone\n        // This might lead to label being too small for its own text, checked next\n      }\n    }\n\n    // Update label font size based on height adjustment\n    self.style('font-size', `${currentLabelFontSize}px`);\n\n    // 3. Final visibility check for the label\n    if (\n      textNode.getComputedTextLength() > availableWidth ||\n      currentLabelFontSize < minLabelFontSize ||\n      availableHeight < currentLabelFontSize\n    ) {\n      self.style('display', 'none');\n      // If label is hidden, value will be hidden by its own .each() loop\n    }\n  });\n\n  // Add node values with clipping\n  if (config.showValues !== false) {\n    const leafValues = cell\n      .append('text')\n      .attr('class', 'treemapValue')\n      .attr('x', (d) => (d.x1 - d.x0) / 2)\n      .attr('y', function (d) {\n        // Y position calculated dynamically in leafValues.each based on final label metrics\n        return (d.y1 - d.y0) / 2; // Placeholder, will be overwritten\n      })\n      .attr('style', (d) => {\n        const labelStyles =\n          'text-anchor: middle; dominant-baseline: hanging; font-size: 28px;fill:' +\n          colorScaleLabel(d.data.name) +\n          ';';\n        const styles = styles2String({ cssCompiledStyles: d.data.cssCompiledStyles } as Node);\n        return labelStyles + styles.labelStyles.replace('color:', 'fill:');\n      })\n\n      .attr('clip-path', (_d, i) => `url(#clip-${id}-${i})`)\n      .text((d) => (d.value ? valueFormat(d.value) : ''));\n\n    leafValues.each(function (d) {\n      const valueTextElement = select(this);\n      const parentCellNode = this.parentNode as SVGGElement | null;\n\n      if (!parentCellNode) {\n        valueTextElement.style('display', 'none');\n        return;\n      }\n\n      const labelElement = select(parentCellNode).select<SVGTextElement>('.treemapLabel');\n\n      if (labelElement.empty() || labelElement.style('display') === 'none') {\n        valueTextElement.style('display', 'none');\n        return;\n      }\n\n      const finalLabelFontSize = parseFloat(labelElement.style('font-size'));\n      const originalValueFontSize = 28; // From initial style setting\n      const valueScaleFactor = 0.6;\n      const minValueFontSize = 6;\n      const spacingBetweenLabelAndValue = 2;\n\n      const actualValueFontSize = Math.max(\n        minValueFontSize,\n        Math.min(originalValueFontSize, Math.round(finalLabelFontSize * valueScaleFactor))\n      );\n      valueTextElement.style('font-size', `${actualValueFontSize}px`);\n\n      const labelCenterY = (d.y1 - d.y0) / 2;\n      const valueTopActualY = labelCenterY + finalLabelFontSize / 2 + spacingBetweenLabelAndValue;\n      valueTextElement.attr('y', valueTopActualY);\n\n      const nodeWidth = d.x1 - d.x0;\n      const nodeTotalHeight = d.y1 - d.y0;\n      const cellBottomPadding = 4;\n      const maxValueBottomY = nodeTotalHeight - cellBottomPadding;\n      const availableWidthForValue = nodeWidth - 2 * 4; // padding for value text\n\n      if (\n        valueTextElement.node()!.getComputedTextLength() > availableWidthForValue ||\n        valueTopActualY + actualValueFontSize > maxValueBottomY ||\n        actualValueFontSize < minValueFontSize\n      ) {\n        valueTextElement.style('display', 'none');\n      } else {\n        valueTextElement.style('display', null);\n      }\n    });\n  }\n  const diagramPadding = config.diagramPadding ?? 8;\n  setupViewPortForSVG(svg, diagramPadding, 'flowchart', config?.useMaxWidth || false);\n};\n\nconst getClasses = function (\n  _text: string,\n  diagramObj: Pick<Diagram, 'db'>\n): Map<string, DiagramStyleClassDef> {\n  return (diagramObj.db as TreemapDB).getClasses();\n};\nexport const renderer: DiagramRenderer = { draw, getClasses };\n", "import type { DiagramStylesProvider } from '../../diagram-api/types.js';\nimport { cleanAndMerge } from '../../utils.js';\nimport type { TreemapStyleOptions } from './types.js';\n\nconst defaultTreemapStyleOptions: TreemapStyleOptions = {\n  sectionStrokeColor: 'black',\n  sectionStrokeWidth: '1',\n  sectionFillColor: '#efefef',\n  leafStrokeColor: 'black',\n  leafStrokeWidth: '1',\n  leafFillColor: '#efefef',\n  labelColor: 'black',\n  labelFontSize: '12px',\n  valueFontSize: '10px',\n  valueColor: 'black',\n  titleColor: 'black',\n  titleFontSize: '14px',\n};\n\nexport const getStyles: DiagramStylesProvider = ({\n  treemap,\n}: { treemap?: TreemapStyleOptions } = {}) => {\n  const options = cleanAndMerge(defaultTreemapStyleOptions, treemap);\n\n  return `\n  .treemapNode.section {\n    stroke: ${options.sectionStrokeColor};\n    stroke-width: ${options.sectionStrokeWidth};\n    fill: ${options.sectionFillColor};\n  }\n  .treemapNode.leaf {\n    stroke: ${options.leafStrokeColor};\n    stroke-width: ${options.leafStrokeWidth};\n    fill: ${options.leafFillColor};\n  }\n  .treemapLabel {\n    fill: ${options.labelColor};\n    font-size: ${options.labelFontSize};\n  }\n  .treemapValue {\n    fill: ${options.valueColor};\n    font-size: ${options.valueFontSize};\n  }\n  .treemapTitle {\n    fill: ${options.titleColor};\n    font-size: ${options.titleFontSize};\n  }\n  `;\n};\n\nexport default getStyles;\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { db } from './db.js';\nimport { parser } from './parser.js';\nimport { renderer } from './renderer.js';\nimport styles from './styles.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n};\n"], "mappings": "g1BAkBA,IAAMA,GAAkC,CACtC,MAAO,CAAC,EACR,OAAQ,IAAI,IACZ,WAAY,CAAC,EACb,QAAS,IAAI,GACf,EAEMC,EAAQ,IAAIC,GAA6B,IAAM,gBAAgBF,EAAkB,CAAC,EAElFG,GAAYC,EAAA,IAAsC,CAEtD,IAAMC,EAAgBC,EAChBC,EAAaJ,EAAgB,EAEnC,OAAOK,EAAc,CACnB,GAAGH,EAAc,QACjB,GAAIE,EAAW,SAAW,CAAC,CAC7B,CAAC,CACH,EATkB,aAWZE,GAAWL,EAAA,IAAqBH,EAAM,QAAQ,MAAnC,YAEXS,GAAUN,EAAA,CAACO,EAAmBC,IAAkB,CACpD,IAAMC,EAAOZ,EAAM,QACnBY,EAAK,MAAM,KAAKF,CAAI,EACpBE,EAAK,OAAO,IAAIF,EAAMC,CAAK,EAEvBA,IAAU,GACZC,EAAK,WAAW,KAAKF,CAAI,EAIvBC,IAAU,GAAK,CAACC,EAAK,OACvBA,EAAK,KAAOF,EAEhB,EAbgB,WAeVG,GAAUV,EAAA,KAAgC,CAAE,KAAM,GAAI,SAAUH,EAAM,QAAQ,UAAW,GAA/E,WAEVc,GAAWX,EAAA,CAACY,EAAYC,IAAmB,CAC/C,IAAMC,EAAUjB,EAAM,QAAQ,QACxBkB,EAAaD,EAAQ,IAAIF,CAAE,GAAK,CAAE,GAAAA,EAAI,OAAQ,CAAC,EAAG,WAAY,CAAC,CAAE,EACvEE,EAAQ,IAAIF,EAAIG,CAAU,EAE1B,IAAMC,EAASH,EAAO,QAAQ,OAAQ,cAAK,EAAE,QAAQ,KAAM,GAAG,EAAE,QAAQ,OAAQ,GAAG,EAAE,MAAM,GAAG,EAE1FG,GACFA,EAAO,QAASC,GAAM,CAChBC,GAAaD,CAAC,IACZF,GAAY,WACdA,EAAW,WAAW,KAAKE,CAAC,EAE5BF,EAAW,WAAa,CAACE,CAAC,GAG1BF,GAAY,OACdA,EAAW,OAAO,KAAKE,CAAC,EAExBF,EAAW,OAAS,CAACE,CAAC,CAE1B,CAAC,EAGHH,EAAQ,IAAIF,EAAIG,CAAU,CAC5B,EAzBiB,YA0BXI,GAAanB,EAAA,IACVH,EAAM,QAAQ,QADJ,cAIbuB,GAAoBpB,EAACqB,GAClBxB,EAAM,QAAQ,QAAQ,IAAIwB,CAAa,GAAG,QAAU,CAAC,EADpC,qBAIpBC,GAAQtB,EAAA,IAAM,CAClBsB,EAAY,EACZzB,EAAM,MAAM,CACd,EAHc,SAKD0B,EAAgB,CAC3B,SAAAlB,GACA,QAAAC,GACA,QAAAI,GACA,UAAAX,GACA,MAAAuB,GACA,YAAAE,GACA,YAAAC,GACA,gBAAAC,GACA,gBAAAC,GACA,kBAAAC,GACA,kBAAAC,GACA,SAAAlB,GACA,WAAAQ,GACA,kBAAAC,EACF,ECxGO,SAASU,GACdC,EAQe,CACf,GAAI,CAACA,EAAM,OACT,MAAO,CAAC,EAGV,IAAMC,EAAsB,CAAC,EACvBC,EAAgD,CAAC,EAEvD,OAAAF,EAAM,QAASG,GAAS,CACtB,IAAMC,EAAoB,CACxB,KAAMD,EAAK,KACX,SAAUA,EAAK,OAAS,OAAS,OAAY,CAAC,CAChD,EAWA,IAVAC,EAAK,cAAgBD,GAAM,cACvBA,GAAM,oBACRC,EAAK,kBAAoB,CAACD,EAAK,iBAAiB,GAG9CA,EAAK,OAAS,QAAUA,EAAK,QAAU,SACzCC,EAAK,MAAQD,EAAK,OAIbD,EAAM,OAAS,GAAKA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASC,EAAK,OAC/DD,EAAM,IAAI,EAGZ,GAAIA,EAAM,SAAW,EAEnBD,EAAK,KAAKG,CAAI,MACT,CAEL,IAAMC,EAASH,EAAMA,EAAM,OAAS,CAAC,EAAE,KACnCG,EAAO,SACTA,EAAO,SAAS,KAAKD,CAAI,EAEzBC,EAAO,SAAW,CAACD,CAAI,CAE3B,CAGID,EAAK,OAAS,QAChBD,EAAM,KAAK,CAAE,KAAAE,EAAM,MAAOD,EAAK,KAAM,CAAC,CAE1C,CAAC,EAEMF,CACT,CAxDgBK,EAAAP,GAAA,kBCKhB,IAAMQ,GAAWC,EAACC,GAAoB,CAGpCC,GAAiBD,EAAYE,CAAE,EAE/B,IAAMC,EAOA,CAAC,EAGP,QAAWC,KAAOJ,EAAI,aAAe,CAAC,EAChCI,EAAI,QAAU,qBAChBF,EAAG,SAASE,EAAI,WAAa,GAAIA,EAAI,WAAa,EAAE,EAKxD,QAAWA,KAAOJ,EAAI,aAAe,CAAC,EAAG,CACvC,IAAMK,EAAOD,EAAI,KAEjB,GAAI,CAACC,EACH,SAGF,IAAMC,EAAQF,EAAI,OAAS,SAASA,EAAI,MAAM,EAAI,EAC5CG,EAAOC,GAAYH,CAAI,EAGvBI,EAASJ,EAAK,cAAgBH,EAAG,kBAAkBG,EAAK,aAAa,EAAI,CAAC,EAC1EK,EAAoBD,EAAO,OAAS,EAAIA,EAAO,KAAK,GAAG,EAAI,OAE3DE,EAAW,CACf,MAAAL,EACA,KAAAC,EACA,KAAMF,EAAK,MACX,MAAOA,EAAK,MACZ,cAAeA,EAAK,cACpB,kBAAAK,CACF,EAEAP,EAAM,KAAKQ,CAAQ,CACrB,CAGA,IAAMC,EAAiBC,GAAeV,CAAK,EAGrCW,EAAsBf,EAAA,CAACgB,EAAsBT,IAAkB,CACnE,QAAWU,KAAQD,EACjBb,EAAG,QAAQc,EAAMV,CAAK,EAClBU,EAAK,UAAYA,EAAK,SAAS,OAAS,GAC1CF,EAAoBE,EAAK,SAAUV,EAAQ,CAAC,CAGlD,EAP4B,uBAS5BQ,EAAoBF,EAAgB,CAAC,CACvC,EA9DiB,YAqEXJ,GAAcT,EAACM,GACZA,EAAK,KAAO,OAAOA,EAAK,IAAI,EAAI,GADrB,eAIPY,GAA2B,CACtC,MAAOlB,EAAA,MAAOmB,GAAgC,CAC5C,GAAI,CAIF,IAAMlB,EAAM,MADMmB,GACU,UAAWD,CAAI,EAC3CE,EAAI,MAAM,eAAgBpB,CAAG,EAC7BF,GAASE,CAAG,CACd,OAASqB,EAAO,CACd,MAAAD,EAAI,MAAM,yBAA0BC,CAAK,EACnCA,CACR,CACF,EAZO,QAaT,ECnFA,IAAMC,GAAwB,GACxBC,EAAwB,GACxBC,EAAwB,GAKxBC,GAAuBC,EAAA,CAACC,EAAOC,EAAIC,EAAUC,IAAqB,CACtE,IAAMC,EAAYD,EAAQ,GACpBE,EAASD,EAAU,UAAU,EAC7BE,EAAsBD,EAAO,SAAWV,GACxCY,EAAQH,EAAU,gBAAgB,EAClCI,EAAOJ,EAAU,QAAQ,EACzB,CAAE,eAAAK,CAAe,EAAIC,EAAU,EACrC,GAAI,CAACF,EACH,OAIF,IAAMG,EAAcJ,EAAQ,GAAK,EAE3BK,EAAMC,GAAiBZ,CAAE,EAEzBa,EAAQT,EAAO,UAAYA,EAAO,UAAYT,EAAwB,IACtEmB,EAASV,EAAO,WAAaA,EAAO,WAAaT,EAAwB,IAEzEoB,EAAWF,EACXG,EAAYF,EAASJ,EAG3BC,EAAI,KAAK,UAAW,OAAOI,CAAQ,IAAIC,CAAS,EAAE,EAClDC,EAAiBN,EAAKK,EAAWD,EAAUX,EAAO,WAAW,EAG7D,IAAIc,EACJ,GAAI,CAEF,IAAMC,EAAYf,EAAO,aAAe,IAGxC,GAAIe,IAAc,OAEhBD,EAAcpB,EAACsB,GAAkB,IAAMC,EAAO,GAAG,EAAED,CAAK,EAA1C,uBACLD,EAAU,WAAW,GAAG,GAAKA,EAAU,SAAS,GAAG,EAAG,CAE/D,IAAMG,EAAY,QAAQ,KAAKH,CAAS,EAClCI,EAAeD,EAAYA,EAAU,CAAC,EAAI,GAChDJ,EAAcpB,EAACsB,GAAkB,IAAMC,EAAO,IAAME,CAAY,EAAEH,CAAK,EAAzD,cAChB,SAAWD,EAAU,WAAW,GAAG,EAAG,CAEpC,IAAMK,EAAeL,EAAU,UAAU,CAAC,EAC1CD,EAAcpB,EAACsB,GAAkB,IAAMC,EAAOG,GAAgB,EAAE,EAAEJ,CAAK,EAAzD,cAChB,MAEEF,EAAcG,EAAOF,CAAS,CAElC,OAASM,EAAO,CACdC,EAAI,MAAM,kCAAmCD,CAAK,EAElDP,EAAcG,EAAO,GAAG,CAC1B,CAGA,IAAMM,EAAaC,EAAqB,EAAE,MAAM,CAC9C,cACApB,EAAe,QACfA,EAAe,QACfA,EAAe,QACfA,EAAe,QACfA,EAAe,QACfA,EAAe,QACfA,EAAe,QACfA,EAAe,QACfA,EAAe,QACfA,EAAe,QACfA,EAAe,SACfA,EAAe,QACjB,CAAC,EACKqB,GAAiBD,EAAqB,EAAE,MAAM,CAClD,cACApB,EAAe,YACfA,EAAe,YACfA,EAAe,YACfA,EAAe,YACfA,EAAe,YACfA,EAAe,YACfA,EAAe,YACfA,EAAe,YACfA,EAAe,YACfA,EAAe,YACfA,EAAe,aACfA,EAAe,YACjB,CAAC,EACKsB,EAAkBF,EAAqB,EAAE,MAAM,CACnDpB,EAAe,aACfA,EAAe,aACfA,EAAe,aACfA,EAAe,aACfA,EAAe,aACfA,EAAe,aACfA,EAAe,aACfA,EAAe,aACfA,EAAe,aACfA,EAAe,aACfA,EAAe,cACfA,EAAe,aACjB,CAAC,EAGGF,GACFK,EACG,OAAO,MAAM,EACb,KAAK,IAAKI,EAAW,CAAC,EACtB,KAAK,IAAKL,EAAc,CAAC,EACzB,KAAK,QAAS,cAAc,EAC5B,KAAK,cAAe,QAAQ,EAC5B,KAAK,oBAAqB,QAAQ,EAClC,KAAKJ,CAAK,EAIf,IAAMyB,EAAIpB,EACP,OAAO,GAAG,EACV,KAAK,YAAa,gBAAgBD,CAAW,GAAG,EAChD,KAAK,QAAS,kBAAkB,EAG7BsB,GAAgBC,GAAuB1B,CAAI,EAC9C,IAAK2B,GAAMA,EAAE,OAAS,CAAC,EACvB,KAAK,CAACC,EAAGC,KAAOA,EAAE,OAAS,IAAMD,EAAE,OAAS,EAAE,EAe3CE,EAZgBC,GAAqB,EACxC,KAAK,CAACzB,EAAOC,CAAM,CAAC,EACpB,WAAYoB,GACXA,EAAE,UAAYA,EAAE,SAAS,OAAS,EAAItC,EAAwBD,EAAwB,CACxF,EACC,aAAaU,CAAmB,EAChC,YAAa6B,GAAOA,EAAE,UAAYA,EAAE,SAAS,OAAS,EAAIvC,EAAwB,CAAE,EACpF,aAAcuC,GAAOA,EAAE,UAAYA,EAAE,SAAS,OAAS,EAAIvC,EAAwB,CAAE,EACrF,cAAeuC,GAAOA,EAAE,UAAYA,EAAE,SAAS,OAAS,EAAIvC,EAAwB,CAAE,EACtF,MAAM,EAAI,EAGqBqC,EAAa,EAGzCO,GAAcF,EAAY,YAAY,EAAE,OAAQH,GAAMA,EAAE,UAAYA,EAAE,SAAS,OAAS,CAAC,EACzFM,EAAWT,EACd,UAAU,iBAAiB,EAC3B,KAAKQ,EAAW,EAChB,MAAM,EACN,OAAO,GAAG,EACV,KAAK,QAAS,gBAAgB,EAC9B,KAAK,YAAcL,GAAM,aAAaA,EAAE,EAAE,IAAIA,EAAE,EAAE,GAAG,EAGxDM,EACG,OAAO,MAAM,EACb,KAAK,QAAUN,GAAMA,EAAE,GAAKA,EAAE,EAAE,EAChC,KAAK,SAAUtC,CAAqB,EACpC,KAAK,QAAS,sBAAsB,EACpC,KAAK,OAAQ,MAAM,EACnB,KAAK,eAAgB,EAAG,EACxB,KAAK,eAAgB,EAAG,EACxB,KAAK,QAAUsC,GAEVA,EAAE,QAAU,EACP,iBAEF,EACR,EAGHM,EACG,OAAO,UAAU,EACjB,KAAK,KAAM,CAACC,EAAIC,IAAM,gBAAgB1C,CAAE,IAAI0C,CAAC,EAAE,EAC/C,OAAO,MAAM,EACb,KAAK,QAAUR,GAAM,KAAK,IAAI,EAAGA,EAAE,GAAKA,EAAE,GAAK,EAAE,CAAC,EAClD,KAAK,SAAUtC,CAAqB,EAEvC4C,EACG,OAAO,MAAM,EACb,KAAK,QAAUN,GAAMA,EAAE,GAAKA,EAAE,EAAE,EAChC,KAAK,SAAWA,GAAMA,EAAE,GAAKA,EAAE,EAAE,EACjC,KAAK,QAAS,CAACO,EAAIC,IACX,yBAAyBA,CAAC,EAClC,EACA,KAAK,OAASR,GAAMP,EAAWO,EAAE,KAAK,IAAI,CAAC,EAC3C,KAAK,eAAgB,EAAG,EACxB,KAAK,SAAWA,GAAML,GAAeK,EAAE,KAAK,IAAI,CAAC,EACjD,KAAK,eAAgB,CAAG,EACxB,KAAK,iBAAkB,EAAG,EAC1B,KAAK,QAAUA,GAAM,CAEpB,GAAIA,EAAE,QAAU,EACd,MAAO,iBAET,IAAMS,EAASC,EAAc,CAAE,kBAAmBV,EAAE,KAAK,iBAAkB,CAAS,EACpF,OAAOS,EAAO,WAAa,IAAMA,EAAO,aAAa,KAAK,GAAG,CAC/D,CAAC,EAEHH,EACG,OAAO,MAAM,EACb,KAAK,QAAS,qBAAqB,EACnC,KAAK,IAAK,CAAC,EACX,KAAK,IAAK5C,EAAwB,CAAC,EACnC,KAAK,oBAAqB,QAAQ,EAClC,KAAMsC,GAAOA,EAAE,QAAU,EAAI,GAAKA,EAAE,KAAK,IAAK,EAC9C,KAAK,cAAe,MAAM,EAC1B,KAAK,QAAUA,GAAM,CAEpB,GAAIA,EAAE,QAAU,EACd,MAAO,iBAET,IAAMW,EACJ,oDACAf,EAAgBI,EAAE,KAAK,IAAI,EAC3B,oEACIS,EAASC,EAAc,CAAE,kBAAmBV,EAAE,KAAK,iBAAkB,CAAS,EACpF,OAAOW,EAAcF,EAAO,YAAY,QAAQ,SAAU,OAAO,CACnE,CAAC,EACA,KAAK,SAAUT,EAAG,CAEjB,GAAIA,EAAE,QAAU,EACd,OAEF,IAAMY,EAAOC,EAAO,IAAI,EAClBC,EAAed,EAAE,KAAK,KAC5BY,EAAK,KAAKE,CAAY,EACtB,IAAMC,EAAmBf,EAAE,GAAKA,EAAE,GAC5BgB,EAAiB,EACnBC,EACA/C,EAAO,aAAe,IAAS8B,EAAE,MAMnCiB,EAL6BF,EAAmB,GACV,GACN,GAGYC,EAG5CC,EAAsBF,EAAmBC,EAAiB,EAG5D,IAAME,EAAuB,KAAK,IADJ,GAC+BD,CAAmB,EAC1EE,EAAWP,EAAK,KAAK,EAE3B,GADiCO,EAAS,sBAAsB,EACjCD,EAAsB,CACnD,IAAME,EAAW,MACbC,EAAuBP,EAC3B,KAAOO,EAAqB,OAAS,GAAG,CAEtC,GADAA,EAAuBP,EAAa,UAAU,EAAGO,EAAqB,OAAS,CAAC,EAC5EA,EAAqB,SAAW,EAAG,CACrCT,EAAK,KAAKQ,CAAQ,EACdD,EAAS,sBAAsB,EAAID,GACrCN,EAAK,KAAK,EAAE,EAEd,KACF,CAEA,GADAA,EAAK,KAAKS,EAAuBD,CAAQ,EACrCD,EAAS,sBAAsB,GAAKD,EACtC,KAEJ,CACF,CACF,CAAC,EAGChD,EAAO,aAAe,IACxBoC,EACG,OAAO,MAAM,EACb,KAAK,QAAS,qBAAqB,EACnC,KAAK,IAAMN,GAAMA,EAAE,GAAKA,EAAE,GAAK,EAAE,EACjC,KAAK,IAAKtC,EAAwB,CAAC,EACnC,KAAK,cAAe,KAAK,EACzB,KAAK,oBAAqB,QAAQ,EAClC,KAAMsC,GAAOA,EAAE,MAAQhB,EAAYgB,EAAE,KAAK,EAAI,EAAG,EACjD,KAAK,aAAc,QAAQ,EAC3B,KAAK,QAAUA,GAAM,CAEpB,GAAIA,EAAE,QAAU,EACd,MAAO,iBAET,IAAMW,EACJ,sEACAf,EAAgBI,EAAE,KAAK,IAAI,EAC3B,oEACIS,EAASC,EAAc,CAAE,kBAAmBV,EAAE,KAAK,iBAAkB,CAAS,EACpF,OAAOW,EAAcF,EAAO,YAAY,QAAQ,SAAU,OAAO,CACnE,CAAC,EAIL,IAAMa,GAAYnB,EAAY,OAAO,EAC/BoB,EAAO1B,EACV,UAAU,mBAAmB,EAC7B,KAAKyB,EAAS,EACd,MAAM,EACN,OAAO,GAAG,EACV,KAAK,QAAS,CAACtB,EAAGQ,IACV,oCAAoCA,CAAC,GAAGR,EAAE,KAAK,cAAgB,IAAIA,EAAE,KAAK,aAAa,GAAK,EAAE,GACtG,EACA,KAAK,YAAcA,GAAM,aAAaA,EAAE,EAAE,IAAIA,EAAE,EAAE,GAAG,EAGxDuB,EACG,OAAO,MAAM,EACb,KAAK,QAAUvB,GAAMA,EAAE,GAAKA,EAAE,EAAE,EAChC,KAAK,SAAWA,GAAMA,EAAE,GAAKA,EAAE,EAAE,EACjC,KAAK,QAAS,aAAa,EAC3B,KAAK,OAASA,GAGNA,EAAE,OAASP,EAAWO,EAAE,OAAO,KAAK,IAAI,EAAIP,EAAWO,EAAE,KAAK,IAAI,CAC1E,EACA,KAAK,QAAUA,GACCU,EAAc,CAAE,kBAAmBV,EAAE,KAAK,iBAAkB,CAAS,EACtE,UACf,EACA,KAAK,eAAgB,EAAG,EACxB,KAAK,SAAWA,GAGRA,EAAE,OAASP,EAAWO,EAAE,OAAO,KAAK,IAAI,EAAIP,EAAWO,EAAE,KAAK,IAAI,CAC1E,EACA,KAAK,eAAgB,CAAG,EAG3BuB,EACG,OAAO,UAAU,EACjB,KAAK,KAAM,CAAChB,EAAIC,IAAM,QAAQ1C,CAAE,IAAI0C,CAAC,EAAE,EACvC,OAAO,MAAM,EACb,KAAK,QAAUR,GAAM,KAAK,IAAI,EAAGA,EAAE,GAAKA,EAAE,GAAK,CAAC,CAAC,EACjD,KAAK,SAAWA,GAAM,KAAK,IAAI,EAAGA,EAAE,GAAKA,EAAE,GAAK,CAAC,CAAC,EAGlCuB,EAChB,OAAO,MAAM,EACb,KAAK,QAAS,cAAc,EAC5B,KAAK,IAAMvB,IAAOA,EAAE,GAAKA,EAAE,IAAM,CAAC,EAClC,KAAK,IAAMA,IAAOA,EAAE,GAAKA,EAAE,IAAM,CAAC,EAElC,KAAK,QAAUA,GAAM,CACpB,IAAMW,EACJ,wEACAf,EAAgBI,EAAE,KAAK,IAAI,EAC3B,IACIS,EAASC,EAAc,CAAE,kBAAmBV,EAAE,KAAK,iBAAkB,CAAS,EACpF,OAAOW,EAAcF,EAAO,YAAY,QAAQ,SAAU,OAAO,CACnE,CAAC,EACA,KAAK,YAAa,CAACF,EAAIC,IAAM,aAAa1C,CAAE,IAAI0C,CAAC,GAAG,EACpD,KAAMR,GAAMA,EAAE,KAAK,IAAI,EAEf,KAAK,SAAUA,EAAG,CAC3B,IAAMY,EAAOC,EAAO,IAAI,EAClBW,EAAYxB,EAAE,GAAKA,EAAE,GACrByB,EAAazB,EAAE,GAAKA,EAAE,GACtBmB,EAAWP,EAAK,KAAK,EAErBc,EAAU,EACVC,EAAiBH,EAAY,EAAIE,EACjCE,EAAkBH,EAAa,EAAIC,EAEzC,GAAIC,EAAiB,IAAMC,EAAkB,GAAI,CAC/ChB,EAAK,MAAM,UAAW,MAAM,EAC5B,MACF,CAEA,IAAIiB,EAAuB,SAASjB,EAAK,MAAM,WAAW,EAAG,EAAE,EACzDkB,EAAmB,EACnBC,EAA2B,GAC3BC,EAAmB,GACnBC,EAAmB,EACnBC,EAA8B,EAGpC,KACEf,EAAS,sBAAsB,EAAIQ,GACnCE,EAAuBC,GAEvBD,IACAjB,EAAK,MAAM,YAAa,GAAGiB,CAAoB,IAAI,EAIrD,IAAIM,EAA2B,KAAK,IAClCF,EACA,KAAK,IAAIF,EAA0B,KAAK,MAAMF,EAAuBG,CAAgB,CAAC,CACxF,EACII,EACFP,EAAuBK,EAA8BC,EAEvD,KAAOC,EAAiBR,GAAmBC,EAAuBC,IAChED,IACAM,EAA2B,KAAK,IAC9BF,EACA,KAAK,IAAIF,EAA0B,KAAK,MAAMF,EAAuBG,CAAgB,CAAC,CACxF,EAEE,EAAAG,EAA2BF,GAC3BJ,IAAyBC,KAI3BlB,EAAK,MAAM,YAAa,GAAGiB,CAAoB,IAAI,EACnDO,EACEP,EAAuBK,EAA8BC,EACnDA,GAA4BF,GAAoBG,EAAiBR,EAOvEhB,EAAK,MAAM,YAAa,GAAGiB,CAAoB,IAAI,GAIjDV,EAAS,sBAAsB,EAAIQ,GACnCE,EAAuBC,GACvBF,EAAkBC,IAElBjB,EAAK,MAAM,UAAW,MAAM,CAGhC,CAAC,EAGG1C,EAAO,aAAe,IACLqD,EAChB,OAAO,MAAM,EACb,KAAK,QAAS,cAAc,EAC5B,KAAK,IAAMvB,IAAOA,EAAE,GAAKA,EAAE,IAAM,CAAC,EAClC,KAAK,IAAK,SAAUA,EAAG,CAEtB,OAAQA,EAAE,GAAKA,EAAE,IAAM,CACzB,CAAC,EACA,KAAK,QAAUA,GAAM,CACpB,IAAMW,EACJ,yEACAf,EAAgBI,EAAE,KAAK,IAAI,EAC3B,IACIS,EAASC,EAAc,CAAE,kBAAmBV,EAAE,KAAK,iBAAkB,CAAS,EACpF,OAAOW,EAAcF,EAAO,YAAY,QAAQ,SAAU,OAAO,CACnE,CAAC,EAEA,KAAK,YAAa,CAACF,EAAIC,IAAM,aAAa1C,CAAE,IAAI0C,CAAC,GAAG,EACpD,KAAMR,GAAOA,EAAE,MAAQhB,EAAYgB,EAAE,KAAK,EAAI,EAAG,EAEzC,KAAK,SAAUA,EAAG,CAC3B,IAAMqC,EAAmBxB,EAAO,IAAI,EAC9ByB,EAAiB,KAAK,WAE5B,GAAI,CAACA,EAAgB,CACnBD,EAAiB,MAAM,UAAW,MAAM,EACxC,MACF,CAEA,IAAME,EAAe1B,EAAOyB,CAAc,EAAE,OAAuB,eAAe,EAElF,GAAIC,EAAa,MAAM,GAAKA,EAAa,MAAM,SAAS,IAAM,OAAQ,CACpEF,EAAiB,MAAM,UAAW,MAAM,EACxC,MACF,CAEA,IAAMG,EAAqB,WAAWD,EAAa,MAAM,WAAW,CAAC,EAC/DE,EAAwB,GACxBT,EAAmB,GACnBC,EAAmB,EACnBC,EAA8B,EAE9BQ,EAAsB,KAAK,IAC/BT,EACA,KAAK,IAAIQ,EAAuB,KAAK,MAAMD,EAAqBR,CAAgB,CAAC,CACnF,EACAK,EAAiB,MAAM,YAAa,GAAGK,CAAmB,IAAI,EAG9D,IAAMC,GADgB3C,EAAE,GAAKA,EAAE,IAAM,EACEwC,EAAqB,EAAIN,EAChEG,EAAiB,KAAK,IAAKM,CAAe,EAE1C,IAAMnB,EAAYxB,EAAE,GAAKA,EAAE,GAGrB4C,GAFkB5C,EAAE,GAAKA,EAAE,GACP,EAEpB6C,GAAyBrB,EAAY,EAAI,EAG7Ca,EAAiB,KAAK,EAAG,sBAAsB,EAAIQ,IACnDF,EAAkBD,EAAsBE,IACxCF,EAAsBT,EAEtBI,EAAiB,MAAM,UAAW,MAAM,EAExCA,EAAiB,MAAM,UAAW,IAAI,CAE1C,CAAC,EAEH,IAAMS,GAAiB5E,EAAO,gBAAkB,EAChD6E,GAAoBtE,EAAKqE,GAAgB,YAAa5E,GAAQ,aAAe,EAAK,CACpF,EA9e6B,QAgfvB8E,GAAapF,EAAA,SACjBC,EACAoF,EACmC,CACnC,OAAQA,EAAW,GAAiB,WAAW,CACjD,EALmB,cAMNC,GAA4B,CAAE,KAAAvF,GAAM,WAAAqF,EAAW,ECzgB5D,IAAMG,GAAkD,CACtD,mBAAoB,QACpB,mBAAoB,IACpB,iBAAkB,UAClB,gBAAiB,QACjB,gBAAiB,IACjB,cAAe,UACf,WAAY,QACZ,cAAe,OACf,cAAe,OACf,WAAY,QACZ,WAAY,QACZ,cAAe,MACjB,EAEaC,GAAmCC,EAAA,CAAC,CAC/C,QAAAC,CACF,EAAuC,CAAC,IAAM,CAC5C,IAAMC,EAAUC,EAAcL,GAA4BG,CAAO,EAEjE,MAAO;AAAA;AAAA,cAEKC,EAAQ,kBAAkB;AAAA,oBACpBA,EAAQ,kBAAkB;AAAA,YAClCA,EAAQ,gBAAgB;AAAA;AAAA;AAAA,cAGtBA,EAAQ,eAAe;AAAA,oBACjBA,EAAQ,eAAe;AAAA,YAC/BA,EAAQ,aAAa;AAAA;AAAA;AAAA,YAGrBA,EAAQ,UAAU;AAAA,iBACbA,EAAQ,aAAa;AAAA;AAAA;AAAA,YAG1BA,EAAQ,UAAU;AAAA,iBACbA,EAAQ,aAAa;AAAA;AAAA;AAAA,YAG1BA,EAAQ,UAAU;AAAA,iBACbA,EAAQ,aAAa;AAAA;AAAA,GAGtC,EA7BgD,aA+BzCE,GAAQL,GC5CR,IAAMM,GAA6B,CACxC,OAAAC,GACA,GAAAC,EACA,SAAAC,GACA,OAAAC,EACF", "names": ["defaultTreemapData", "state", "ImperativeState", "getConfig", "__name", "defaultConfig", "defaultConfig_default", "userConfig", "cleanAndMerge", "getNodes", "addNode", "node", "level", "data", "getRoot", "addClass", "id", "_style", "classes", "styleClass", "styles", "s", "isLabelStyle", "getClasses", "getStylesForClass", "classSelector", "clear", "db", "setAccTitle", "getAccTitle", "setDiagramTitle", "getDiagramTitle", "getAccDescription", "setAccDescription", "buildHierarchy", "items", "root", "stack", "item", "node", "parent", "__name", "populate", "__name", "ast", "populateCommonDb", "db", "items", "row", "item", "level", "name", "getItemName", "styles", "cssCompiledStyles", "itemData", "hierarchyNodes", "buildHierarchy", "addNodesRecursively", "nodes", "node", "parser", "text", "parse", "log", "error", "DEFAULT_INNER_PADDING", "SECTION_INNER_PADDING", "SECTION_HEADER_HEIGHT", "draw", "__name", "_text", "id", "_version", "diagram", "treemapDb", "config", "treemapInnerPadding", "title", "root", "themeVariables", "getConfig", "titleHeight", "svg", "selectSvgElement", "width", "height", "svgWidth", "svgHeight", "configureSvgSize", "valueFormat", "formatStr", "value", "format", "precision", "precisionStr", "restOfFormat", "error", "log", "colorScale", "ordinal", "colorScalePeer", "colorScaleLabel", "g", "hierarchyRoot", "hierarchy", "d", "a", "b", "treemapData", "treemap_default", "branchNodes", "sections", "_d", "i", "styles", "styles2String", "labelStyles", "self", "select_default", "originalText", "totalHeaderWidth", "labelXPosition", "spaceForTextContent", "actualAvailableWidth", "textNode", "ellipsis", "currentTruncatedText", "leafNodes", "cell", "nodeWidth", "nodeHeight", "padding", "availableWidth", "availableHeight", "currentLabelFontSize", "minLabelFontSize", "originalValueRelFontSize", "valueScaleFactor", "minValueFontSize", "spacingBetweenLabelAndValue", "prospectiveValueFontSize", "combinedHeight", "valueTextElement", "parentCellNode", "labelElement", "finalLabelFontSize", "originalValueFontSize", "actualValueFontSize", "valueTopActualY", "maxValueBottomY", "availableWidthForValue", "diagramPadding", "setupViewPortForSVG", "getClasses", "diagramObj", "renderer", "defaultTreemapStyleOptions", "getStyles", "__name", "treemap", "options", "cleanAndMerge", "styles_default", "diagram", "parser", "db", "renderer", "styles_default"]}