{"version": 3, "sources": ["../../../src/diagrams/pie/pieDb.ts", "../../../src/diagrams/pie/pieParser.ts", "../../../src/diagrams/pie/pieStyles.ts", "../../../src/diagrams/pie/pieRenderer.ts", "../../../src/diagrams/pie/pieDiagram.ts"], "sourcesContent": ["import { log } from '../../logger.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  clear as commonClear,\n} from '../common/commonDb.js';\nimport type { PieFields, PieDB, Sections, D3Section } from './pieTypes.js';\nimport type { RequiredDeep } from 'type-fest';\nimport type { PieDiagramConfig } from '../../config.type.js';\nimport DEFAULT_CONFIG from '../../defaultConfig.js';\n\nexport const DEFAULT_PIE_CONFIG: Required<PieDiagramConfig> = DEFAULT_CONFIG.pie;\n\nexport const DEFAULT_PIE_DB: RequiredDeep<PieFields> = {\n  sections: new Map(),\n  showData: false,\n  config: DEFAULT_PIE_CONFIG,\n} as const;\n\nlet sections: Sections = DEFAULT_PIE_DB.sections;\nlet showData: boolean = DEFAULT_PIE_DB.showData;\nconst config: Required<PieDiagramConfig> = structuredClone(DEFAULT_PIE_CONFIG);\n\nconst getConfig = (): Required<PieDiagramConfig> => structuredClone(config);\n\nconst clear = (): void => {\n  sections = new Map();\n  showData = DEFAULT_PIE_DB.showData;\n  commonClear();\n};\n\nconst addSection = ({ label, value }: D3Section): void => {\n  if (!sections.has(label)) {\n    sections.set(label, value);\n    log.debug(`added new section: ${label}, with value: ${value}`);\n  }\n};\n\nconst getSections = (): Sections => sections;\n\nconst setShowData = (toggle: boolean): void => {\n  showData = toggle;\n};\n\nconst getShowData = (): boolean => showData;\n\nexport const db: PieDB = {\n  getConfig,\n\n  clear,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n\n  addSection,\n  getSections,\n  setShowData,\n  getShowData,\n};\n", "import type { Pie } from '@mermaid-js/parser';\nimport { parse } from '@mermaid-js/parser';\nimport { log } from '../../logger.js';\nimport type { ParserDefinition } from '../../diagram-api/types.js';\nimport { populateCommonDb } from '../common/populateCommonDb.js';\nimport type { PieDB } from './pieTypes.js';\nimport { db } from './pieDb.js';\n\nconst populateDb = (ast: Pie, db: PieDB) => {\n  populateCommonDb(ast, db);\n  db.setShowData(ast.showData);\n  ast.sections.map(db.addSection);\n};\n\nexport const parser: ParserDefinition = {\n  parse: async (input: string): Promise<void> => {\n    const ast: Pie = await parse('pie', input);\n    log.debug(ast);\n    populateDb(ast, db);\n  },\n};\n", "import type { DiagramStylesProvider } from '../../diagram-api/types.js';\nimport type { PieStyleOptions } from './pieTypes.js';\n\nconst getStyles: DiagramStylesProvider = (options: PieStyleOptions) =>\n  `\n  .pieCircle{\n    stroke: ${options.pieStrokeColor};\n    stroke-width : ${options.pieStrokeWidth};\n    opacity : ${options.pieOpacity};\n  }\n  .pieOuterCircle{\n    stroke: ${options.pieOuterStrokeColor};\n    stroke-width: ${options.pieOuterStrokeWidth};\n    fill: none;\n  }\n  .pieTitleText {\n    text-anchor: middle;\n    font-size: ${options.pieTitleTextSize};\n    fill: ${options.pieTitleTextColor};\n    font-family: ${options.fontFamily};\n  }\n  .slice {\n    font-family: ${options.fontFamily};\n    fill: ${options.pieSectionTextColor};\n    font-size:${options.pieSectionTextSize};\n    // fill: white;\n  }\n  .legend text {\n    fill: ${options.pieLegendTextColor};\n    font-family: ${options.fontFamily};\n    font-size: ${options.pieLegendTextSize};\n  }\n`;\n\nexport default getStyles;\n", "import type d3 from 'd3';\nimport { arc, pie as d3pie, scaleOrdinal } from 'd3';\nimport type { MermaidConfig, PieDiagramConfig } from '../../config.type.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DrawDefinition, SVG, SVGGroup } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport { cleanAndMerge, parseFontSize } from '../../utils.js';\nimport type { D3Section, PieDB, Sections } from './pieTypes.js';\n\nconst createPieArcs = (sections: Sections): d3.PieArcDatum<D3Section>[] => {\n  // Compute the position of each group on the pie:\n  const pieData: D3Section[] = [...sections.entries()]\n    .map((element: [string, number]): D3Section => {\n      return {\n        label: element[0],\n        value: element[1],\n      };\n    })\n    .sort((a: D3Section, b: D3Section): number => {\n      return b.value - a.value;\n    });\n  const pie: d3.Pie<unknown, D3Section> = d3pie<D3Section>().value(\n    (d3Section: D3Section): number => d3Section.value\n  );\n  return pie(pieData);\n};\n\n/**\n * Draws a Pie Chart with the data given in text.\n *\n * @param text - pie chart code\n * @param id - diagram id\n * @param _version - MermaidJS version from package.json.\n * @param diagObj - A standard diagram containing the DB and the text and type etc of the diagram.\n */\nexport const draw: DrawDefinition = (text, id, _version, diagObj) => {\n  log.debug('rendering pie chart\\n' + text);\n  const db = diagObj.db as PieDB;\n  const globalConfig: MermaidConfig = getConfig();\n  const pieConfig: Required<PieDiagramConfig> = cleanAndMerge(db.getConfig(), globalConfig.pie);\n  const MARGIN = 40;\n  const LEGEND_RECT_SIZE = 18;\n  const LEGEND_SPACING = 4;\n  const height = 450;\n  const pieWidth: number = height;\n  const svg: SVG = selectSvgElement(id);\n  const group: SVGGroup = svg.append('g');\n  group.attr('transform', 'translate(' + pieWidth / 2 + ',' + height / 2 + ')');\n\n  const { themeVariables } = globalConfig;\n  let [outerStrokeWidth] = parseFontSize(themeVariables.pieOuterStrokeWidth);\n  outerStrokeWidth ??= 2;\n\n  const textPosition: number = pieConfig.textPosition;\n  const radius: number = Math.min(pieWidth, height) / 2 - MARGIN;\n  // Shape helper to build arcs:\n  const arcGenerator: d3.Arc<unknown, d3.PieArcDatum<D3Section>> = arc<d3.PieArcDatum<D3Section>>()\n    .innerRadius(0)\n    .outerRadius(radius);\n  const labelArcGenerator: d3.Arc<unknown, d3.PieArcDatum<D3Section>> = arc<\n    d3.PieArcDatum<D3Section>\n  >()\n    .innerRadius(radius * textPosition)\n    .outerRadius(radius * textPosition);\n\n  group\n    .append('circle')\n    .attr('cx', 0)\n    .attr('cy', 0)\n    .attr('r', radius + outerStrokeWidth / 2)\n    .attr('class', 'pieOuterCircle');\n\n  const sections: Sections = db.getSections();\n  const arcs: d3.PieArcDatum<D3Section>[] = createPieArcs(sections);\n\n  const myGeneratedColors = [\n    themeVariables.pie1,\n    themeVariables.pie2,\n    themeVariables.pie3,\n    themeVariables.pie4,\n    themeVariables.pie5,\n    themeVariables.pie6,\n    themeVariables.pie7,\n    themeVariables.pie8,\n    themeVariables.pie9,\n    themeVariables.pie10,\n    themeVariables.pie11,\n    themeVariables.pie12,\n  ];\n  // Set the color scale\n  const color: d3.ScaleOrdinal<string, 12, never> = scaleOrdinal(myGeneratedColors);\n\n  // Build the pie chart: each part of the pie is a path that we build using the arc function.\n  group\n    .selectAll('mySlices')\n    .data(arcs)\n    .enter()\n    .append('path')\n    .attr('d', arcGenerator)\n    .attr('fill', (datum: d3.PieArcDatum<D3Section>) => {\n      return color(datum.data.label);\n    })\n    .attr('class', 'pieCircle');\n\n  let sum = 0;\n  sections.forEach((section) => {\n    sum += section;\n  });\n  // Now add the percentage.\n  // Use the centroid method to get the best coordinates.\n  group\n    .selectAll('mySlices')\n    .data(arcs)\n    .enter()\n    .append('text')\n    .text((datum: d3.PieArcDatum<D3Section>): string => {\n      return ((datum.data.value / sum) * 100).toFixed(0) + '%';\n    })\n    .attr('transform', (datum: d3.PieArcDatum<D3Section>): string => {\n      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n      return 'translate(' + labelArcGenerator.centroid(datum) + ')';\n    })\n    .style('text-anchor', 'middle')\n    .attr('class', 'slice');\n\n  group\n    .append('text')\n    .text(db.getDiagramTitle())\n    .attr('x', 0)\n    .attr('y', -(height - 50) / 2)\n    .attr('class', 'pieTitleText');\n\n  // Add the legends/annotations for each section\n  const legend = group\n    .selectAll('.legend')\n    .data(color.domain())\n    .enter()\n    .append('g')\n    .attr('class', 'legend')\n    .attr('transform', (_datum, index: number): string => {\n      const height = LEGEND_RECT_SIZE + LEGEND_SPACING;\n      const offset = (height * color.domain().length) / 2;\n      const horizontal = 12 * LEGEND_RECT_SIZE;\n      const vertical = index * height - offset;\n      return 'translate(' + horizontal + ',' + vertical + ')';\n    });\n\n  legend\n    .append('rect')\n    .attr('width', LEGEND_RECT_SIZE)\n    .attr('height', LEGEND_RECT_SIZE)\n    .style('fill', color)\n    .style('stroke', color);\n\n  legend\n    .data(arcs)\n    .append('text')\n    .attr('x', LEGEND_RECT_SIZE + LEGEND_SPACING)\n    .attr('y', LEGEND_RECT_SIZE - LEGEND_SPACING)\n    .text((datum: d3.PieArcDatum<D3Section>): string => {\n      const { label, value } = datum.data;\n      if (db.getShowData()) {\n        return `${label} [${value}]`;\n      }\n      return label;\n    });\n\n  const longestTextWidth = Math.max(\n    ...legend\n      .selectAll('text')\n      .nodes()\n      .map((node) => (node as Element)?.getBoundingClientRect().width ?? 0)\n  );\n\n  const totalWidth = pieWidth + MARGIN + LEGEND_RECT_SIZE + LEGEND_SPACING + longestTextWidth;\n\n  // Set viewBox\n  svg.attr('viewBox', `0 0 ${totalWidth} ${height}`);\n  configureSvgSize(svg, height, totalWidth, pieConfig.useMaxWidth);\n};\n\nexport const renderer = { draw };\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { parser } from './pieParser.js';\nimport { db } from './pieDb.js';\nimport styles from './pieStyles.js';\nimport { renderer } from './pieRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n};\n"], "mappings": "urBAeO,IAAMA,EAAiDC,EAAe,IAEhEC,EAA0C,CACrD,SAAU,IAAI,IACd,SAAU,GACV,OAAQF,CACV,EAEIG,EAAqBD,EAAe,SACpCE,EAAoBF,EAAe,SACjCG,GAAqC,gBAAgBL,CAAkB,EAEvEM,GAAYC,EAAA,IAAkC,gBAAgBF,EAAM,EAAxD,aAEZG,GAAQD,EAAA,IAAY,CACxBJ,EAAW,IAAI,IACfC,EAAWF,EAAe,SAC1BM,EAAY,CACd,EAJc,SAMRC,GAAaF,EAAA,CAAC,CAAE,MAAAG,EAAO,MAAAC,CAAM,IAAuB,CACnDR,EAAS,IAAIO,CAAK,IACrBP,EAAS,IAAIO,EAAOC,CAAK,EACzBC,EAAI,MAAM,sBAAsBF,CAAK,iBAAiBC,CAAK,EAAE,EAEjE,EALmB,cAObE,GAAcN,EAAA,IAAgBJ,EAAhB,eAEdW,GAAcP,EAACQ,GAA0B,CAC7CX,EAAWW,CACb,EAFoB,eAIdC,GAAcT,EAAA,IAAeH,EAAf,eAEPa,EAAY,CACvB,UAAAX,GAEA,MAAAE,GACA,gBAAAU,EACA,gBAAAC,EACA,YAAAC,EACA,YAAAC,EACA,kBAAAC,EACA,kBAAAC,EAEA,WAAAd,GACA,YAAAI,GACA,YAAAC,GACA,YAAAE,EACF,ECzDA,IAAMQ,GAAaC,EAAA,CAACC,EAAUC,IAAc,CAC1CC,EAAiBF,EAAKC,CAAE,EACxBA,EAAG,YAAYD,EAAI,QAAQ,EAC3BA,EAAI,SAAS,IAAIC,EAAG,UAAU,CAChC,EAJmB,cAMNE,EAA2B,CACtC,MAAOJ,EAAA,MAAOK,GAAiC,CAC7C,IAAMJ,EAAW,MAAMK,EAAM,MAAOD,CAAK,EACzCE,EAAI,MAAMN,CAAG,EACbF,GAAWE,EAAKC,CAAE,CACpB,EAJO,QAKT,ECjBA,IAAMM,GAAmCC,EAACC,GACxC;AAAA;AAAA,cAEYA,EAAQ,cAAc;AAAA,qBACfA,EAAQ,cAAc;AAAA,gBAC3BA,EAAQ,UAAU;AAAA;AAAA;AAAA,cAGpBA,EAAQ,mBAAmB;AAAA,oBACrBA,EAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,iBAK9BA,EAAQ,gBAAgB;AAAA,YAC7BA,EAAQ,iBAAiB;AAAA,mBAClBA,EAAQ,UAAU;AAAA;AAAA;AAAA,mBAGlBA,EAAQ,UAAU;AAAA,YACzBA,EAAQ,mBAAmB;AAAA,gBACvBA,EAAQ,kBAAkB;AAAA;AAAA;AAAA;AAAA,YAI9BA,EAAQ,kBAAkB;AAAA,mBACnBA,EAAQ,UAAU;AAAA,iBACpBA,EAAQ,iBAAiB;AAAA;AAAA,EA3BD,aA+BlCC,EAAQH,GCvBf,IAAMI,GAAgBC,EAACC,GAAoD,CAEzE,IAAMC,EAAuB,CAAC,GAAGD,EAAS,QAAQ,CAAC,EAChD,IAAKE,IACG,CACL,MAAOA,EAAQ,CAAC,EAChB,MAAOA,EAAQ,CAAC,CAClB,EACD,EACA,KAAK,CAACC,EAAcC,IACZA,EAAE,MAAQD,EAAE,KACpB,EAIH,OAHwCE,EAAiB,EAAE,MACxDC,GAAiCA,EAAU,KAC9C,EACWL,CAAO,CACpB,EAhBsB,iBA0BTM,GAAuBR,EAAA,CAACS,EAAMC,EAAIC,GAAUC,IAAY,CACnEC,EAAI,MAAM;AAAA,EAA0BJ,CAAI,EACxC,IAAMK,EAAKF,EAAQ,GACbG,EAA8BC,EAAU,EACxCC,EAAwCC,EAAcJ,EAAG,UAAU,EAAGC,EAAa,GAAG,EACtFI,EAAS,GACTC,EAAmB,GACnBC,EAAiB,EACjBC,EAAS,IACTC,EAAmBD,EACnBE,EAAWC,EAAiBf,CAAE,EAC9BgB,EAAkBF,EAAI,OAAO,GAAG,EACtCE,EAAM,KAAK,YAAa,aAAeH,EAAW,EAAI,IAAMD,EAAS,EAAI,GAAG,EAE5E,GAAM,CAAE,eAAAK,CAAe,EAAIZ,EACvB,CAACa,CAAgB,EAAIC,EAAcF,EAAe,mBAAmB,EACzEC,IAAqB,EAErB,IAAME,EAAuBb,EAAU,aACjCc,EAAiB,KAAK,IAAIR,EAAUD,CAAM,EAAI,EAAIH,EAElDa,GAA2DC,EAA+B,EAC7F,YAAY,CAAC,EACb,YAAYF,CAAM,EACfG,GAAgED,EAEpE,EACC,YAAYF,EAASD,CAAY,EACjC,YAAYC,EAASD,CAAY,EAEpCJ,EACG,OAAO,QAAQ,EACf,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,EACZ,KAAK,IAAKK,EAASH,EAAmB,CAAC,EACvC,KAAK,QAAS,gBAAgB,EAEjC,IAAM3B,EAAqBa,EAAG,YAAY,EACpCqB,EAAoCpC,GAAcE,CAAQ,EAE1DmC,GAAoB,CACxBT,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,KACfA,EAAe,MACfA,EAAe,MACfA,EAAe,KACjB,EAEMU,EAA4CC,EAAaF,EAAiB,EAGhFV,EACG,UAAU,UAAU,EACpB,KAAKS,CAAI,EACT,MAAM,EACN,OAAO,MAAM,EACb,KAAK,IAAKH,EAAY,EACtB,KAAK,OAASO,GACNF,EAAME,EAAM,KAAK,KAAK,CAC9B,EACA,KAAK,QAAS,WAAW,EAE5B,IAAIC,EAAM,EACVvC,EAAS,QAASwC,GAAY,CAC5BD,GAAOC,CACT,CAAC,EAGDf,EACG,UAAU,UAAU,EACpB,KAAKS,CAAI,EACT,MAAM,EACN,OAAO,MAAM,EACb,KAAMI,IACIA,EAAM,KAAK,MAAQC,EAAO,KAAK,QAAQ,CAAC,EAAI,GACtD,EACA,KAAK,YAAcD,GAEX,aAAeL,GAAkB,SAASK,CAAK,EAAI,GAC3D,EACA,MAAM,cAAe,QAAQ,EAC7B,KAAK,QAAS,OAAO,EAExBb,EACG,OAAO,MAAM,EACb,KAAKZ,EAAG,gBAAgB,CAAC,EACzB,KAAK,IAAK,CAAC,EACX,KAAK,IAAK,EAAEQ,EAAS,IAAM,CAAC,EAC5B,KAAK,QAAS,cAAc,EAG/B,IAAMoB,EAAShB,EACZ,UAAU,SAAS,EACnB,KAAKW,EAAM,OAAO,CAAC,EACnB,MAAM,EACN,OAAO,GAAG,EACV,KAAK,QAAS,QAAQ,EACtB,KAAK,YAAa,CAACM,EAAQC,IAA0B,CACpD,IAAMtB,EAASF,EAAmBC,EAC5BwB,GAAUvB,EAASe,EAAM,OAAO,EAAE,OAAU,EAC5CS,GAAa,GAAK1B,EAClB2B,GAAWH,EAAQtB,EAASuB,GAClC,MAAO,aAAeC,GAAa,IAAMC,GAAW,GACtD,CAAC,EAEHL,EACG,OAAO,MAAM,EACb,KAAK,QAAStB,CAAgB,EAC9B,KAAK,SAAUA,CAAgB,EAC/B,MAAM,OAAQiB,CAAK,EACnB,MAAM,SAAUA,CAAK,EAExBK,EACG,KAAKP,CAAI,EACT,OAAO,MAAM,EACb,KAAK,IAAKf,EAAmBC,CAAc,EAC3C,KAAK,IAAKD,EAAmBC,CAAc,EAC3C,KAAMkB,GAA6C,CAClD,GAAM,CAAE,MAAAS,EAAO,MAAAC,CAAM,EAAIV,EAAM,KAC/B,OAAIzB,EAAG,YAAY,EACV,GAAGkC,CAAK,KAAKC,CAAK,IAEpBD,CACT,CAAC,EAEH,IAAME,GAAmB,KAAK,IAC5B,GAAGR,EACA,UAAU,MAAM,EAChB,MAAM,EACN,IAAKS,GAAUA,GAAkB,sBAAsB,EAAE,OAAS,CAAC,CACxE,EAEMC,EAAa7B,EAAWJ,EAASC,EAAmBC,EAAiB6B,GAG3E1B,EAAI,KAAK,UAAW,OAAO4B,CAAU,IAAI9B,CAAM,EAAE,EACjD+B,EAAiB7B,EAAKF,EAAQ8B,EAAYnC,EAAU,WAAW,CACjE,EAhJoC,QAkJvBqC,EAAW,CAAE,KAAA9C,EAAK,ECjLxB,IAAM+C,GAA6B,CACxC,OAAAC,EACA,GAAAC,EACA,SAAAC,EACA,OAAAC,CACF", "names": ["DEFAULT_PIE_CONFIG", "defaultConfig_default", "DEFAULT_PIE_DB", "sections", "showData", "config", "getConfig", "__name", "clear", "addSection", "label", "value", "log", "getSections", "setShowData", "toggle", "getShowData", "db", "setDiagramTitle", "getDiagramTitle", "setAccTitle", "getAccTitle", "setAccDescription", "getAccDescription", "populateDb", "__name", "ast", "db", "populateCommonDb", "parser", "input", "parse", "log", "getStyles", "__name", "options", "pieStyles_default", "createPieArcs", "__name", "sections", "pieData", "element", "a", "b", "pie_default", "d3Section", "draw", "text", "id", "_version", "diagObj", "log", "db", "globalConfig", "getConfig", "pieConfig", "cleanAndMerge", "MARGIN", "LEGEND_RECT_SIZE", "LEGEND_SPACING", "height", "<PERSON><PERSON><PERSON><PERSON>", "svg", "selectSvgElement", "group", "themeVariables", "outerStrokeWidth", "parseFontSize", "textPosition", "radius", "arcGenerator", "arc_default", "labelArcGenerator", "arcs", "myGeneratedColors", "color", "ordinal", "datum", "sum", "section", "legend", "_datum", "index", "offset", "horizontal", "vertical", "label", "value", "longestTextWidth", "node", "totalWidth", "configureSvgSize", "renderer", "diagram", "parser", "db", "renderer", "pieStyles_default"]}