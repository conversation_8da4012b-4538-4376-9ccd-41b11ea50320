{"version": 3, "sources": ["../../../../parser/dist/mermaid-parser.core.mjs"], "sourcesContent": ["import {\n  GitGraphModule,\n  createGitGraphServices\n} from \"./chunks/mermaid-parser.core/chunk-YDWR4PMV.mjs\";\nimport {\n  InfoModule,\n  createInfoServices\n} from \"./chunks/mermaid-parser.core/chunk-OKEFYU3C.mjs\";\nimport {\n  PacketModule,\n  createPacketServices\n} from \"./chunks/mermaid-parser.core/chunk-CY4NQLBY.mjs\";\nimport {\n  PieModule,\n  createPieServices\n} from \"./chunks/mermaid-parser.core/chunk-SI5TIVW2.mjs\";\nimport {\n  ArchitectureModule,\n  createArchitectureServices\n} from \"./chunks/mermaid-parser.core/chunk-2EOCH6SN.mjs\";\nimport {\n  RadarModule,\n  createRadarServices\n} from \"./chunks/mermaid-parser.core/chunk-K7TBWW4H.mjs\";\nimport {\n  TreemapModule,\n  createTreemapServices\n} from \"./chunks/mermaid-parser.core/chunk-QKX3RCWE.mjs\";\nimport {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  Architecture,\n  ArchitectureGeneratedModule,\n  Branch,\n  Commit,\n  CommonTokenBuilder,\n  CommonValueConverter,\n  GitGraph,\n  GitGraphGeneratedModule,\n  Info,\n  InfoGeneratedModule,\n  Merge,\n  MermaidGeneratedSharedModule,\n  Packet,\n  PacketBlock,\n  PacketGeneratedModule,\n  Pie,\n  PieGeneratedModule,\n  PieSection,\n  Radar,\n  RadarGeneratedModule,\n  Statement,\n  TreemapDoc,\n  TreemapGeneratedModule,\n  __name,\n  isArchitecture,\n  isBranch,\n  isCommit,\n  isGitGraph,\n  isInfo,\n  isMerge,\n  isPacket,\n  isPacketBlock,\n  isPie,\n  isPieSection,\n  isTreemapDoc\n} from \"./chunks/mermaid-parser.core/chunk-ORCS5NZH.mjs\";\n\n// src/parse.ts\nvar parsers = {};\nvar initializers = {\n  info: /* @__PURE__ */ __name(async () => {\n    const { createInfoServices: createInfoServices2 } = await import(\"./chunks/mermaid-parser.core/info-3VTXS3R3.mjs\");\n    const parser = createInfoServices2().Info.parser.LangiumParser;\n    parsers.info = parser;\n  }, \"info\"),\n  packet: /* @__PURE__ */ __name(async () => {\n    const { createPacketServices: createPacketServices2 } = await import(\"./chunks/mermaid-parser.core/packet-DSR6H3E6.mjs\");\n    const parser = createPacketServices2().Packet.parser.LangiumParser;\n    parsers.packet = parser;\n  }, \"packet\"),\n  pie: /* @__PURE__ */ __name(async () => {\n    const { createPieServices: createPieServices2 } = await import(\"./chunks/mermaid-parser.core/pie-GAM7RPQU.mjs\");\n    const parser = createPieServices2().Pie.parser.LangiumParser;\n    parsers.pie = parser;\n  }, \"pie\"),\n  architecture: /* @__PURE__ */ __name(async () => {\n    const { createArchitectureServices: createArchitectureServices2 } = await import(\"./chunks/mermaid-parser.core/architecture-I2MV5QL6.mjs\");\n    const parser = createArchitectureServices2().Architecture.parser.LangiumParser;\n    parsers.architecture = parser;\n  }, \"architecture\"),\n  gitGraph: /* @__PURE__ */ __name(async () => {\n    const { createGitGraphServices: createGitGraphServices2 } = await import(\"./chunks/mermaid-parser.core/gitGraph-PIIEIUND.mjs\");\n    const parser = createGitGraphServices2().GitGraph.parser.LangiumParser;\n    parsers.gitGraph = parser;\n  }, \"gitGraph\"),\n  radar: /* @__PURE__ */ __name(async () => {\n    const { createRadarServices: createRadarServices2 } = await import(\"./chunks/mermaid-parser.core/radar-NEH6LVNW.mjs\");\n    const parser = createRadarServices2().Radar.parser.LangiumParser;\n    parsers.radar = parser;\n  }, \"radar\"),\n  treemap: /* @__PURE__ */ __name(async () => {\n    const { createTreemapServices: createTreemapServices2 } = await import(\"./chunks/mermaid-parser.core/treemap-FKARHQ26.mjs\");\n    const parser = createTreemapServices2().Treemap.parser.LangiumParser;\n    parsers.treemap = parser;\n  }, \"treemap\")\n};\nasync function parse(diagramType, text) {\n  const initializer = initializers[diagramType];\n  if (!initializer) {\n    throw new Error(`Unknown diagram type: ${diagramType}`);\n  }\n  if (!parsers[diagramType]) {\n    await initializer();\n  }\n  const parser = parsers[diagramType];\n  const result = parser.parse(text);\n  if (result.lexerErrors.length > 0 || result.parserErrors.length > 0) {\n    throw new MermaidParseError(result);\n  }\n  return result.value;\n}\n__name(parse, \"parse\");\nvar MermaidParseError = class extends Error {\n  constructor(result) {\n    const lexerErrors = result.lexerErrors.map((err) => err.message).join(\"\\n\");\n    const parserErrors = result.parserErrors.map((err) => err.message).join(\"\\n\");\n    super(`Parsing failed: ${lexerErrors} ${parserErrors}`);\n    this.result = result;\n  }\n  static {\n    __name(this, \"MermaidParseError\");\n  }\n};\nexport {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  Architecture,\n  ArchitectureGeneratedModule,\n  ArchitectureModule,\n  Branch,\n  Commit,\n  CommonTokenBuilder,\n  CommonValueConverter,\n  GitGraph,\n  GitGraphGeneratedModule,\n  GitGraphModule,\n  Info,\n  InfoGeneratedModule,\n  InfoModule,\n  Merge,\n  MermaidGeneratedSharedModule,\n  MermaidParseError,\n  Packet,\n  PacketBlock,\n  PacketGeneratedModule,\n  PacketModule,\n  Pie,\n  PieGeneratedModule,\n  PieModule,\n  PieSection,\n  Radar,\n  RadarGeneratedModule,\n  RadarModule,\n  Statement,\n  TreemapDoc,\n  TreemapGeneratedModule,\n  TreemapModule,\n  createArchitectureServices,\n  createGitGraphServices,\n  createInfoServices,\n  createPacketServices,\n  createPieServices,\n  createRadarServices,\n  createTreemapServices,\n  isArchitecture,\n  isBranch,\n  isCommit,\n  isGitGraph,\n  isInfo,\n  isMerge,\n  isPacket,\n  isPacketBlock,\n  isPie,\n  isPieSection,\n  isTreemapDoc,\n  parse\n};\n"], "mappings": ";;;;;;;;AAqEA,IAAI,UAAU,CAAC;AACf,IAAI,eAAe;AAAA,EACjB,MAAsB,gBAAAA,QAAO,YAAY;AACvC,UAAM,EAAE,oBAAoB,oBAAoB,IAAI,MAAM,OAAO,8BAAgD;AACjH,UAAM,SAAS,oBAAoB,EAAE,KAAK,OAAO;AACjD,YAAQ,OAAO;AAAA,EACjB,GAAG,MAAM;AAAA,EACT,QAAwB,gBAAAA,QAAO,YAAY;AACzC,UAAM,EAAE,sBAAsB,sBAAsB,IAAI,MAAM,OAAO,gCAAkD;AACvH,UAAM,SAAS,sBAAsB,EAAE,OAAO,OAAO;AACrD,YAAQ,SAAS;AAAA,EACnB,GAAG,QAAQ;AAAA,EACX,KAAqB,gBAAAA,QAAO,YAAY;AACtC,UAAM,EAAE,mBAAmB,mBAAmB,IAAI,MAAM,OAAO,6BAA+C;AAC9G,UAAM,SAAS,mBAAmB,EAAE,IAAI,OAAO;AAC/C,YAAQ,MAAM;AAAA,EAChB,GAAG,KAAK;AAAA,EACR,cAA8B,gBAAAA,QAAO,YAAY;AAC/C,UAAM,EAAE,4BAA4B,4BAA4B,IAAI,MAAM,OAAO,sCAAwD;AACzI,UAAM,SAAS,4BAA4B,EAAE,aAAa,OAAO;AACjE,YAAQ,eAAe;AAAA,EACzB,GAAG,cAAc;AAAA,EACjB,UAA0B,gBAAAA,QAAO,YAAY;AAC3C,UAAM,EAAE,wBAAwB,wBAAwB,IAAI,MAAM,OAAO,kCAAoD;AAC7H,UAAM,SAAS,wBAAwB,EAAE,SAAS,OAAO;AACzD,YAAQ,WAAW;AAAA,EACrB,GAAG,UAAU;AAAA,EACb,OAAuB,gBAAAA,QAAO,YAAY;AACxC,UAAM,EAAE,qBAAqB,qBAAqB,IAAI,MAAM,OAAO,+BAAiD;AACpH,UAAM,SAAS,qBAAqB,EAAE,MAAM,OAAO;AACnD,YAAQ,QAAQ;AAAA,EAClB,GAAG,OAAO;AAAA,EACV,SAAyB,gBAAAA,QAAO,YAAY;AAC1C,UAAM,EAAE,uBAAuB,uBAAuB,IAAI,MAAM,OAAO,iCAAmD;AAC1H,UAAM,SAAS,uBAAuB,EAAE,QAAQ,OAAO;AACvD,YAAQ,UAAU;AAAA,EACpB,GAAG,SAAS;AACd;AACA,eAAe,MAAM,aAAa,MAAM;AACtC,QAAM,cAAc,aAAa,WAAW;AAC5C,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,yBAAyB,WAAW,EAAE;AAAA,EACxD;AACA,MAAI,CAAC,QAAQ,WAAW,GAAG;AACzB,UAAM,YAAY;AAAA,EACpB;AACA,QAAM,SAAS,QAAQ,WAAW;AAClC,QAAM,SAAS,OAAO,MAAM,IAAI;AAChC,MAAI,OAAO,YAAY,SAAS,KAAK,OAAO,aAAa,SAAS,GAAG;AACnE,UAAM,IAAI,kBAAkB,MAAM;AAAA,EACpC;AACA,SAAO,OAAO;AAChB;AAde;AAefA,QAAO,OAAO,OAAO;AACrB,IAAI,oBAAoB,cAAc,MAAM;AAAA,EA3H5C,OA2H4C;AAAA;AAAA;AAAA,EAC1C,YAAY,QAAQ;AAClB,UAAM,cAAc,OAAO,YAAY,IAAI,CAAC,QAAQ,IAAI,OAAO,EAAE,KAAK,IAAI;AAC1E,UAAM,eAAe,OAAO,aAAa,IAAI,CAAC,QAAQ,IAAI,OAAO,EAAE,KAAK,IAAI;AAC5E,UAAM,mBAAmB,WAAW,IAAI,YAAY,EAAE;AACtD,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO;AACL,IAAAA,QAAO,MAAM,mBAAmB;AAAA,EAClC;AACF;", "names": ["__name"]}