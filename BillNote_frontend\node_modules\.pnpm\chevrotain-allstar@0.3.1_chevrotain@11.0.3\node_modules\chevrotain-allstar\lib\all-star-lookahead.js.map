{"version": 3, "file": "all-star-lookahead.js", "sourceRoot": "", "sources": ["../src/all-star-lookahead.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,EAGH,YAAY,EACZ,UAAU,EAGV,WAAW,EACX,WAAW,EACX,MAAM,EACN,mBAAmB,EACnB,gCAAgC,EAChC,uBAAuB,EACvB,UAAU,EACV,QAAQ,EAER,oBAAoB,EAGpB,iBAAiB,EAEpB,MAAM,YAAY,CAAC;AACpB,OAAO,EAGH,aAAa,EACb,cAAc,EACd,WAAW,EACX,SAAS,EAET,iBAAiB,EACjB,cAAc,EAEjB,MAAM,UAAU,CAAC;AAClB,OAAO,EAEH,YAAY,EAGZ,SAAS,EACT,eAAe,EAClB,MAAM,UAAU,CAAC;AAClB,OAAO,GAAG,MAAM,kBAAkB,CAAC;AACnC,OAAO,OAAO,MAAM,sBAAsB,CAAC;AAC3C,OAAO,MAAM,MAAM,qBAAqB,CAAC;AACzC,OAAO,GAAG,MAAM,kBAAkB,CAAC;AACnC,OAAO,OAAO,MAAM,sBAAsB,CAAC;AAC3C,OAAO,OAAO,MAAM,sBAAsB,CAAC;AAC3C,OAAO,OAAO,MAAM,sBAAsB,CAAC;AAC3C,OAAO,MAAM,MAAM,qBAAqB,CAAC;AAMzC,SAAS,cAAc,CAAC,UAAyB,EAAE,QAAgB;IAC/D,MAAM,GAAG,GAAoC,EAAE,CAAA;IAC/C,OAAO,CAAC,YAAY,EAAE,EAAE;QACpB,MAAM,GAAG,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAA;QACnC,IAAI,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;QACvB,IAAI,QAAQ,KAAK,SAAS,EAAE;YACxB,OAAO,QAAQ,CAAA;SAClB;aAAM;YACH,QAAQ,GAAG;gBACP,aAAa,EAAE,UAAU;gBACzB,QAAQ;gBACR,MAAM,EAAE,EAAE;aACb,CAAA;YACD,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAA;YACnB,OAAO,QAAQ,CAAA;SAClB;IACL,CAAC,CAAA;AACL,CAAC;AAED,MAAM,YAAY;IAAlB;QACY,eAAU,GAAc,EAAE,CAAA;IAkBtC,CAAC;IAhBG,EAAE,CAAC,KAAa;QACZ,OAAO,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;IACpE,CAAC;IAED,GAAG,CAAC,KAAa,EAAE,KAAc;QAC7B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,CAAA;IAClC,CAAC;IAED,QAAQ;QACJ,IAAI,KAAK,GAAG,EAAE,CAAA;QACd,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAA;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC3B,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;SACnD;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;CACJ;AAQD,MAAM,gBAAgB,GAAG,IAAI,YAAY,EAAE,CAAA;AAM3C,MAAM,OAAO,uBAAwB,SAAQ,oBAAoB;IAM7D,YAAY,OAAgC;;QACxC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,mCAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3E,CAAC;IAEQ,UAAU,CAAC,OAA0B;QAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;IAEQ,wCAAwC;QAC7C,OAAO,EAAE,CAAC;IACd,CAAC;IAEQ,2BAA2B;QAChC,OAAO,EAAE,CAAC;IACd,CAAC;IAEQ,4BAA4B,CAAC,OAMrC;QACG,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,aAAa,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC;QAC9E,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,aAAa,GAAG,aAAa,CAAC,QAAQ,CAAC;QAC7C,MAAM,WAAW,GAAgC,GAAG,CAChD,iBAAiB,CAAC;YACd,YAAY,EAAE,CAAC;YACf,UAAU,EAAE,cAAc;YAC1B,QAAQ,EAAE,aAAa;YACvB,IAAI,EAAE,IAAI;SACb,CAAC,EACF,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAC/C,CAAA;QAED,IAAI,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5D,MAAM,WAAW,GAAG,MAAM,CACtB,WAAW,EACX,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE;gBACrB,OAAO,CAAC,OAAO,EAAE,CAAC,WAAW,EAAE,EAAE;oBAC7B,IAAI,WAAW,EAAE;wBACb,MAAM,CAAC,WAAW,CAAC,YAAa,CAAC,GAAG,GAAG,CAAA;wBACvC,OAAO,CAAC,WAAW,CAAC,eAAgB,EAAE,CAAC,iBAAiB,EAAE,EAAE;4BACxD,MAAM,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAA;wBACnC,CAAC,CAAC,CAAA;qBACL;gBACL,CAAC,CAAC,CAAA;gBACF,OAAO,MAAM,CAAA;YACjB,CAAC,EACD,EAA4B,CAC/B,CAAA;YAED,IAAI,aAAa,EAAE;gBACf,OAAO,UAA4B,MAAM;;oBACrC,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;oBAC5B,MAAM,UAAU,GAAuB,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;oBAC1E,IAAI,MAAM,KAAK,SAAS,IAAI,UAAU,KAAK,SAAS,EAAE;wBAClD,MAAM,IAAI,GAAG,MAAA,MAAM,CAAC,UAAU,CAAC,0CAAE,IAAI,CAAA;wBACrC,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;4BACjD,OAAO,SAAS,CAAC;yBACpB;qBACJ;oBACD,OAAO,UAAU,CAAA;gBACrB,CAAC,CAAA;aACJ;iBAAM;gBACH,OAAO;oBACH,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;oBAC5B,OAAO,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAC/C,CAAC,CAAA;aACJ;SACJ;aAAM,IAAI,aAAa,EAAE;YACtB,OAAO,UAA4B,MAAM;gBACrC,MAAM,UAAU,GAAG,IAAI,YAAY,EAAE,CAAA;gBACrC,MAAM,MAAM,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAA;gBACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC7B,MAAM,IAAI,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,CAAC,EAAE,IAAI,CAAA;oBAC7B,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;iBAC3D;gBACD,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;gBACpF,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;YAC3D,CAAC,CAAA;SACJ;aAAM;YACH,OAAO;gBACH,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;gBAC1F,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;YAC3D,CAAC,CAAA;SACJ;IACL,CAAC;IAEQ,yBAAyB,CAAC,OAMlC;QACG,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC;QACzE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QACxD,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,aAAa,GAAG,aAAa,CAAC,QAAQ,CAAC;QAC7C,MAAM,IAAI,GAAG,GAAG,CACZ,iBAAiB,CAAC;YACd,YAAY,EAAE,CAAC;YACf,UAAU,EAAE,cAAc;YAC1B,QAAQ;YACR,IAAI;SACP,CAAC,EACF,CAAC,CAAC,EAAE,EAAE;YACJ,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAC5B,CAAC,CACF,CAAA;QAED,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC9D,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YACnB,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;YAEtC,IACE,iBAAiB,CAAC,MAAM,KAAK,CAAC;gBAC9B,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,EAC7C;gBACA,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAA;gBAC9C,MAAM,sBAAsB,GAAG,iBAAiB,CAAC,YAAY,CAAA;gBAE7D,OAAO;oBACL,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,KAAK,sBAAsB,CAAA;gBAC3D,CAAC,CAAA;aACF;iBAAM;gBACL,MAAM,WAAW,GAAG,MAAM,CACxB,iBAAiB,EACjB,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBACtB,IAAI,WAAW,KAAK,SAAS,EAAE;wBAC7B,MAAM,CAAC,WAAW,CAAC,YAAa,CAAC,GAAG,IAAI,CAAA;wBACxC,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC,iBAAiB,EAAE,EAAE;4BACzD,MAAM,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAA;wBAClC,CAAC,CAAC,CAAA;qBACH;oBACD,OAAO,MAAM,CAAA;gBACf,CAAC,EACD,EAA6B,CAC9B,CAAA;gBAED,OAAO;oBACL,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;oBAC5B,OAAO,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,IAAI,CAAA;gBACrD,CAAC,CAAA;aACF;SACF;QACD,OAAO;YACL,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAA;YACvF,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAA;IACP,CAAC;CAEJ;AAED,SAAS,aAAa,CAAC,SAAsC,EAAE,UAAU,GAAG,IAAI;IAC5E,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAA;IAEjC,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;QACzB,MAAM,MAAM,GAAG,IAAI,GAAG,EAAU,CAAA;QAChC,KAAK,MAAM,OAAO,IAAI,GAAG,EAAE;YACvB,IAAI,OAAO,KAAK,SAAS,EAAE;gBACvB,IAAI,UAAU,EAAE;oBACZ,iCAAiC;oBACjC,MAAK;iBACR;qBAAM;oBACH,OAAO,KAAK,CAAC;iBAChB;aACJ;YACD,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,YAAa,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,eAAgB,CAAC,CAAA;YACxE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;gBACzB,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACpB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;wBACpB,OAAO,KAAK,CAAA;qBACf;iBACJ;qBAAM;oBACH,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;oBAClB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;iBACpB;aACJ;SACJ;KACJ;IACD,OAAO,IAAI,CAAA;AACf,CAAC;AAED,SAAS,gBAAgB,CAAC,GAAQ;IAC9B,MAAM,cAAc,GAAG,GAAG,CAAC,cAAc,CAAC,MAAM,CAAA;IAChD,MAAM,aAAa,GAAe,KAAK,CAAC,cAAc,CAAC,CAAA;IACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;QACrC,aAAa,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;KAC9D;IACD,OAAO,aAAa,CAAC;AACzB,CAAC;AAED,SAAS,eAAe,CAEpB,SAAqB,EACrB,QAAgB,EAChB,YAA0B,EAC1B,OAAwB;IAExB,MAAM,GAAG,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAA;IAC7C,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;IACrB,IAAI,KAAK,KAAK,SAAS,EAAE;QACrB,MAAM,OAAO,GAAG,iBAAiB,CAAC,GAAG,CAAC,aAAyB,CAAC,CAAA;QAChE,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAA;QAC9C,GAAG,CAAC,KAAK,GAAG,KAAK,CAAA;KACpB;IAED,MAAM,GAAG,GAAG,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC,CAAA;IAC7E,OAAO,GAAG,CAAA;AACd,CAAC;AAED,SAAS,gBAAgB,CAErB,GAAQ,EACR,EAAY,EACZ,YAA0B,EAC1B,OAAwB;IAExB,IAAI,SAAS,GAAG,EAAE,CAAA;IAElB,IAAI,CAAC,GAAG,CAAC,CAAA;IACT,MAAM,IAAI,GAAa,EAAE,CAAA;IACzB,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAA;IAEpB,OAAO,IAAI,EAAE;QACT,IAAI,CAAC,GAAG,sBAAsB,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;QAC5C,IAAI,CAAC,KAAK,SAAS,EAAE;YACjB,CAAC,GAAG,sBAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC,CAAA;SACxF;QAED,IAAI,CAAC,KAAK,SAAS,EAAE;YACjB,OAAO,yBAAyB,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,CAAA;SACvD;QAED,IAAI,CAAC,CAAC,aAAa,KAAK,IAAI,EAAE;YAC1B,OAAO,CAAC,CAAC,UAAU,CAAA;SACtB;QAED,SAAS,GAAG,CAAC,CAAA;QACb,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACZ,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAA;KACnB;AACL,CAAC;AAED,SAAS,sBAAsB,CAE3B,GAAQ,EACR,SAAmB,EACnB,KAAa,EACb,SAAiB,EACjB,YAA0B,EAC1B,OAAwB;IAExB,MAAM,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE,YAAY,CAAC,CAAA;IACrE,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE;QAClB,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,CAAA;QAC5C,OAAO,SAAS,CAAA;KACnB;IAED,IAAI,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAA;IACjC,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;IAEtD,IAAI,YAAY,KAAK,SAAS,EAAE;QAC5B,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAA;QAC7B,QAAQ,CAAC,UAAU,GAAG,YAAY,CAAA;QAClC,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,YAAY,CAAA;KAC5C;SAAM,IAAI,gCAAgC,CAAC,KAAK,CAAC,EAAE;QAChD,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAE,CAAA;QACnC,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAA;QAC7B,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAA;QAChC,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,UAAU,CAAA;QACvC,wBAAwB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAA;KAC9E;IAED,QAAQ,GAAG,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;IACtD,OAAO,QAAQ,CAAA;AACnB,CAAC;AAED,SAAS,wBAAwB,CAE7B,GAAQ,EACR,SAAiB,EACjB,gBAA0B,EAC1B,OAAwB;IAExB,MAAM,UAAU,GAAgB,EAAE,CAAA;IAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE;QACjC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;KACxC;IACD,MAAM,QAAQ,GAAG,GAAG,CAAC,aAAa,CAAA;IAClC,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAA;IAClC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;IACtC,MAAM,OAAO,GAAG,mBAAmB,CAAC;QAChC,YAAY;QACZ,gBAAgB;QAChB,UAAU;QACV,UAAU;KACb,CAAC,CAAA;IACF,OAAO,CAAC,OAAO,CAAC,CAAA;AACpB,CAAC;AAED,SAAS,mBAAmB,CAAC,OAK5B;IACG,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,EAAE,CAChD,UAAU,CAAC,OAAO,CAAC,CACtB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACZ,MAAM,UAAU,GACZ,OAAO,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAA;IAC9D,IAAI,WAAW,GACX,qCAAqC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAC9D,IAAI,CACP,SAAS,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG;QAClE,YAAY,OAAO,CAAC,YAAY,CAAC,IAAI,WAAW;QAChD,IAAI,OAAO,6DAA6D,CAAA;IAE5E,WAAW;QACP,WAAW;YACX,8FAA8F;YAC9F,sBAAsB,CAAA;IAC1B,OAAO,WAAW,CAAA;AACtB,CAAC;AAED,SAAS,oBAAoB,CAAC,IAA+B;IACzD,IAAI,IAAI,YAAY,WAAW,EAAE;QAC7B,OAAO,SAAS,CAAA;KACnB;SAAM,IAAI,IAAI,YAAY,MAAM,EAAE;QAC/B,OAAO,QAAQ,CAAA;KAClB;SAAM,IAAI,IAAI,YAAY,WAAW,EAAE;QACpC,OAAO,IAAI,CAAA;KACd;SAAM,IAAI,IAAI,YAAY,mBAAmB,EAAE;QAC5C,OAAO,cAAc,CAAA;KACxB;SAAM,IAAI,IAAI,YAAY,gCAAgC,EAAE;QACzD,OAAO,kBAAkB,CAAA;KAC5B;SAAM,IAAI,IAAI,YAAY,uBAAuB,EAAE;QAChD,OAAO,UAAU,CAAA;KACpB;SAAM,IAAI,IAAI,YAAY,UAAU,EAAE;QACnC,OAAO,MAAM,CAAA;KAChB;SAAM,IAAI,IAAI,YAAY,QAAQ,EAAE;QACjC,OAAO,SAAS,CAAA;KACnB;SAAM;QACH,MAAM,KAAK,CAAC,sBAAsB,CAAC,CAAA;KACtC;AACL,CAAC;AAED,SAAS,yBAAyB,CAC9B,IAAc,EACd,QAAkB,EAClB,OAAe;IAEf,MAAM,eAAe,GAAG,OAAO,CAC3B,QAAQ,CAAC,OAAO,CAAC,QAAQ,EACzB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAC7B,CAAA;IACD,MAAM,cAAc,GAAG,MAAM,CACzB,eAAe;SACV,MAAM,CAAC,CAAC,CAAC,EAAuB,EAAE,CAAC,CAAC,YAAY,cAAc,CAAC;SAC/D,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,EAC5B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CACxB,CAAA;IACD,OAAO;QACH,WAAW,EAAE,OAAO;QACpB,kBAAkB,EAAE,cAAc;QAClC,SAAS,EAAE,IAAI;KAClB,CAAA;AACL,CAAC;AAED,SAAS,sBAAsB,CAC3B,KAAe,EACf,KAAa;IAEb,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;AAC1C,CAAC;AAED,SAAS,eAAe,CACpB,OAAqB,EACrB,KAAa,EACb,YAA0B;IAE1B,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAA;IACvC,MAAM,iBAAiB,GAAgB,EAAE,CAAA;IAEzC,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;QAC9B,IAAI,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,EAAE;YAClC,SAAQ;SACX;QACD,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE;YAChC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACzB,SAAQ;SACX;QACD,MAAM,gBAAgB,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAA;QACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;YACzC,MAAM,MAAM,GAAG,kBAAkB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;YACpD,IAAI,MAAM,KAAK,SAAS,EAAE;gBACtB,YAAY,CAAC,GAAG,CAAC;oBACb,KAAK,EAAE,MAAM;oBACb,GAAG,EAAE,CAAC,CAAC,GAAG;oBACV,KAAK,EAAE,CAAC,CAAC,KAAK;iBACjB,CAAC,CAAA;aACL;SACJ;KACJ;IAED,IAAI,KAA+B,CAAA;IAEnC,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE;QAC3D,KAAK,GAAG,YAAY,CAAA;KACvB;IAED,IAAI,KAAK,KAAK,SAAS,EAAE;QACrB,KAAK,GAAG,IAAI,YAAY,EAAE,CAAA;QAC1B,KAAK,MAAM,CAAC,IAAI,YAAY,CAAC,QAAQ,EAAE;YACnC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;SACpB;KACJ;IAED,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE;QAClE,KAAK,MAAM,CAAC,IAAI,iBAAiB,EAAE;YAC/B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;SACf;KACJ;IAED,OAAO,KAAK,CAAA;AAChB,CAAC;AAED,SAAS,kBAAkB,CACvB,UAAsB,EACtB,KAAa;IAEb,IACI,UAAU,YAAY,cAAc;QACpC,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,SAAS,CAAC,EAC3C;QACE,OAAO,UAAU,CAAC,MAAM,CAAA;KAC3B;IACD,OAAO,SAAS,CAAA;AACpB,CAAC;AAED,SAAS,YAAY,CACjB,OAAqB,EACrB,YAA0B;IAE1B,IAAI,GAAuB,CAAA;IAC3B,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;QAC9B,IAAI,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;YACjC,IAAI,GAAG,KAAK,SAAS,EAAE;gBACnB,GAAG,GAAG,CAAC,CAAC,GAAG,CAAA;aACd;iBAAM,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,EAAE;gBACtB,OAAO,SAAS,CAAA;aACnB;SACJ;KACJ;IACD,OAAO,GAAG,CAAA;AACd,CAAC;AAED,SAAS,WAAW,CAAC,OAAqB;IACtC,OAAO;QACH,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,EAAE;QACT,aAAa,EAAE,KAAK;QACpB,UAAU,EAAE,CAAC,CAAC;KACjB,CAAA;AACL,CAAC;AAED,SAAS,UAAU,CACf,GAAQ,EACR,IAAc,EACd,KAAa,EACb,EAAY;IAEZ,EAAE,GAAG,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;IACzB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,CAAA;IACnC,OAAO,EAAE,CAAA;AACb,CAAC;AAED,SAAS,WAAW,CAAC,GAAQ,EAAE,KAAe;IAC1C,IAAI,KAAK,KAAK,SAAS,EAAE;QACrB,OAAO,KAAK,CAAA;KACf;IACD,uCAAuC;IACvC,0FAA0F;IAC1F,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAA;IAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IACnC,IAAI,QAAQ,KAAK,SAAS,EAAE;QACxB,OAAO,QAAQ,CAAA;KAClB;IACD,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAA;IACxB,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAA;IAC1B,OAAO,KAAK,CAAA;AAChB,CAAC;AAED,SAAS,iBAAiB,CAAC,QAAkB;IACzC,MAAM,OAAO,GAAG,IAAI,YAAY,EAAE,CAAA;IAElC,MAAM,mBAAmB,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAA;IACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,EAAE,CAAC,EAAE,EAAE;QAC1C,MAAM,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;QAC7C,MAAM,MAAM,GAAc;YACtB,KAAK,EAAE,MAAM;YACb,GAAG,EAAE,CAAC;YACN,KAAK,EAAE,EAAE;SACZ,CAAA;QACD,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;KAC3B;IAED,OAAO,OAAO,CAAA;AAClB,CAAC;AAED,SAAS,OAAO,CAAC,MAAiB,EAAE,OAAqB;IACrD,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAA;IAEtB,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,EAAE;QAC1B,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,MAAM,QAAQ,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;YAClC,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,EAAG,CAAA;YACnC,MAAM,YAAY,GAAc;gBAC5B,KAAK,EAAE,WAAW;gBAClB,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,KAAK,EAAE,QAAQ;aAClB,CAAA;YACD,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;SACjC;aAAM;YACH,oDAAoD;YACpD,yEAAyE;YACzE,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;SACtB;QACD,OAAM;KACT;IAED,IAAI,CAAC,CAAC,CAAC,sBAAsB,EAAE;QAC3B,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;KACtB;IAED,MAAM,gBAAgB,GAAG,CAAC,CAAC,WAAW,CAAC,MAAM,CAAA;IAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE;QACvC,MAAM,UAAU,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QACnC,MAAM,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;QAE9C,IAAI,CAAC,KAAK,SAAS,EAAE;YACjB,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;SACtB;KACJ;AACL,CAAC;AAED,SAAS,gBAAgB,CACrB,MAAiB,EACjB,UAAsB;IAEtB,IAAI,UAAU,YAAY,iBAAiB,EAAE;QACzC,OAAO;YACH,KAAK,EAAE,UAAU,CAAC,MAAM;YACxB,GAAG,EAAE,MAAM,CAAC,GAAG;YACf,KAAK,EAAE,MAAM,CAAC,KAAK;SACtB,CAAA;KACJ;SAAM,IAAI,UAAU,YAAY,cAAc,EAAE;QAC7C,MAAM,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC,CAAA;QACvD,OAAO;YACH,KAAK,EAAE,UAAU,CAAC,MAAM;YACxB,GAAG,EAAE,MAAM,CAAC,GAAG;YACf,KAAK;SACR,CAAA;KACJ;IACD,OAAO,SAAS,CAAA;AACpB,CAAC;AAED,SAAS,wBAAwB,CAAC,OAAqB;IACnD,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;QAC9B,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE;YAChC,OAAO,IAAI,CAAA;SACd;KACJ;IACD,OAAO,KAAK,CAAA;AAChB,CAAC;AAED,SAAS,0BAA0B,CAAC,OAAqB;IACrD,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;QAC9B,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE;YAChC,OAAO,KAAK,CAAA;SACf;KACJ;IACD,OAAO,IAAI,CAAA;AACf,CAAC;AAED,SAAS,gCAAgC,CAAC,OAAqB;IAC3D,IAAI,0BAA0B,CAAC,OAAO,CAAC,EAAE;QACrC,OAAO,IAAI,CAAA;KACd;IACD,MAAM,OAAO,GAAG,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IACvD,MAAM,SAAS,GACX,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAA;IAC3E,OAAO,SAAS,CAAA;AACpB,CAAC;AAED,SAAS,qBAAqB,CAC1B,OAA6B;IAE7B,MAAM,YAAY,GAAG,IAAI,GAAG,EAAmC,CAAA;IAC/D,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE;QACrB,MAAM,GAAG,GAAG,eAAe,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;QACrC,IAAI,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAChC,IAAI,IAAI,KAAK,SAAS,EAAE;YACpB,IAAI,GAAG,EAAE,CAAA;YACT,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;SAC9B;QACD,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;KACrB;IACD,OAAO,YAAY,CAAA;AACvB,CAAC;AAED,SAAS,oBAAoB,CACzB,OAA6C;IAE7C,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;QAC9C,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,OAAO,IAAI,CAAA;SACd;KACJ;IACD,OAAO,KAAK,CAAA;AAChB,CAAC;AAED,SAAS,4BAA4B,CACjC,OAA6C;IAE7C,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;QAC9C,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,OAAO,IAAI,CAAA;SACd;KACJ;IACD,OAAO,KAAK,CAAA;AAChB,CAAC"}