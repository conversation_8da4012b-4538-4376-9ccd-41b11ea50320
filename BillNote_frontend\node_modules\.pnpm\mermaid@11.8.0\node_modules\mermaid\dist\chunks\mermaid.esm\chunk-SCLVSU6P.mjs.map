{"version": 3, "sources": ["../../../src/diagrams/globalStyles.ts"], "sourcesContent": ["export const getIconStyles = () => `\n  /* Font Awesome icon styling - consolidated */\n  .label-icon {\n    display: inline-block;\n    height: 1em;\n    overflow: visible;\n    vertical-align: -0.125em;\n  }\n  \n  .node .label-icon path {\n    fill: currentColor;\n    stroke: revert;\n    stroke-width: revert;\n  }\n`;\n"], "mappings": ";;;;;AAAO,IAAM,gBAAgB,6BAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAAN;", "names": []}