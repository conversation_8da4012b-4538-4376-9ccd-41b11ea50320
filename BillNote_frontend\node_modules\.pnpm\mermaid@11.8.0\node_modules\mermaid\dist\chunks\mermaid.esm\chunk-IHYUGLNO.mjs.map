{"version": 3, "sources": ["../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/util.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/add-border-segments.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/coordinate-system.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/data/list.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/greedy-fas.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/acyclic.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/normalize.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/util.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/feasible-tree.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/dijkstra.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/floyd-warshall.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/topsort.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/dfs.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/postorder.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/preorder.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/network-simplex.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/index.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/nesting-graph.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/add-subgraph-constraints.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/build-layer-graph.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/cross-count.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/init-order.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/barycenter.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/resolve-conflicts.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/sort.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/sort-subgraph.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/index.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/parent-dummy-chains.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/position/bk.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/position/index.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/layout.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\n\nexport {\n  addDummyNode,\n  simplify,\n  asNonCompoundGraph,\n  successorWeights,\n  predecessorWeights,\n  intersectRect,\n  buildLayerMatrix,\n  normalizeRanks,\n  removeEmptyRanks,\n  addBorderNode,\n  maxRank,\n  partition,\n  time,\n  notime,\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  var v;\n  do {\n    v = _.uniqueId(name);\n  } while (g.hasNode(v));\n\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  var simplified = new Graph().setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    simplified.setNode(v, g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };\n    var label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen),\n    });\n  });\n  return simplified;\n}\n\nfunction asNonCompoundGraph(g) {\n  var simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  _.forEach(g.edges(), function (e) {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\n\nfunction successorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var sucs = {};\n    _.forEach(g.outEdges(v), function (e) {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\nfunction predecessorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var preds = {};\n    _.forEach(g.inEdges(v), function (e) {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  var x = rect.x;\n  var y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = rect.width / 2;\n  var h = rect.height / 2;\n\n  if (!dx && !dy) {\n    throw new Error('Not possible to find intersection inside of the rectangle');\n  }\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  var layering = _.map(_.range(maxRank(g) + 1), function () {\n    return [];\n  });\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    var rank = node.rank;\n    if (!_.isUndefined(rank)) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  var min = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (_.has(node, 'rank')) {\n      node.rank -= min;\n    }\n  });\n}\n\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  var offset = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n\n  var layers = [];\n  _.forEach(g.nodes(), function (v) {\n    var rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n\n  var delta = 0;\n  var nodeRankFactor = g.graph().nodeRankFactor;\n  _.forEach(layers, function (vs, i) {\n    if (_.isUndefined(vs) && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (delta) {\n      _.forEach(vs, function (v) {\n        g.node(v).rank += delta;\n      });\n    }\n  });\n}\n\nfunction addBorderNode(g, prefix, rank, order) {\n  var node = {\n    width: 0,\n    height: 0,\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, 'border', node, prefix);\n}\n\nfunction maxRank(g) {\n  return _.max(\n    _.map(g.nodes(), function (v) {\n      var rank = g.node(v).rank;\n      if (!_.isUndefined(rank)) {\n        return rank;\n      }\n    }),\n  );\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  var result = { lhs: [], rhs: [] };\n  _.forEach(collection, function (value) {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  var start = _.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + ' time: ' + (_.now() - start) + 'ms');\n  }\n}\n\nfunction notime(name, fn) {\n  return fn();\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { addBorderSegments };\n\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    var children = g.children(v);\n    var node = g.node(v);\n    if (children.length) {\n      _.forEach(children, dfs);\n    }\n\n    if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (var rank = node.minRank, maxRank = node.maxRank + 1; rank < maxRank; ++rank) {\n        addBorderNode(g, 'borderLeft', '_bl', v, node, rank);\n        addBorderNode(g, 'borderRight', '_br', v, node, rank);\n      }\n    }\n  }\n\n  _.forEach(g.children(), dfs);\n}\n\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  var label = { width: 0, height: 0, rank: rank, borderType: prop };\n  var prev = sgNode[prop][rank - 1];\n  var curr = util.addDummyNode(g, 'border', label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, { weight: 1 });\n  }\n}\n", "import * as _ from 'lodash-es';\n\nexport { adjust, undo };\n\nfunction adjust(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapWidthHeight(g);\n  }\n}\n\nfunction undo(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'bt' || rankDir === 'rl') {\n    reverseY(g);\n  }\n\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\n\nfunction swapWidthHeight(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapWidthHeightOne(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    swapWidthHeightOne(g.edge(e));\n  });\n}\n\nfunction swapWidthHeightOne(attrs) {\n  var w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\n\nfunction reverseY(g) {\n  _.forEach(g.nodes(), function (v) {\n    reverseYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, reverseYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      reverseYOne(edge);\n    }\n  });\n}\n\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\n\nfunction swapXY(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapXYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, swapXYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      swapXYOne(edge);\n    }\n  });\n}\n\nfunction swapXYOne(attrs) {\n  var x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}\n", "/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nexport { List };\n\nclass List {\n  constructor() {\n    var sentinel = {};\n    sentinel._next = sentinel._prev = sentinel;\n    this._sentinel = sentinel;\n  }\n  dequeue() {\n    var sentinel = this._sentinel;\n    var entry = sentinel._prev;\n    if (entry !== sentinel) {\n      unlink(entry);\n      return entry;\n    }\n  }\n  enqueue(entry) {\n    var sentinel = this._sentinel;\n    if (entry._prev && entry._next) {\n      unlink(entry);\n    }\n    entry._next = sentinel._next;\n    sentinel._next._prev = entry;\n    sentinel._next = entry;\n    entry._prev = sentinel;\n  }\n  toString() {\n    var strs = [];\n    var sentinel = this._sentinel;\n    var curr = sentinel._prev;\n    while (curr !== sentinel) {\n      strs.push(JSON.stringify(curr, filterOutLinks));\n      curr = curr._prev;\n    }\n    return '[' + strs.join(', ') + ']';\n  }\n}\n\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\n\nfunction filterOutLinks(k, v) {\n  if (k !== '_next' && k !== '_prev') {\n    return v;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { List } from './data/list.js';\n\n/*\n * A greedy heuristic for finding a feedback arc set for a graph. A feedback\n * arc set is a set of edges that can be removed to make a graph acyclic.\n * The algorithm comes from: <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, \"A fast and\n * effective heuristic for the feedback arc set problem.\" This implementation\n * adjusts that from the paper to allow for weighted edges.\n */\nexport { greedyFAS };\n\nvar DEFAULT_WEIGHT_FN = _.constant(1);\n\nfunction greedyFAS(g, weightFn) {\n  if (g.nodeCount() <= 1) {\n    return [];\n  }\n  var state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);\n  var results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);\n\n  // Expand multi-edges\n  return _.flatten(\n    _.map(results, function (e) {\n      return g.outEdges(e.v, e.w);\n    }),\n  );\n}\n\nfunction doGreedyFAS(g, buckets, zeroIdx) {\n  var results = [];\n  var sources = buckets[buckets.length - 1];\n  var sinks = buckets[0];\n\n  var entry;\n  while (g.nodeCount()) {\n    while ((entry = sinks.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    while ((entry = sources.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    if (g.nodeCount()) {\n      for (var i = buckets.length - 2; i > 0; --i) {\n        entry = buckets[i].dequeue();\n        if (entry) {\n          results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));\n          break;\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\nfunction removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {\n  var results = collectPredecessors ? [] : undefined;\n\n  _.forEach(g.inEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var uEntry = g.node(edge.v);\n\n    if (collectPredecessors) {\n      results.push({ v: edge.v, w: edge.w });\n    }\n\n    uEntry.out -= weight;\n    assignBucket(buckets, zeroIdx, uEntry);\n  });\n\n  _.forEach(g.outEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var w = edge.w;\n    var wEntry = g.node(w);\n    wEntry['in'] -= weight;\n    assignBucket(buckets, zeroIdx, wEntry);\n  });\n\n  g.removeNode(entry.v);\n\n  return results;\n}\n\nfunction buildState(g, weightFn) {\n  var fasGraph = new Graph();\n  var maxIn = 0;\n  var maxOut = 0;\n\n  _.forEach(g.nodes(), function (v) {\n    fasGraph.setNode(v, { v: v, in: 0, out: 0 });\n  });\n\n  // Aggregate weights on nodes, but also sum the weights across multi-edges\n  // into a single edge for the fasGraph.\n  _.forEach(g.edges(), function (e) {\n    var prevWeight = fasGraph.edge(e.v, e.w) || 0;\n    var weight = weightFn(e);\n    var edgeWeight = prevWeight + weight;\n    fasGraph.setEdge(e.v, e.w, edgeWeight);\n    maxOut = Math.max(maxOut, (fasGraph.node(e.v).out += weight));\n    maxIn = Math.max(maxIn, (fasGraph.node(e.w)['in'] += weight));\n  });\n\n  var buckets = _.range(maxOut + maxIn + 3).map(function () {\n    return new List();\n  });\n  var zeroIdx = maxIn + 1;\n\n  _.forEach(fasGraph.nodes(), function (v) {\n    assignBucket(buckets, zeroIdx, fasGraph.node(v));\n  });\n\n  return { graph: fasGraph, buckets: buckets, zeroIdx: zeroIdx };\n}\n\nfunction assignBucket(buckets, zeroIdx, entry) {\n  if (!entry.out) {\n    buckets[0].enqueue(entry);\n  } else if (!entry['in']) {\n    buckets[buckets.length - 1].enqueue(entry);\n  } else {\n    buckets[entry.out - entry['in'] + zeroIdx].enqueue(entry);\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { greedyFAS } from './greedy-fas.js';\n\nexport { run, undo };\n\nfunction run(g) {\n  var fas = g.graph().acyclicer === 'greedy' ? greedyFAS(g, weightFn(g)) : dfsFAS(g);\n  _.forEach(fas, function (e) {\n    var label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, _.uniqueId('rev'));\n  });\n\n  function weightFn(g) {\n    return function (e) {\n      return g.edge(e).weight;\n    };\n  }\n}\n\nfunction dfsFAS(g) {\n  var fas = [];\n  var stack = {};\n  var visited = {};\n\n  function dfs(v) {\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    _.forEach(g.outEdges(v), function (e) {\n      if (Object.prototype.hasOwnProperty.call(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n\n  _.forEach(g.nodes(), dfs);\n  return fas;\n}\n\nfunction undo(g) {\n  _.forEach(g.edges(), function (e) {\n    var label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n\n      var forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}\n", "/**\n * TypeScript type imports:\n *\n * @import { Graph } from '../graphlib/graph.js';\n */\nimport * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, undo };\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  _.forEach(g.edges(), function (edge) {\n    normalizeEdge(g, edge);\n  });\n}\n\n/**\n * @param {Graph} g\n */\nfunction normalizeEdge(g, e) {\n  var v = e.v;\n  var vRank = g.node(v).rank;\n  var w = e.w;\n  var wRank = g.node(w).rank;\n  var name = e.name;\n  var edgeLabel = g.edge(e);\n  var labelRank = edgeLabel.labelRank;\n\n  if (wRank === vRank + 1) return;\n\n  g.removeEdge(e);\n\n  /**\n   * @typedef {Object} Attrs\n   * @property {number} width\n   * @property {number} height\n   * @property {ReturnType<Graph[\"node\"]>} edgeLabel\n   * @property {any} edgeObj\n   * @property {ReturnType<Graph[\"node\"]>[\"rank\"]} rank\n   * @property {string} [dummy]\n   * @property {ReturnType<Graph[\"node\"]>[\"labelpos\"]} [labelpos]\n   */\n\n  /** @type {Attrs | undefined} */\n  var attrs = undefined;\n  var dummy, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0,\n      height: 0,\n      edgeLabel: edgeLabel,\n      edgeObj: e,\n      rank: vRank,\n    };\n    dummy = util.addDummyNode(g, 'edge', attrs, '_d');\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      attrs.dummy = 'edge-label';\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n\n  g.setEdge(v, w, { weight: edgeLabel.weight }, name);\n}\n\nfunction undo(g) {\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var origLabel = node.edgeLabel;\n    var w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({ x: node.x, y: node.y });\n      if (node.dummy === 'edge-label') {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { longestPath, slack };\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n\n  function dfs(v) {\n    var label = g.node(v);\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n\n    var rank = _.min(\n      _.map(g.outEdges(v), function (e) {\n        return dfs(e.w) - g.edge(e).minlen;\n      }),\n    );\n\n    if (\n      rank === Number.POSITIVE_INFINITY || // return value of _.map([]) for Lodash 3\n      rank === undefined || // return value of _.map([]) for Lodash 4\n      rank === null\n    ) {\n      // return value of _.map([null])\n      rank = 0;\n    }\n\n    return (label.rank = rank);\n  }\n\n  _.forEach(g.sources(), dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport { slack } from './util.js';\n\nexport { feasibleTree };\n\n/*\n * Constructs a spanning tree with tight edges and adjusted the input node's\n * ranks to achieve this. A tight edge is one that is has a length that matches\n * its \"minlen\" attribute.\n *\n * The basic structure for this function is derived from <PERSON><PERSON><PERSON>, et al., \"A\n * Technique for Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a DAG.\n *    2. Graph must be connected.\n *    3. Graph must have at least one node.\n *    5. Graph nodes must have been previously assigned a \"rank\" property that\n *       respects the \"minlen\" property of incident edges.\n *    6. Graph edges must have a \"minlen\" property.\n *\n * Post-conditions:\n *\n *    - Graph nodes will have their rank adjusted to ensure that all edges are\n *      tight.\n *\n * Returns a tree (undirected graph) that is constructed using only \"tight\"\n * edges.\n */\nfunction feasibleTree(g) {\n  var t = new Graph({ directed: false });\n\n  // Choose arbitrary node from which to start our tree\n  var start = g.nodes()[0];\n  var size = g.nodeCount();\n  t.setNode(start, {});\n\n  var edge, delta;\n  while (tightTree(t, g) < size) {\n    edge = findMinSlackEdge(t, g);\n    delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);\n    shiftRanks(t, g, delta);\n  }\n\n  return t;\n}\n\n/*\n * Finds a maximal tree of tight edges and returns the number of nodes in the\n * tree.\n */\nfunction tightTree(t, g) {\n  function dfs(v) {\n    _.forEach(g.nodeEdges(v), function (e) {\n      var edgeV = e.v,\n        w = v === edgeV ? e.w : edgeV;\n      if (!t.hasNode(w) && !slack(g, e)) {\n        t.setNode(w, {});\n        t.setEdge(v, w, {});\n        dfs(w);\n      }\n    });\n  }\n\n  _.forEach(t.nodes(), dfs);\n  return t.nodeCount();\n}\n\n/*\n * Finds the edge with the smallest slack that is incident on tree and returns\n * it.\n */\nfunction findMinSlackEdge(t, g) {\n  return _.minBy(g.edges(), function (e) {\n    if (t.hasNode(e.v) !== t.hasNode(e.w)) {\n      return slack(g, e);\n    }\n  });\n}\n\nfunction shiftRanks(t, g, delta) {\n  _.forEach(t.nodes(), function (v) {\n    g.node(v).rank += delta;\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\n\nexport { dijkstra };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(\n    g,\n    String(source),\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      },\n  );\n}\n\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n\n  var updateNeighbors = function (edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n\n    if (weight < 0) {\n      throw new Error(\n        'dijkstra does not allow negative edge weights. ' +\n          'Bad edge: ' +\n          edge +\n          ' Weight: ' +\n          weight,\n      );\n    }\n\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n\n  g.nodes().forEach(function (v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = { distance: distance };\n    pq.add(v, distance);\n  });\n\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n\n    edgeFn(v).forEach(updateNeighbors);\n  }\n\n  return results;\n}\n", "import * as _ from 'lodash-es';\n\nexport { floyd<PERSON>ars<PERSON> };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction floydWarshall(g, weightFn, edgeFn) {\n  return runFloydWarshall(\n    g,\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      },\n  );\n}\n\nfunction runFloydWarshall(g, weightFn, edgeFn) {\n  var results = {};\n  var nodes = g.nodes();\n\n  nodes.forEach(function (v) {\n    results[v] = {};\n    results[v][v] = { distance: 0 };\n    nodes.forEach(function (w) {\n      if (v !== w) {\n        results[v][w] = { distance: Number.POSITIVE_INFINITY };\n      }\n    });\n    edgeFn(v).forEach(function (edge) {\n      var w = edge.v === v ? edge.w : edge.v;\n      var d = weightFn(edge);\n      results[v][w] = { distance: d, predecessor: v };\n    });\n  });\n\n  nodes.forEach(function (k) {\n    var rowK = results[k];\n    nodes.forEach(function (i) {\n      var rowI = results[i];\n      nodes.forEach(function (j) {\n        var ik = rowI[k];\n        var kj = rowK[j];\n        var ij = rowI[j];\n        var altDistance = ik.distance + kj.distance;\n        if (altDistance < ij.distance) {\n          ij.distance = altDistance;\n          ij.predecessor = kj.predecessor;\n        }\n      });\n    });\n  });\n\n  return results;\n}\n", "import * as _ from 'lodash-es';\n\nexport { topsort, CycleException };\n\ntopsort.CycleException = CycleException;\n\nfunction topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n\n  function visit(node) {\n    if (Object.prototype.hasOwnProperty.call(stack, node)) {\n      throw new CycleException();\n    }\n\n    if (!Object.prototype.hasOwnProperty.call(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      _.each(g.predecessors(node), visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n\n  _.each(g.sinks(), visit);\n\n  if (_.size(visited) !== g.nodeCount()) {\n    throw new CycleException();\n  }\n\n  return results;\n}\n\nfunction CycleException() {}\nCycleException.prototype = new Error(); // must be an instance of Error to pass testing\n", "import * as _ from 'lodash-es';\n\nexport { dfs };\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * Order must be one of \"pre\" or \"post\".\n */\nfunction dfs(g, vs, order) {\n  if (!_.isArray(vs)) {\n    vs = [vs];\n  }\n\n  var navigation = (g.isDirected() ? g.successors : g.neighbors).bind(g);\n\n  var acc = [];\n  var visited = {};\n  _.each(vs, function (v) {\n    if (!g.hasNode(v)) {\n      throw new Error('Graph does not have node: ' + v);\n    }\n\n    doDfs(g, v, order === 'post', visited, navigation, acc);\n  });\n  return acc;\n}\n\nfunction doDfs(g, v, postorder, visited, navigation, acc) {\n  if (!Object.prototype.hasOwnProperty.call(visited, v)) {\n    visited[v] = true;\n\n    if (!postorder) {\n      acc.push(v);\n    }\n    _.each(navigation(v), function (w) {\n      doDfs(g, w, postorder, visited, navigation, acc);\n    });\n    if (postorder) {\n      acc.push(v);\n    }\n  }\n}\n", "import { dfs } from './dfs.js';\n\nexport { postorder };\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, 'post');\n}\n", "import { dfs } from './dfs.js';\n\nexport { preorder };\n\nfunction preorder(g, vs) {\n  return dfs(g, vs, 'pre');\n}\n", "import * as _ from 'lodash-es';\nimport * as alg from '../../graphlib/alg/index.js';\nimport { simplify } from '../util.js';\nimport { feasibleTree } from './feasible-tree.js';\nimport { longestPath, slack } from './util.js';\n\nexport { networkSimplex };\n\n// Expose some internals for testing purposes\nnetworkSimplex.initLowLimValues = initLowLimValues;\nnetworkSimplex.initCutValues = initCutValues;\nnetworkSimplex.calcCutValue = calcCutValue;\nnetworkSimplex.leaveEdge = leaveEdge;\nnetworkSimplex.enterEdge = enterEdge;\nnetworkSimplex.exchangeEdges = exchangeEdges;\n\n/*\n * The network simplex algorithm assigns ranks to each node in the input graph\n * and iteratively improves the ranking to reduce the length of edges.\n *\n * Preconditions:\n *\n *    1. The input graph must be a DAG.\n *    2. All nodes in the graph must have an object value.\n *    3. All edges in the graph must have \"minlen\" and \"weight\" attributes.\n *\n * Postconditions:\n *\n *    1. All nodes in the graph will have an assigned \"rank\" attribute that has\n *       been optimized by the network simplex algorithm. Ranks start at 0.\n *\n *\n * A rough sketch of the algorithm is as follows:\n *\n *    1. Assign initial ranks to each node. We use the longest path algorithm,\n *       which assigns ranks to the lowest position possible. In general this\n *       leads to very wide bottom ranks and unnecessarily long edges.\n *    2. Construct a feasible tight tree. A tight tree is one such that all\n *       edges in the tree have no slack (difference between length of edge\n *       and minlen for the edge). This by itself greatly improves the assigned\n *       rankings by shorting edges.\n *    3. Iteratively find edges that have negative cut values. Generally a\n *       negative cut value indicates that the edge could be removed and a new\n *       tree edge could be added to produce a more compact graph.\n *\n * Much of the algorithms here are derived from Gansner, et al., \"A Technique\n * for Drawing Directed Graphs.\" The structure of the file roughly follows the\n * structure of the overall algorithm.\n */\nfunction networkSimplex(g) {\n  g = simplify(g);\n  longestPath(g);\n  var t = feasibleTree(g);\n  initLowLimValues(t);\n  initCutValues(t, g);\n\n  var e, f;\n  while ((e = leaveEdge(t))) {\n    f = enterEdge(t, g, e);\n    exchangeEdges(t, g, e, f);\n  }\n}\n\n/*\n * Initializes cut values for all edges in the tree.\n */\nfunction initCutValues(t, g) {\n  var vs = alg.postorder(t, t.nodes());\n  vs = vs.slice(0, vs.length - 1);\n  _.forEach(vs, function (v) {\n    assignCutValue(t, g, v);\n  });\n}\n\nfunction assignCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  t.edge(child, parent).cutvalue = calcCutValue(t, g, child);\n}\n\n/*\n * Given the tight tree, its graph, and a child in the graph calculate and\n * return the cut value for the edge between the child and its parent.\n */\nfunction calcCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  // True if the child is on the tail end of the edge in the directed graph\n  var childIsTail = true;\n  // The graph's view of the tree edge we're inspecting\n  var graphEdge = g.edge(child, parent);\n  // The accumulated cut value for the edge between this node and its parent\n  var cutValue = 0;\n\n  if (!graphEdge) {\n    childIsTail = false;\n    graphEdge = g.edge(parent, child);\n  }\n\n  cutValue = graphEdge.weight;\n\n  _.forEach(g.nodeEdges(child), function (e) {\n    var isOutEdge = e.v === child,\n      other = isOutEdge ? e.w : e.v;\n\n    if (other !== parent) {\n      var pointsToHead = isOutEdge === childIsTail,\n        otherWeight = g.edge(e).weight;\n\n      cutValue += pointsToHead ? otherWeight : -otherWeight;\n      if (isTreeEdge(t, child, other)) {\n        var otherCutValue = t.edge(child, other).cutvalue;\n        cutValue += pointsToHead ? -otherCutValue : otherCutValue;\n      }\n    }\n  });\n\n  return cutValue;\n}\n\nfunction initLowLimValues(tree, root) {\n  if (arguments.length < 2) {\n    root = tree.nodes()[0];\n  }\n  dfsAssignLowLim(tree, {}, 1, root);\n}\n\nfunction dfsAssignLowLim(tree, visited, nextLim, v, parent) {\n  var low = nextLim;\n  var label = tree.node(v);\n\n  visited[v] = true;\n  _.forEach(tree.neighbors(v), function (w) {\n    if (!Object.prototype.hasOwnProperty.call(visited, w)) {\n      nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);\n    }\n  });\n\n  label.low = low;\n  label.lim = nextLim++;\n  if (parent) {\n    label.parent = parent;\n  } else {\n    // TODO should be able to remove this when we incrementally update low lim\n    delete label.parent;\n  }\n\n  return nextLim;\n}\n\nfunction leaveEdge(tree) {\n  return _.find(tree.edges(), function (e) {\n    return tree.edge(e).cutvalue < 0;\n  });\n}\n\nfunction enterEdge(t, g, edge) {\n  var v = edge.v;\n  var w = edge.w;\n\n  // For the rest of this function we assume that v is the tail and w is the\n  // head, so if we don't have this edge in the graph we should flip it to\n  // match the correct orientation.\n  if (!g.hasEdge(v, w)) {\n    v = edge.w;\n    w = edge.v;\n  }\n\n  var vLabel = t.node(v);\n  var wLabel = t.node(w);\n  var tailLabel = vLabel;\n  var flip = false;\n\n  // If the root is in the tail of the edge then we need to flip the logic that\n  // checks for the head and tail nodes in the candidates function below.\n  if (vLabel.lim > wLabel.lim) {\n    tailLabel = wLabel;\n    flip = true;\n  }\n\n  var candidates = _.filter(g.edges(), function (edge) {\n    return (\n      flip === isDescendant(t, t.node(edge.v), tailLabel) &&\n      flip !== isDescendant(t, t.node(edge.w), tailLabel)\n    );\n  });\n\n  return _.minBy(candidates, function (edge) {\n    return slack(g, edge);\n  });\n}\n\nfunction exchangeEdges(t, g, e, f) {\n  var v = e.v;\n  var w = e.w;\n  t.removeEdge(v, w);\n  t.setEdge(f.v, f.w, {});\n  initLowLimValues(t);\n  initCutValues(t, g);\n  updateRanks(t, g);\n}\n\nfunction updateRanks(t, g) {\n  var root = _.find(t.nodes(), function (v) {\n    return !g.node(v).parent;\n  });\n  var vs = alg.preorder(t, root);\n  vs = vs.slice(1);\n  _.forEach(vs, function (v) {\n    var parent = t.node(v).parent,\n      edge = g.edge(v, parent),\n      flipped = false;\n\n    if (!edge) {\n      edge = g.edge(parent, v);\n      flipped = true;\n    }\n\n    g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);\n  });\n}\n\n/*\n * Returns true if the edge is in the tree.\n */\nfunction isTreeEdge(tree, u, v) {\n  return tree.hasEdge(u, v);\n}\n\n/*\n * Returns true if the specified node is descendant of the root node per the\n * assigned low and lim attributes in the tree.\n */\nfunction isDescendant(tree, vLabel, rootLabel) {\n  return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;\n}\n", "import { feasibleTree } from './feasible-tree.js';\nimport { networkSimplex } from './network-simplex.js';\nimport { longestPath } from './util.js';\n\nexport { rank };\n\n/*\n * Assigns a rank to each node in the input graph that respects the \"minlen\"\n * constraint specified on edges between nodes.\n *\n * This basic structure is derived from <PERSON><PERSON><PERSON>, et al., \"A Technique for\n * Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a connected DAG\n *    2. Graph nodes must be objects\n *    3. Graph edges must have \"weight\" and \"minlen\" attributes\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have a \"rank\" attribute based on the results of the\n *       algorithm. Ranks can start at any index (including negative), we'll\n *       fix them up later.\n */\nfunction rank(g) {\n  switch (g.graph().ranker) {\n    case 'network-simplex':\n      networkSimplexRanker(g);\n      break;\n    case 'tight-tree':\n      tightTreeRanker(g);\n      break;\n    case 'longest-path':\n      longestPathRanker(g);\n      break;\n    default:\n      networkSimplexRanker(g);\n  }\n}\n\n// A fast and simple ranker, but results are far from optimal.\nvar longestPathRanker = longestPath;\n\nfunction tightTreeRanker(g) {\n  longestPath(g);\n  feasibleTree(g);\n}\n\nfunction networkSimplexRanker(g) {\n  networkSimplex(g);\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, cleanup };\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  var root = util.addDummyNode(g, 'root', {}, '_root');\n  var depths = treeDepths(g);\n  var height = _.max(_.values(depths)) - 1; // Note: depths is an Object not an array\n  var nodeSep = 2 * height + 1;\n\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  _.forEach(g.edges(), function (e) {\n    g.edge(e).minlen *= nodeSep;\n  });\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  var weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  _.forEach(g.children(), function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n  });\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\n\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  var children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n    }\n    return;\n  }\n\n  var top = util.addBorderNode(g, '_bt');\n  var bottom = util.addBorderNode(g, '_bb');\n  var label = g.node(v);\n\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n\n  _.forEach(children, function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n\n    var childNode = g.node(child);\n    var childTop = childNode.borderTop ? childNode.borderTop : child;\n    var childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    var thisWeight = childNode.borderTop ? weight : 2 * weight;\n    var minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n  });\n\n  if (!g.parent(v)) {\n    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });\n  }\n}\n\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      _.forEach(children, function (child) {\n        dfs(child, depth + 1);\n      });\n    }\n    depths[v] = depth;\n  }\n  _.forEach(g.children(), function (v) {\n    dfs(v, 1);\n  });\n  return depths;\n}\n\nfunction sumWeights(g) {\n  return _.reduce(\n    g.edges(),\n    function (acc, e) {\n      return acc + g.edge(e).weight;\n    },\n    0,\n  );\n}\n\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { addSubgraphConstraints };\n\nfunction addSubgraphConstraints(g, cg, vs) {\n  var prev = {},\n    rootPrev;\n\n  _.forEach(vs, function (v) {\n    var child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      _.each(children, function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.reduce(_.sortBy(subgraphs, \"order\"), function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\n\nexport { buildLayerGraph };\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  var root = createRootNode(g),\n    result = new Graph({ compound: true })\n      .setGraph({ root: root })\n      .setDefaultNodeLabel(function (v) {\n        return g.node(v);\n      });\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v),\n      parent = g.parent(v);\n\n    if (node.rank === rank || (node.minRank <= rank && rank <= node.maxRank)) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      _.forEach(g[relationship](v), function (e) {\n        var u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = !_.isUndefined(edge) ? edge.weight : 0;\n        result.setEdge(u, v, { weight: g.edge(e).weight + weight });\n      });\n\n      if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank],\n        });\n      }\n    }\n  });\n\n  return result;\n}\n\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode((v = _.uniqueId('_root'))));\n  return v;\n}\n", "import * as _ from 'lodash-es';\n\nexport { crossCount };\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(\n    southLayer,\n    _.map(southLayer, function (v, i) {\n      return i;\n    }),\n  );\n  var southEntries = _.flatten(\n    _.map(northLayer, function (v) {\n      return _.sortBy(\n        _.map(g.outEdges(v), function (e) {\n          return { pos: southPos[e.w], weight: g.edge(e).weight };\n        }),\n        'pos',\n      );\n    }),\n  );\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function () {\n    return 0;\n  });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(\n    // @ts-expect-error\n    southEntries.forEach(function (entry) {\n      var index = entry.pos + firstIndex;\n      tree[index] += entry.weight;\n      var weightSum = 0;\n      // @ts-expect-error\n      while (index > 0) {\n        // @ts-expect-error\n        if (index % 2) {\n          weightSum += tree[index + 1];\n        }\n        // @ts-expect-error\n        index = (index - 1) >> 1;\n        tree[index] += entry.weight;\n      }\n      cc += entry.weight * weightSum;\n    }),\n  );\n\n  return cc;\n}\n", "import * as _ from 'lodash-es';\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nexport function initOrder(g) {\n  var visited = {};\n  var simpleNodes = _.filter(g.nodes(), function (v) {\n    return !g.children(v).length;\n  });\n  var maxRank = _.max(\n    _.map(simpleNodes, function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  var layers = _.map(_.range(maxRank + 1), function () {\n    return [];\n  });\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    var node = g.node(v);\n    layers[node.rank].push(v);\n    _.forEach(g.successors(v), dfs);\n  }\n\n  var orderedVs = _.sortBy(simpleNodes, function (v) {\n    return g.node(v).rank;\n  });\n  _.forEach(orderedVs, dfs);\n\n  return layers;\n}\n", "import * as _ from 'lodash-es';\n\nexport { barycenter };\n\nfunction barycenter(g, movable) {\n  return _.map(movable, function (v) {\n    var inV = g.inEdges(v);\n    if (!inV.length) {\n      return { v: v };\n    } else {\n      var result = _.reduce(\n        inV,\n        function (acc, e) {\n          var edge = g.edge(e),\n            nodeU = g.node(e.v);\n          return {\n            sum: acc.sum + edge.weight * nodeU.order,\n            weight: acc.weight + edge.weight,\n          };\n        },\n        { sum: 0, weight: 0 },\n      );\n\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight,\n      };\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { resolveConflicts };\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function (entry, i) {\n    var tmp = (mappedEntries[entry.v] = {\n      indegree: 0,\n      in: [],\n      out: [],\n      vs: [entry.v],\n      i: i,\n    });\n    if (!_.isUndefined(entry.barycenter)) {\n      // @ts-expect-error\n      tmp.barycenter = entry.barycenter;\n      // @ts-expect-error\n      tmp.weight = entry.weight;\n    }\n  });\n\n  _.forEach(cg.edges(), function (e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  var sourceSet = _.filter(mappedEntries, function (entry) {\n    // @ts-expect-error\n    return !entry.indegree;\n  });\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n\n  function handleIn(vEntry) {\n    return function (uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (\n        _.isUndefined(uEntry.barycenter) ||\n        _.isUndefined(vEntry.barycenter) ||\n        uEntry.barycenter >= vEntry.barycenter\n      ) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return function (wEntry) {\n      wEntry['in'].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry['in'].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n\n  return _.map(\n    _.filter(entries, function (entry) {\n      return !entry.merged;\n    }),\n    function (entry) {\n      return _.pick(entry, ['vs', 'i', 'barycenter', 'weight']);\n    },\n  );\n}\n\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\n\nexport { sort };\n\nfunction sort(entries, biasRight) {\n  var parts = util.partition(entries, function (entry) {\n    return Object.prototype.hasOwnProperty.call(entry, 'barycenter');\n  });\n  var sortable = parts.lhs,\n    unsortable = _.sortBy(parts.rhs, function (entry) {\n      return -entry.i;\n    }),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n\n  sortable.sort(compareWithBias(!!biasRight));\n\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n\n  _.forEach(sortable, function (entry) {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n\n  var result = { vs: _.flatten(vs) };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\n\nfunction consumeUnsortable(vs, unsortable, index) {\n  var last;\n  while (unsortable.length && (last = _.last(unsortable)).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\n\nfunction compareWithBias(bias) {\n  return function (entryV, entryW) {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}\n", "import * as _ from 'lodash-es';\nimport { barycenter } from './barycenter.js';\nimport { resolveConflicts } from './resolve-conflicts.js';\nimport { sort } from './sort.js';\n\nexport { sortSubgraph };\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight : undefined;\n  var subgraphs = {};\n\n  if (bl) {\n    movable = _.filter(movable, function (w) {\n      return w !== bl && w !== br;\n    });\n  }\n\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function (entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (Object.prototype.hasOwnProperty.call(subgraphResult, 'barycenter')) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  var result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br]);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!Object.prototype.hasOwnProperty.call(result, 'barycenter')) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter =\n        (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function (entry) {\n    entry.vs = _.flatten(\n      entry.vs.map(function (v) {\n        if (subgraphs[v]) {\n          return subgraphs[v].vs;\n        }\n        return v;\n      }),\n    );\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter =\n      (target.barycenter * target.weight + other.barycenter * other.weight) /\n      (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\nimport { addSubgraphConstraints } from './add-subgraph-constraints.js';\nimport { buildLayerGraph } from './build-layer-graph.js';\nimport { crossCount } from './cross-count.js';\nimport { initOrder } from './init-order.js';\nimport { sortSubgraph } from './sort-subgraph.js';\n\nexport { order };\n\n/*\n * Applies heuristics to minimize edge crossings in the graph and sets the best\n * order solution as an order attribute on each node.\n *\n * Pre-conditions:\n *\n *    1. Graph must be DAG\n *    2. Graph nodes must be objects with a \"rank\" attribute\n *    3. Graph edges must have the \"weight\" attribute\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have an \"order\" attribute based on the results of the\n *       algorithm.\n */\nfunction order(g) {\n  var maxRank = util.maxRank(g),\n    downLayerGraphs = buildLayerGraphs(g, _.range(1, maxRank + 1), 'inEdges'),\n    upLayerGraphs = buildLayerGraphs(g, _.range(maxRank - 1, -1, -1), 'outEdges');\n\n  var layering = initOrder(g);\n  assignOrder(g, layering);\n\n  var bestCC = Number.POSITIVE_INFINITY,\n    best;\n\n  for (var i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);\n\n    layering = util.buildLayerMatrix(g);\n    var cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = _.cloneDeep(layering);\n      bestCC = cc;\n    }\n  }\n\n  assignOrder(g, best);\n}\n\nfunction buildLayerGraphs(g, ranks, relationship) {\n  return _.map(ranks, function (rank) {\n    return buildLayerGraph(g, rank, relationship);\n  });\n}\n\nfunction sweepLayerGraphs(layerGraphs, biasRight) {\n  var cg = new Graph();\n  _.forEach(layerGraphs, function (lg) {\n    var root = lg.graph().root;\n    var sorted = sortSubgraph(lg, root, cg, biasRight);\n    _.forEach(sorted.vs, function (v, i) {\n      lg.node(v).order = i;\n    });\n    addSubgraphConstraints(lg, cg, sorted.vs);\n  });\n}\n\nfunction assignOrder(g, layering) {\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, i) {\n      g.node(v).order = i;\n    });\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { parentDummyChains };\n\nfunction parentDummyChains(g) {\n  var postorderNums = postorder(g);\n\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var edgeObj = node.edgeObj;\n    var pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    var path = pathData.path;\n    var lca = pathData.lca;\n    var pathIdx = 0;\n    var pathV = path[pathIdx];\n    var ascending = true;\n\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca && g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n\n      if (!ascending) {\n        while (\n          pathIdx < path.length - 1 &&\n          g.node((pathV = path[pathIdx + 1])).minRank <= node.rank\n        ) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  var vPath = [];\n  var wPath = [];\n  var low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  var lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  var parent;\n  var lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent && (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n\n  return { path: vPath.concat(wPath.reverse()), lca: lca };\n}\n\nfunction postorder(g) {\n  var result = {};\n  var lim = 0;\n\n  function dfs(v) {\n    var low = lim;\n    _.forEach(g.children(v), dfs);\n    result[v] = { low: low, lim: lim++ };\n  }\n  _.forEach(g.children(), dfs);\n\n  return result;\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nexport {\n  positionX,\n  findType1Conflicts,\n  findType2Conflicts,\n  addConflict,\n  hasConflict,\n  verticalAlignment,\n  horizontalCompaction,\n  alignCoordinates,\n  findSmallestWidthAlignment,\n  balance,\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  var conflicts = {};\n\n  function visitLayer(prevLayer, layer) {\n    var // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = _.last(layer);\n\n    _.forEach(layer, function (v, i) {\n      var w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n\n      if (w || v === lastNode) {\n        _.forEach(layer.slice(scanPos, i + 1), function (scanNode) {\n          _.forEach(g.predecessors(scanNode), function (u) {\n            var uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) && !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        // @ts-expect-error\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n\n    return layer;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findType2Conflicts(g, layering) {\n  var conflicts = {};\n\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var v;\n    _.forEach(_.range(southPos, southEnd), function (i) {\n      v = south[i];\n      if (g.node(v).dummy) {\n        _.forEach(g.predecessors(v), function (u) {\n          var uNode = g.node(u);\n          if (uNode.dummy && (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n\n  function visitLayer(north, south) {\n    var prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n\n    _.forEach(south, function (v, southLookahead) {\n      if (g.node(v).dummy === 'border') {\n        var predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          // @ts-expect-error\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n\n    return south;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return _.find(g.predecessors(v), function (u) {\n      return g.node(u).dummy;\n    });\n  }\n}\n\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n\n  var conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\n\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return !!conflicts[v] && Object.prototype.hasOwnProperty.call(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  var root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, order) {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n\n  _.forEach(layering, function (layer) {\n    var prevIdx = -1;\n    _.forEach(layer, function (v) {\n      var ws = neighborFn(v);\n      if (ws.length) {\n        ws = _.sortBy(ws, function (w) {\n          return pos[w];\n        });\n        var mp = (ws.length - 1) / 2;\n        for (var i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          var w = ws[i];\n          if (align[v] === v && prevIdx < pos[w] && !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n\n  return { root: root, align: align };\n}\n\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  var xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? 'borderLeft' : 'borderRight';\n\n  function iterate(setXsFunc, nextNodesFunc) {\n    var stack = blockG.nodes();\n    var elem = stack.pop();\n    var visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce(function (acc, e) {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    var min = blockG.outEdges(elem).reduce(function (acc, e) {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n\n    var node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  _.forEach(align, function (v) {\n    xs[v] = xs[root[v]];\n  });\n\n  return xs;\n}\n\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  var blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n\n  _.forEach(layering, function (layer) {\n    var u;\n    _.forEach(layer, function (v) {\n      var vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return _.minBy(_.values(xss), function (xs) {\n    var max = Number.NEGATIVE_INFINITY;\n    var min = Number.POSITIVE_INFINITY;\n\n    _.forIn(xs, function (x, v) {\n      var halfWidth = width(g, v) / 2;\n\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n\n    return max - min;\n  });\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  var alignToVals = _.values(alignTo),\n    alignToMin = _.min(alignToVals),\n    alignToMax = _.max(alignToVals);\n\n  _.forEach(['u', 'd'], function (vert) {\n    _.forEach(['l', 'r'], function (horiz) {\n      var alignment = vert + horiz,\n        xs = xss[alignment],\n        delta;\n      if (xs === alignTo) return;\n\n      var xsVals = _.values(xs);\n      delta = horiz === 'l' ? alignToMin - _.min(xsVals) : alignToMax - _.max(xsVals);\n\n      if (delta) {\n        xss[alignment] = _.mapValues(xs, function (x) {\n          return x + delta;\n        });\n      }\n    });\n  });\n}\n\nfunction balance(xss, align) {\n  return _.mapValues(xss.ul, function (ignore, v) {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      var xs = _.sortBy(_.map(xss, v));\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\n\nfunction positionX(g) {\n  var layering = util.buildLayerMatrix(g);\n  var conflicts = _.merge(findType1Conflicts(g, layering), findType2Conflicts(g, layering));\n\n  var xss = {};\n  var adjustedLayering;\n  _.forEach(['u', 'd'], function (vert) {\n    adjustedLayering = vert === 'u' ? layering : _.values(layering).reverse();\n    _.forEach(['l', 'r'], function (horiz) {\n      if (horiz === 'r') {\n        adjustedLayering = _.map(adjustedLayering, function (inner) {\n          return _.values(inner).reverse();\n        });\n      }\n\n      var neighborFn = (vert === 'u' ? g.predecessors : g.successors).bind(g);\n      var align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      var xs = horizontalCompaction(g, adjustedLayering, align.root, align.align, horiz === 'r');\n      if (horiz === 'r') {\n        xs = _.mapValues(xs, function (x) {\n          return -x;\n        });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n\n  var smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\n\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return function (g, v, w) {\n    var vLabel = g.node(v);\n    var wLabel = g.node(w);\n    var sum = 0;\n    var delta;\n\n    sum += vLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(vLabel, 'labelpos')) {\n      switch (vLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = -vLabel.width / 2;\n          break;\n        case 'r':\n          delta = vLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n\n    sum += wLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(wLabel, 'labelpos')) {\n      switch (wLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = wLabel.width / 2;\n          break;\n        case 'r':\n          delta = -wLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    return sum;\n  };\n}\n\nfunction width(g, v) {\n  return g.node(v).width;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\nimport { positionX } from './bk.js';\n\nexport { position };\n\nfunction position(g) {\n  g = util.asNonCompoundGraph(g);\n\n  positionY(g);\n  _.forOwn(positionX(g), function (x, v) {\n    g.node(v).x = x;\n  });\n}\n\nfunction positionY(g) {\n  var layering = util.buildLayerMatrix(g);\n  var rankSep = g.graph().ranksep;\n  var prevY = 0;\n  _.forEach(layering, function (layer) {\n    var maxHeight = _.max(\n      _.map(layer, function (v) {\n        return g.node(v).height;\n      }),\n    );\n    _.forEach(layer, function (v) {\n      g.node(v).y = prevY + maxHeight / 2;\n    });\n    prevY += maxHeight + rankSep;\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { addBorderSegments } from './add-border-segments.js';\nimport * as coordinateSystem from './coordinate-system.js';\nimport * as acyclic from './acyclic.js';\nimport * as normalize from './normalize.js';\nimport { rank } from './rank/index.js';\nimport * as nestingGraph from './nesting-graph.js';\nimport { order } from './order/index.js';\nimport { parentDummyChains } from './parent-dummy-chains.js';\nimport { position } from './position/index.js';\nimport * as util from './util.js';\n\nexport { layout };\n\nfunction layout(g, opts) {\n  var time = opts && opts.debugTiming ? util.time : util.notime;\n  time('layout', () => {\n    var layoutGraph = time('  buildLayoutGraph', () => buildLayoutGraph(g));\n    time('  runLayout', () => runLayout(layoutGraph, time));\n    time('  updateInputGraph', () => updateInputGraph(g, layoutGraph));\n  });\n}\n\nfunction runLayout(g, time) {\n  time('    makeSpaceForEdgeLabels', () => makeSpaceForEdgeLabels(g));\n  time('    removeSelfEdges', () => removeSelfEdges(g));\n  time('    acyclic', () => acyclic.run(g));\n  time('    nestingGraph.run', () => nestingGraph.run(g));\n  time('    rank', () => rank(util.asNonCompoundGraph(g)));\n  time('    injectEdgeLabelProxies', () => injectEdgeLabelProxies(g));\n  time('    removeEmptyRanks', () => util.removeEmptyRanks(g));\n  time('    nestingGraph.cleanup', () => nestingGraph.cleanup(g));\n  time('    normalizeRanks', () => util.normalizeRanks(g));\n  time('    assignRankMinMax', () => assignRankMinMax(g));\n  time('    removeEdgeLabelProxies', () => removeEdgeLabelProxies(g));\n  time('    normalize.run', () => normalize.run(g));\n  time('    parentDummyChains', () => parentDummyChains(g));\n  time('    addBorderSegments', () => addBorderSegments(g));\n  time('    order', () => order(g));\n  time('    insertSelfEdges', () => insertSelfEdges(g));\n  time('    adjustCoordinateSystem', () => coordinateSystem.adjust(g));\n  time('    position', () => position(g));\n  time('    positionSelfEdges', () => positionSelfEdges(g));\n  time('    removeBorderNodes', () => removeBorderNodes(g));\n  time('    normalize.undo', () => normalize.undo(g));\n  time('    fixupEdgeLabelCoords', () => fixupEdgeLabelCoords(g));\n  time('    undoCoordinateSystem', () => coordinateSystem.undo(g));\n  time('    translateGraph', () => translateGraph(g));\n  time('    assignNodeIntersects', () => assignNodeIntersects(g));\n  time('    reversePoints', () => reversePointsForReversedEdges(g));\n  time('    acyclic.undo', () => acyclic.undo(g));\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  _.forEach(inputGraph.nodes(), function (v) {\n    var inputLabel = inputGraph.node(v);\n    var layoutLabel = layoutGraph.node(v);\n\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var inputLabel = inputGraph.edge(e);\n    var layoutLabel = layoutGraph.edge(e);\n\n    inputLabel.points = layoutLabel.points;\n    if (Object.prototype.hasOwnProperty.call(layoutLabel, 'x')) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\n\nvar graphNumAttrs = ['nodesep', 'edgesep', 'ranksep', 'marginx', 'marginy'];\nvar graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: 'tb' };\nvar graphAttrs = ['acyclicer', 'ranker', 'rankdir', 'align'];\nvar nodeNumAttrs = ['width', 'height'];\nvar nodeDefaults = { width: 0, height: 0 };\nvar edgeNumAttrs = ['minlen', 'weight', 'width', 'height', 'labeloffset'];\nvar edgeDefaults = {\n  minlen: 1,\n  weight: 1,\n  width: 0,\n  height: 0,\n  labeloffset: 10,\n  labelpos: 'r',\n};\nvar edgeAttrs = ['labelpos'];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  var g = new Graph({ multigraph: true, compound: true });\n  var graph = canonicalize(inputGraph.graph());\n\n  g.setGraph(\n    _.merge({}, graphDefaults, selectNumberAttrs(graph, graphNumAttrs), _.pick(graph, graphAttrs)),\n  );\n\n  _.forEach(inputGraph.nodes(), function (v) {\n    var node = canonicalize(inputGraph.node(v));\n    g.setNode(v, _.defaults(selectNumberAttrs(node, nodeNumAttrs), nodeDefaults));\n    g.setParent(v, inputGraph.parent(v));\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(\n      e,\n      _.merge({}, edgeDefaults, selectNumberAttrs(edge, edgeNumAttrs), _.pick(edge, edgeAttrs)),\n    );\n  });\n\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  var graph = g.graph();\n  graph.ranksep /= 2;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== 'c') {\n      if (graph.rankdir === 'TB' || graph.rankdir === 'BT') {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.width && edge.height) {\n      var v = g.node(e.v);\n      var w = g.node(e.w);\n      var label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };\n      util.addDummyNode(g, 'edge-proxy', label, '_ep');\n    }\n  });\n}\n\nfunction assignRankMinMax(g) {\n  var maxRank = 0;\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      // @ts-expect-error\n      maxRank = _.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\n\nfunction removeEdgeLabelProxies(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'edge-proxy') {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction translateGraph(g) {\n  var minX = Number.POSITIVE_INFINITY;\n  var maxX = 0;\n  var minY = Number.POSITIVE_INFINITY;\n  var maxY = 0;\n  var graphLabel = g.graph();\n  var marginX = graphLabel.marginx || 0;\n  var marginY = graphLabel.marginy || 0;\n\n  function getExtremes(attrs) {\n    var x = attrs.x;\n    var y = attrs.y;\n    var w = attrs.width;\n    var h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n\n  _.forEach(g.nodes(), function (v) {\n    getExtremes(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      getExtremes(edge);\n    }\n  });\n\n  minX -= marginX;\n  minY -= marginY;\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, function (p) {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      edge.x -= minX;\n    }\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      edge.y -= minY;\n    }\n  });\n\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\n\nfunction assignNodeIntersects(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    var nodeV = g.node(e.v);\n    var nodeW = g.node(e.w);\n    var p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\n\nfunction fixupEdgeLabelCoords(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      if (edge.labelpos === 'l' || edge.labelpos === 'r') {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n        case 'l':\n          edge.x -= edge.width / 2 + edge.labeloffset;\n          break;\n        case 'r':\n          edge.x += edge.width / 2 + edge.labeloffset;\n          break;\n      }\n    }\n  });\n}\n\nfunction reversePointsForReversedEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\n\nfunction removeBorderNodes(g) {\n  _.forEach(g.nodes(), function (v) {\n    if (g.children(v).length) {\n      var node = g.node(v);\n      var t = g.node(node.borderTop);\n      var b = g.node(node.borderBottom);\n      var l = g.node(_.last(node.borderLeft));\n      var r = g.node(_.last(node.borderRight));\n\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n\n  _.forEach(g.nodes(), function (v) {\n    if (g.node(v).dummy === 'border') {\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction removeSelfEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({ e: e, label: g.edge(e) });\n      g.removeEdge(e);\n    }\n  });\n}\n\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  _.forEach(layers, function (layer) {\n    var orderShift = 0;\n    _.forEach(layer, function (v, i) {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      _.forEach(node.selfEdges, function (selfEdge) {\n        util.addDummyNode(\n          g,\n          'selfedge',\n          {\n            width: selfEdge.label.width,\n            height: selfEdge.label.height,\n            rank: node.rank,\n            order: i + ++orderShift,\n            e: selfEdge.e,\n            label: selfEdge.label,\n          },\n          '_se',\n        );\n      });\n      delete node.selfEdges;\n    });\n  });\n}\n\nfunction positionSelfEdges(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'selfedge') {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [\n        { x: x + (2 * dx) / 3, y: y - dy },\n        { x: x + (5 * dx) / 6, y: y - dy },\n        { x: x + dx, y: y },\n        { x: x + (5 * dx) / 6, y: y + dy },\n        { x: x + (2 * dx) / 3, y: y + dy },\n      ];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\n\nfunction selectNumberAttrs(obj, attrs) {\n  return _.mapValues(_.pick(obj, attrs), Number);\n}\n\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  _.forEach(attrs, function (v, k) {\n    newAttrs[k.toLowerCase()] = v;\n  });\n  return newAttrs;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,SAAS,aAAa,GAAG,MAAM,OAAO,MAAM;AAC1C,MAAI;AACJ,KAAG;AACD,QAAM,iBAAS,IAAI;AAAA,EACrB,SAAS,EAAE,QAAQ,CAAC;AAEpB,QAAM,QAAQ;AACd,IAAE,QAAQ,GAAG,KAAK;AAClB,SAAO;AACT;AATS;AAeT,SAAS,SAAS,GAAG;AACnB,MAAI,aAAa,IAAI,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC;AAC/C,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,eAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;AAAA,EACjC,CAAC;AACD,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,cAAc,WAAW,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,GAAG,QAAQ,EAAE;AACtE,QAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,eAAW,QAAQ,EAAE,GAAG,EAAE,GAAG;AAAA,MAC3B,QAAQ,YAAY,SAAS,MAAM;AAAA,MACnC,QAAQ,KAAK,IAAI,YAAY,QAAQ,MAAM,MAAM;AAAA,IACnD,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAdS;AAgBT,SAAS,mBAAmB,GAAG;AAC7B,MAAI,aAAa,IAAI,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC;AAC/E,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ;AACzB,iBAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;AAAA,IACjC;AAAA,EACF,CAAC;AACD,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,eAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;AAAA,EACjC,CAAC;AACD,SAAO;AACT;AAXS;AAuCT,SAAS,cAAc,MAAM,OAAO;AAClC,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AAIb,MAAI,KAAK,MAAM,IAAI;AACnB,MAAI,KAAK,MAAM,IAAI;AACnB,MAAI,IAAI,KAAK,QAAQ;AACrB,MAAI,IAAI,KAAK,SAAS;AAEtB,MAAI,CAAC,MAAM,CAAC,IAAI;AACd,UAAM,IAAI,MAAM,2DAA2D;AAAA,EAC7E;AAEA,MAAI,IAAI;AACR,MAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,GAAG;AAEvC,QAAI,KAAK,GAAG;AACV,UAAI,CAAC;AAAA,IACP;AACA,SAAM,IAAI,KAAM;AAChB,SAAK;AAAA,EACP,OAAO;AAEL,QAAI,KAAK,GAAG;AACV,UAAI,CAAC;AAAA,IACP;AACA,SAAK;AACL,SAAM,IAAI,KAAM;AAAA,EAClB;AAEA,SAAO,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG;AAChC;AAjCS;AAuCT,SAAS,iBAAiB,GAAG;AAC3B,MAAI,WAAa,YAAM,cAAM,QAAQ,CAAC,IAAI,CAAC,GAAG,WAAY;AACxD,WAAO,CAAC;AAAA,EACV,CAAC;AACD,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAIA,QAAO,KAAK;AAChB,QAAI,CAAG,oBAAYA,KAAI,GAAG;AACxB,eAASA,KAAI,EAAE,KAAK,KAAK,IAAI;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAZS;AAkBT,SAAS,eAAe,GAAG;AACzB,MAAI,MAAQ;AAAA,IACR,YAAI,EAAE,MAAM,GAAG,SAAU,GAAG;AAC5B,aAAO,EAAE,KAAK,CAAC,EAAE;AAAA,IACnB,CAAC;AAAA,EACH;AACA,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAM,YAAI,MAAM,MAAM,GAAG;AACvB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAZS;AAcT,SAAS,iBAAiB,GAAG;AAE3B,MAAI,SAAW;AAAA,IACX,YAAI,EAAE,MAAM,GAAG,SAAU,GAAG;AAC5B,aAAO,EAAE,KAAK,CAAC,EAAE;AAAA,IACnB,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,CAAC;AACd,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAIA,QAAO,EAAE,KAAK,CAAC,EAAE,OAAO;AAC5B,QAAI,CAAC,OAAOA,KAAI,GAAG;AACjB,aAAOA,KAAI,IAAI,CAAC;AAAA,IAClB;AACA,WAAOA,KAAI,EAAE,KAAK,CAAC;AAAA,EACrB,CAAC;AAED,MAAI,QAAQ;AACZ,MAAI,iBAAiB,EAAE,MAAM,EAAE;AAC/B,EAAE,gBAAQ,QAAQ,SAAU,IAAI,GAAG;AACjC,QAAM,oBAAY,EAAE,KAAK,IAAI,mBAAmB,GAAG;AACjD,QAAE;AAAA,IACJ,WAAW,OAAO;AAChB,MAAE,gBAAQ,IAAI,SAAU,GAAG;AACzB,UAAE,KAAK,CAAC,EAAE,QAAQ;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AA5BS;AA8BT,SAAS,cAAc,GAAG,QAAQA,OAAMC,QAAO;AAC7C,MAAI,OAAO;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACA,MAAI,UAAU,UAAU,GAAG;AACzB,SAAK,OAAOD;AACZ,SAAK,QAAQC;AAAA,EACf;AACA,SAAO,aAAa,GAAG,UAAU,MAAM,MAAM;AAC/C;AAVS;AAYT,SAAS,QAAQ,GAAG;AAClB,SAAS;AAAA,IACL,YAAI,EAAE,MAAM,GAAG,SAAU,GAAG;AAC5B,UAAID,QAAO,EAAE,KAAK,CAAC,EAAE;AACrB,UAAI,CAAG,oBAAYA,KAAI,GAAG;AACxB,eAAOA;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACF;AATS;AAgBT,SAAS,UAAU,YAAY,IAAI;AACjC,MAAI,SAAS,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE;AAChC,EAAE,gBAAQ,YAAY,SAAU,OAAO;AACrC,QAAI,GAAG,KAAK,GAAG;AACb,aAAO,IAAI,KAAK,KAAK;AAAA,IACvB,OAAO;AACL,aAAO,IAAI,KAAK,KAAK;AAAA,IACvB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAVS;AAgBT,SAAS,KAAK,MAAM,IAAI;AACtB,MAAI,QAAU,YAAI;AAClB,MAAI;AACF,WAAO,GAAG;AAAA,EACZ,UAAE;AACA,YAAQ,IAAI,OAAO,aAAe,YAAI,IAAI,SAAS,IAAI;AAAA,EACzD;AACF;AAPS;AAST,SAAS,OAAO,MAAM,IAAI;AACxB,SAAO,GAAG;AACZ;AAFS;;;AClPT,SAAS,kBAAkB,GAAG;AAC5B,WAASE,KAAI,GAAG;AACd,QAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,SAAS,QAAQ;AACnB,MAAE,gBAAQ,UAAUA,IAAG;AAAA,IACzB;AAEA,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,SAAS,GAAG;AACzD,WAAK,aAAa,CAAC;AACnB,WAAK,cAAc,CAAC;AACpB,eAASC,QAAO,KAAK,SAASC,WAAU,KAAK,UAAU,GAAGD,QAAOC,UAAS,EAAED,OAAM;AAChF,QAAAE,eAAc,GAAG,cAAc,OAAO,GAAG,MAAMF,KAAI;AACnD,QAAAE,eAAc,GAAG,eAAe,OAAO,GAAG,MAAMF,KAAI;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AAfS,SAAAD,MAAA;AAiBT,EAAE,gBAAQ,EAAE,SAAS,GAAGA,IAAG;AAC7B;AAnBS;AAqBT,SAASG,eAAc,GAAG,MAAM,QAAQ,IAAI,QAAQF,OAAM;AACxD,MAAI,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,MAAMA,OAAM,YAAY,KAAK;AAChE,MAAI,OAAO,OAAO,IAAI,EAAEA,QAAO,CAAC;AAChC,MAAI,OAAY,aAAa,GAAG,UAAU,OAAO,MAAM;AACvD,SAAO,IAAI,EAAEA,KAAI,IAAI;AACrB,IAAE,UAAU,MAAM,EAAE;AACpB,MAAI,MAAM;AACR,MAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,EAAE,CAAC;AAAA,EACrC;AACF;AATS,OAAAE,gBAAA;;;ACtBT,SAAS,OAAO,GAAG;AACjB,MAAI,UAAU,EAAE,MAAM,EAAE,QAAQ,YAAY;AAC5C,MAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,oBAAgB,CAAC;AAAA,EACnB;AACF;AALS;AAOT,SAAS,KAAK,GAAG;AACf,MAAI,UAAU,EAAE,MAAM,EAAE,QAAQ,YAAY;AAC5C,MAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,aAAS,CAAC;AAAA,EACZ;AAEA,MAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,WAAO,CAAC;AACR,oBAAgB,CAAC;AAAA,EACnB;AACF;AAVS;AAYT,SAAS,gBAAgB,GAAG;AAC1B,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,uBAAmB,EAAE,KAAK,CAAC,CAAC;AAAA,EAC9B,CAAC;AACD,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,uBAAmB,EAAE,KAAK,CAAC,CAAC;AAAA,EAC9B,CAAC;AACH;AAPS;AAST,SAAS,mBAAmB,OAAO;AACjC,MAAI,IAAI,MAAM;AACd,QAAM,QAAQ,MAAM;AACpB,QAAM,SAAS;AACjB;AAJS;AAMT,SAAS,SAAS,GAAG;AACnB,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,gBAAY,EAAE,KAAK,CAAC,CAAC;AAAA,EACvB,CAAC;AAED,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,IAAE,gBAAQ,KAAK,QAAQ,WAAW;AAClC,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,kBAAY,IAAI;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AAZS;AAcT,SAAS,YAAY,OAAO;AAC1B,QAAM,IAAI,CAAC,MAAM;AACnB;AAFS;AAIT,SAAS,OAAO,GAAG;AACjB,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,cAAU,EAAE,KAAK,CAAC,CAAC;AAAA,EACrB,CAAC;AAED,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,IAAE,gBAAQ,KAAK,QAAQ,SAAS;AAChC,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,gBAAU,IAAI;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AAZS;AAcT,SAAS,UAAU,OAAO;AACxB,MAAI,IAAI,MAAM;AACd,QAAM,IAAI,MAAM;AAChB,QAAM,IAAI;AACZ;AAJS;;;AC/DT,IAAM,OAAN,MAAW;AAAA,EAPX,OAOW;AAAA;AAAA;AAAA,EACT,cAAc;AACZ,QAAI,WAAW,CAAC;AAChB,aAAS,QAAQ,SAAS,QAAQ;AAClC,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,UAAU;AACR,QAAI,WAAW,KAAK;AACpB,QAAI,QAAQ,SAAS;AACrB,QAAI,UAAU,UAAU;AACtB,aAAO,KAAK;AACZ,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,WAAW,KAAK;AACpB,QAAI,MAAM,SAAS,MAAM,OAAO;AAC9B,aAAO,KAAK;AAAA,IACd;AACA,UAAM,QAAQ,SAAS;AACvB,aAAS,MAAM,QAAQ;AACvB,aAAS,QAAQ;AACjB,UAAM,QAAQ;AAAA,EAChB;AAAA,EACA,WAAW;AACT,QAAI,OAAO,CAAC;AACZ,QAAI,WAAW,KAAK;AACpB,QAAI,OAAO,SAAS;AACpB,WAAO,SAAS,UAAU;AACxB,WAAK,KAAK,KAAK,UAAU,MAAM,cAAc,CAAC;AAC9C,aAAO,KAAK;AAAA,IACd;AACA,WAAO,MAAM,KAAK,KAAK,IAAI,IAAI;AAAA,EACjC;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,QAAM,MAAM,QAAQ,MAAM;AAC1B,QAAM,MAAM,QAAQ,MAAM;AAC1B,SAAO,MAAM;AACb,SAAO,MAAM;AACf;AALS;AAOT,SAAS,eAAe,GAAG,GAAG;AAC5B,MAAI,MAAM,WAAW,MAAM,SAAS;AAClC,WAAO;AAAA,EACT;AACF;AAJS;;;ACrCT,IAAI,oBAAsB,iBAAS,CAAC;AAEpC,SAAS,UAAU,GAAG,UAAU;AAC9B,MAAI,EAAE,UAAU,KAAK,GAAG;AACtB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,QAAQ,WAAW,GAAG,YAAY,iBAAiB;AACvD,MAAI,UAAU,YAAY,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO;AAGnE,SAAS;AAAA,IACL,YAAI,SAAS,SAAU,GAAG;AAC1B,aAAO,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC;AAAA,IAC5B,CAAC;AAAA,EACH;AACF;AAbS;AAeT,SAAS,YAAY,GAAG,SAAS,SAAS;AACxC,MAAI,UAAU,CAAC;AACf,MAAI,UAAU,QAAQ,QAAQ,SAAS,CAAC;AACxC,MAAI,QAAQ,QAAQ,CAAC;AAErB,MAAI;AACJ,SAAO,EAAE,UAAU,GAAG;AACpB,WAAQ,QAAQ,MAAM,QAAQ,GAAI;AAChC,iBAAW,GAAG,SAAS,SAAS,KAAK;AAAA,IACvC;AACA,WAAQ,QAAQ,QAAQ,QAAQ,GAAI;AAClC,iBAAW,GAAG,SAAS,SAAS,KAAK;AAAA,IACvC;AACA,QAAI,EAAE,UAAU,GAAG;AACjB,eAAS,IAAI,QAAQ,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3C,gBAAQ,QAAQ,CAAC,EAAE,QAAQ;AAC3B,YAAI,OAAO;AACT,oBAAU,QAAQ,OAAO,WAAW,GAAG,SAAS,SAAS,OAAO,IAAI,CAAC;AACrE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAzBS;AA2BT,SAAS,WAAW,GAAG,SAAS,SAAS,OAAO,qBAAqB;AACnE,MAAI,UAAU,sBAAsB,CAAC,IAAI;AAEzC,EAAE,gBAAQ,EAAE,QAAQ,MAAM,CAAC,GAAG,SAAU,MAAM;AAC5C,QAAI,SAAS,EAAE,KAAK,IAAI;AACxB,QAAI,SAAS,EAAE,KAAK,KAAK,CAAC;AAE1B,QAAI,qBAAqB;AACvB,cAAQ,KAAK,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAE,CAAC;AAAA,IACvC;AAEA,WAAO,OAAO;AACd,iBAAa,SAAS,SAAS,MAAM;AAAA,EACvC,CAAC;AAED,EAAE,gBAAQ,EAAE,SAAS,MAAM,CAAC,GAAG,SAAU,MAAM;AAC7C,QAAI,SAAS,EAAE,KAAK,IAAI;AACxB,QAAI,IAAI,KAAK;AACb,QAAI,SAAS,EAAE,KAAK,CAAC;AACrB,WAAO,IAAI,KAAK;AAChB,iBAAa,SAAS,SAAS,MAAM;AAAA,EACvC,CAAC;AAED,IAAE,WAAW,MAAM,CAAC;AAEpB,SAAO;AACT;AA1BS;AA4BT,SAAS,WAAW,GAAG,UAAU;AAC/B,MAAI,WAAW,IAAI,MAAM;AACzB,MAAI,QAAQ;AACZ,MAAI,SAAS;AAEb,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,aAAS,QAAQ,GAAG,EAAE,GAAM,IAAI,GAAG,KAAK,EAAE,CAAC;AAAA,EAC7C,CAAC;AAID,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,aAAa,SAAS,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK;AAC5C,QAAI,SAAS,SAAS,CAAC;AACvB,QAAI,aAAa,aAAa;AAC9B,aAAS,QAAQ,EAAE,GAAG,EAAE,GAAG,UAAU;AACrC,aAAS,KAAK,IAAI,QAAS,SAAS,KAAK,EAAE,CAAC,EAAE,OAAO,MAAO;AAC5D,YAAQ,KAAK,IAAI,OAAQ,SAAS,KAAK,EAAE,CAAC,EAAE,IAAI,KAAK,MAAO;AAAA,EAC9D,CAAC;AAED,MAAI,UAAY,cAAM,SAAS,QAAQ,CAAC,EAAE,IAAI,WAAY;AACxD,WAAO,IAAI,KAAK;AAAA,EAClB,CAAC;AACD,MAAI,UAAU,QAAQ;AAEtB,EAAE,gBAAQ,SAAS,MAAM,GAAG,SAAU,GAAG;AACvC,iBAAa,SAAS,SAAS,SAAS,KAAK,CAAC,CAAC;AAAA,EACjD,CAAC;AAED,SAAO,EAAE,OAAO,UAAU,SAAkB,QAAiB;AAC/D;AA9BS;AAgCT,SAAS,aAAa,SAAS,SAAS,OAAO;AAC7C,MAAI,CAAC,MAAM,KAAK;AACd,YAAQ,CAAC,EAAE,QAAQ,KAAK;AAAA,EAC1B,WAAW,CAAC,MAAM,IAAI,GAAG;AACvB,YAAQ,QAAQ,SAAS,CAAC,EAAE,QAAQ,KAAK;AAAA,EAC3C,OAAO;AACL,YAAQ,MAAM,MAAM,MAAM,IAAI,IAAI,OAAO,EAAE,QAAQ,KAAK;AAAA,EAC1D;AACF;AARS;;;AChHT,SAAS,IAAI,GAAG;AACd,MAAI,MAAM,EAAE,MAAM,EAAE,cAAc,WAAW,UAAU,GAAG,SAAS,CAAC,CAAC,IAAI,OAAO,CAAC;AACjF,EAAE,gBAAQ,KAAK,SAAU,GAAG;AAC1B,QAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,MAAE,WAAW,CAAC;AACd,UAAM,cAAc,EAAE;AACtB,UAAM,WAAW;AACjB,MAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,OAAS,iBAAS,KAAK,CAAC;AAAA,EAC9C,CAAC;AAED,WAAS,SAASC,IAAG;AACnB,WAAO,SAAU,GAAG;AAClB,aAAOA,GAAE,KAAK,CAAC,EAAE;AAAA,IACnB;AAAA,EACF;AAJS;AAKX;AAfS;AAiBT,SAAS,OAAO,GAAG;AACjB,MAAI,MAAM,CAAC;AACX,MAAI,QAAQ,CAAC;AACb,MAAI,UAAU,CAAC;AAEf,WAASC,KAAI,GAAG;AACd,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,GAAG;AACpD;AAAA,IACF;AACA,YAAQ,CAAC,IAAI;AACb,UAAM,CAAC,IAAI;AACX,IAAE,gBAAQ,EAAE,SAAS,CAAC,GAAG,SAAU,GAAG;AACpC,UAAI,OAAO,UAAU,eAAe,KAAK,OAAO,EAAE,CAAC,GAAG;AACpD,YAAI,KAAK,CAAC;AAAA,MACZ,OAAO;AACL,QAAAA,KAAI,EAAE,CAAC;AAAA,MACT;AAAA,IACF,CAAC;AACD,WAAO,MAAM,CAAC;AAAA,EAChB;AAdS,SAAAA,MAAA;AAgBT,EAAE,gBAAQ,EAAE,MAAM,GAAGA,IAAG;AACxB,SAAO;AACT;AAvBS;AAyBT,SAASC,MAAK,GAAG;AACf,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,QAAI,MAAM,UAAU;AAClB,QAAE,WAAW,CAAC;AAEd,UAAI,cAAc,MAAM;AACxB,aAAO,MAAM;AACb,aAAO,MAAM;AACb,QAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,OAAO,WAAW;AAAA,IACxC;AAAA,EACF,CAAC;AACH;AAZS,OAAAA,OAAA;;;ACrBT,SAASC,KAAI,GAAG;AACd,IAAE,MAAM,EAAE,cAAc,CAAC;AACzB,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,MAAM;AACnC,kBAAc,GAAG,IAAI;AAAA,EACvB,CAAC;AACH;AALS,OAAAA,MAAA;AAUT,SAAS,cAAc,GAAG,GAAG;AAC3B,MAAI,IAAI,EAAE;AACV,MAAI,QAAQ,EAAE,KAAK,CAAC,EAAE;AACtB,MAAI,IAAI,EAAE;AACV,MAAI,QAAQ,EAAE,KAAK,CAAC,EAAE;AACtB,MAAI,OAAO,EAAE;AACb,MAAI,YAAY,EAAE,KAAK,CAAC;AACxB,MAAI,YAAY,UAAU;AAE1B,MAAI,UAAU,QAAQ,EAAG;AAEzB,IAAE,WAAW,CAAC;AAcd,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,OAAK,IAAI,GAAG,EAAE,OAAO,QAAQ,OAAO,EAAE,GAAG,EAAE,OAAO;AAChD,cAAU,SAAS,CAAC;AACpB,YAAQ;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR;AAAA,MACA,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AACA,YAAa,aAAa,GAAG,QAAQ,OAAO,IAAI;AAChD,QAAI,UAAU,WAAW;AACvB,YAAM,QAAQ,UAAU;AACxB,YAAM,SAAS,UAAU;AACzB,YAAM,QAAQ;AACd,YAAM,WAAW,UAAU;AAAA,IAC7B;AACA,MAAE,QAAQ,GAAG,OAAO,EAAE,QAAQ,UAAU,OAAO,GAAG,IAAI;AACtD,QAAI,MAAM,GAAG;AACX,QAAE,MAAM,EAAE,YAAY,KAAK,KAAK;AAAA,IAClC;AACA,QAAI;AAAA,EACN;AAEA,IAAE,QAAQ,GAAG,GAAG,EAAE,QAAQ,UAAU,OAAO,GAAG,IAAI;AACpD;AAnDS;AAqDT,SAASC,MAAK,GAAG;AACf,EAAE,gBAAQ,EAAE,MAAM,EAAE,aAAa,SAAU,GAAG;AAC5C,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,YAAY,KAAK;AACrB,QAAI;AACJ,MAAE,QAAQ,KAAK,SAAS,SAAS;AACjC,WAAO,KAAK,OAAO;AACjB,UAAI,EAAE,WAAW,CAAC,EAAE,CAAC;AACrB,QAAE,WAAW,CAAC;AACd,gBAAU,OAAO,KAAK,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAE,CAAC;AAC9C,UAAI,KAAK,UAAU,cAAc;AAC/B,kBAAU,IAAI,KAAK;AACnB,kBAAU,IAAI,KAAK;AACnB,kBAAU,QAAQ,KAAK;AACvB,kBAAU,SAAS,KAAK;AAAA,MAC1B;AACA,UAAI;AACJ,aAAO,EAAE,KAAK,CAAC;AAAA,IACjB;AAAA,EACF,CAAC;AACH;AApBS,OAAAA,OAAA;;;AChET,SAAS,YAAY,GAAG;AACtB,MAAI,UAAU,CAAC;AAEf,WAASC,KAAI,GAAG;AACd,QAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,GAAG;AACpD,aAAO,MAAM;AAAA,IACf;AACA,YAAQ,CAAC,IAAI;AAEb,QAAIC,QAAS;AAAA,MACT,YAAI,EAAE,SAAS,CAAC,GAAG,SAAU,GAAG;AAChC,eAAOD,KAAI,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;AAAA,MAC9B,CAAC;AAAA,IACH;AAEA,QACEC,UAAS,OAAO;AAAA,IAChBA,UAAS;AAAA,IACTA,UAAS,MACT;AAEA,MAAAA,QAAO;AAAA,IACT;AAEA,WAAQ,MAAM,OAAOA;AAAA,EACvB;AAvBS,SAAAD,MAAA;AAyBT,EAAE,gBAAQ,EAAE,QAAQ,GAAGA,IAAG;AAC5B;AA7BS;AAmCT,SAAS,MAAM,GAAG,GAAG;AACnB,SAAO,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE;AACzD;AAFS;;;AC7BT,SAAS,aAAa,GAAG;AACvB,MAAI,IAAI,IAAI,MAAM,EAAE,UAAU,MAAM,CAAC;AAGrC,MAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;AACvB,MAAI,OAAO,EAAE,UAAU;AACvB,IAAE,QAAQ,OAAO,CAAC,CAAC;AAEnB,MAAI,MAAM;AACV,SAAO,UAAU,GAAG,CAAC,IAAI,MAAM;AAC7B,WAAO,iBAAiB,GAAG,CAAC;AAC5B,YAAQ,EAAE,QAAQ,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI;AAC3D,eAAW,GAAG,GAAG,KAAK;AAAA,EACxB;AAEA,SAAO;AACT;AAhBS;AAsBT,SAAS,UAAU,GAAG,GAAG;AACvB,WAASE,KAAI,GAAG;AACd,IAAE,gBAAQ,EAAE,UAAU,CAAC,GAAG,SAAU,GAAG;AACrC,UAAI,QAAQ,EAAE,GACZ,IAAI,MAAM,QAAQ,EAAE,IAAI;AAC1B,UAAI,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG;AACjC,UAAE,QAAQ,GAAG,CAAC,CAAC;AACf,UAAE,QAAQ,GAAG,GAAG,CAAC,CAAC;AAClB,QAAAA,KAAI,CAAC;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AAVS,SAAAA,MAAA;AAYT,EAAE,gBAAQ,EAAE,MAAM,GAAGA,IAAG;AACxB,SAAO,EAAE,UAAU;AACrB;AAfS;AAqBT,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAS,cAAM,EAAE,MAAM,GAAG,SAAU,GAAG;AACrC,QAAI,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG;AACrC,aAAO,MAAM,GAAG,CAAC;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AANS;AAQT,SAAS,WAAW,GAAG,GAAG,OAAO;AAC/B,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,MAAE,KAAK,CAAC,EAAE,QAAQ;AAAA,EACpB,CAAC;AACH;AAJS;;;AC7ET,IAAI,sBAAwB,iBAAS,CAAC;;;ACDtC,IAAIC,uBAAwB,iBAAS,CAAC;;;ACAtC,QAAQ,iBAAiB;AAEzB,SAAS,QAAQ,GAAG;AAClB,MAAI,UAAU,CAAC;AACf,MAAI,QAAQ,CAAC;AACb,MAAI,UAAU,CAAC;AAEf,WAAS,MAAM,MAAM;AACnB,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,IAAI,GAAG;AACrD,YAAM,IAAI,eAAe;AAAA,IAC3B;AAEA,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,SAAS,IAAI,GAAG;AACxD,YAAM,IAAI,IAAI;AACd,cAAQ,IAAI,IAAI;AAChB,MAAE,gBAAK,EAAE,aAAa,IAAI,GAAG,KAAK;AAClC,aAAO,MAAM,IAAI;AACjB,cAAQ,KAAK,IAAI;AAAA,IACnB;AAAA,EACF;AAZS;AAcT,EAAE,gBAAK,EAAE,MAAM,GAAG,KAAK;AAEvB,MAAM,aAAK,OAAO,MAAM,EAAE,UAAU,GAAG;AACrC,UAAM,IAAI,eAAe;AAAA,EAC3B;AAEA,SAAO;AACT;AA1BS;AA4BT,SAAS,iBAAiB;AAAC;AAAlB;AACT,eAAe,YAAY,IAAI,MAAM;;;ACvBrC,SAAS,IAAI,GAAG,IAAIC,QAAO;AACzB,MAAI,CAAG,gBAAQ,EAAE,GAAG;AAClB,SAAK,CAAC,EAAE;AAAA,EACV;AAEA,MAAI,cAAc,EAAE,WAAW,IAAI,EAAE,aAAa,EAAE,WAAW,KAAK,CAAC;AAErE,MAAI,MAAM,CAAC;AACX,MAAI,UAAU,CAAC;AACf,EAAE,gBAAK,IAAI,SAAU,GAAG;AACtB,QAAI,CAAC,EAAE,QAAQ,CAAC,GAAG;AACjB,YAAM,IAAI,MAAM,+BAA+B,CAAC;AAAA,IAClD;AAEA,UAAM,GAAG,GAAGA,WAAU,QAAQ,SAAS,YAAY,GAAG;AAAA,EACxD,CAAC;AACD,SAAO;AACT;AAjBS;AAmBT,SAAS,MAAM,GAAG,GAAGC,YAAW,SAAS,YAAY,KAAK;AACxD,MAAI,CAAC,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,GAAG;AACrD,YAAQ,CAAC,IAAI;AAEb,QAAI,CAACA,YAAW;AACd,UAAI,KAAK,CAAC;AAAA,IACZ;AACA,IAAE,gBAAK,WAAW,CAAC,GAAG,SAAU,GAAG;AACjC,YAAM,GAAG,GAAGA,YAAW,SAAS,YAAY,GAAG;AAAA,IACjD,CAAC;AACD,QAAIA,YAAW;AACb,UAAI,KAAK,CAAC;AAAA,IACZ;AAAA,EACF;AACF;AAdS;;;AC3BT,SAAS,UAAU,GAAG,IAAI;AACxB,SAAO,IAAI,GAAG,IAAI,MAAM;AAC1B;AAFS;;;ACAT,SAAS,SAAS,GAAG,IAAI;AACvB,SAAO,IAAI,GAAG,IAAI,KAAK;AACzB;AAFS;;;ACKT,eAAe,mBAAmB;AAClC,eAAe,gBAAgB;AAC/B,eAAe,eAAe;AAC9B,eAAe,YAAY;AAC3B,eAAe,YAAY;AAC3B,eAAe,gBAAgB;AAmC/B,SAAS,eAAe,GAAG;AACzB,MAAI,SAAS,CAAC;AACd,cAAY,CAAC;AACb,MAAI,IAAI,aAAa,CAAC;AACtB,mBAAiB,CAAC;AAClB,gBAAc,GAAG,CAAC;AAElB,MAAI,GAAG;AACP,SAAQ,IAAI,UAAU,CAAC,GAAI;AACzB,QAAI,UAAU,GAAG,GAAG,CAAC;AACrB,kBAAc,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B;AACF;AAZS;AAiBT,SAAS,cAAc,GAAG,GAAG;AAC3B,MAAI,KAAS,UAAU,GAAG,EAAE,MAAM,CAAC;AACnC,OAAK,GAAG,MAAM,GAAG,GAAG,SAAS,CAAC;AAC9B,EAAE,gBAAQ,IAAI,SAAU,GAAG;AACzB,mBAAe,GAAG,GAAG,CAAC;AAAA,EACxB,CAAC;AACH;AANS;AAQT,SAAS,eAAe,GAAG,GAAG,OAAO;AACnC,MAAI,WAAW,EAAE,KAAK,KAAK;AAC3B,MAAI,SAAS,SAAS;AACtB,IAAE,KAAK,OAAO,MAAM,EAAE,WAAW,aAAa,GAAG,GAAG,KAAK;AAC3D;AAJS;AAUT,SAAS,aAAa,GAAG,GAAG,OAAO;AACjC,MAAI,WAAW,EAAE,KAAK,KAAK;AAC3B,MAAI,SAAS,SAAS;AAEtB,MAAI,cAAc;AAElB,MAAI,YAAY,EAAE,KAAK,OAAO,MAAM;AAEpC,MAAI,WAAW;AAEf,MAAI,CAAC,WAAW;AACd,kBAAc;AACd,gBAAY,EAAE,KAAK,QAAQ,KAAK;AAAA,EAClC;AAEA,aAAW,UAAU;AAErB,EAAE,gBAAQ,EAAE,UAAU,KAAK,GAAG,SAAU,GAAG;AACzC,QAAI,YAAY,EAAE,MAAM,OACtB,QAAQ,YAAY,EAAE,IAAI,EAAE;AAE9B,QAAI,UAAU,QAAQ;AACpB,UAAI,eAAe,cAAc,aAC/B,cAAc,EAAE,KAAK,CAAC,EAAE;AAE1B,kBAAY,eAAe,cAAc,CAAC;AAC1C,UAAI,WAAW,GAAG,OAAO,KAAK,GAAG;AAC/B,YAAI,gBAAgB,EAAE,KAAK,OAAO,KAAK,EAAE;AACzC,oBAAY,eAAe,CAAC,gBAAgB;AAAA,MAC9C;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAlCS;AAoCT,SAAS,iBAAiB,MAAM,MAAM;AACpC,MAAI,UAAU,SAAS,GAAG;AACxB,WAAO,KAAK,MAAM,EAAE,CAAC;AAAA,EACvB;AACA,kBAAgB,MAAM,CAAC,GAAG,GAAG,IAAI;AACnC;AALS;AAOT,SAAS,gBAAgB,MAAM,SAAS,SAAS,GAAG,QAAQ;AAC1D,MAAI,MAAM;AACV,MAAI,QAAQ,KAAK,KAAK,CAAC;AAEvB,UAAQ,CAAC,IAAI;AACb,EAAE,gBAAQ,KAAK,UAAU,CAAC,GAAG,SAAU,GAAG;AACxC,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,GAAG;AACrD,gBAAU,gBAAgB,MAAM,SAAS,SAAS,GAAG,CAAC;AAAA,IACxD;AAAA,EACF,CAAC;AAED,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,MAAI,QAAQ;AACV,UAAM,SAAS;AAAA,EACjB,OAAO;AAEL,WAAO,MAAM;AAAA,EACf;AAEA,SAAO;AACT;AArBS;AAuBT,SAAS,UAAU,MAAM;AACvB,SAAS,aAAK,KAAK,MAAM,GAAG,SAAU,GAAG;AACvC,WAAO,KAAK,KAAK,CAAC,EAAE,WAAW;AAAA,EACjC,CAAC;AACH;AAJS;AAMT,SAAS,UAAU,GAAG,GAAG,MAAM;AAC7B,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AAKb,MAAI,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG;AACpB,QAAI,KAAK;AACT,QAAI,KAAK;AAAA,EACX;AAEA,MAAI,SAAS,EAAE,KAAK,CAAC;AACrB,MAAI,SAAS,EAAE,KAAK,CAAC;AACrB,MAAI,YAAY;AAChB,MAAI,OAAO;AAIX,MAAI,OAAO,MAAM,OAAO,KAAK;AAC3B,gBAAY;AACZ,WAAO;AAAA,EACT;AAEA,MAAI,aAAe,eAAO,EAAE,MAAM,GAAG,SAAUC,OAAM;AACnD,WACE,SAAS,aAAa,GAAG,EAAE,KAAKA,MAAK,CAAC,GAAG,SAAS,KAClD,SAAS,aAAa,GAAG,EAAE,KAAKA,MAAK,CAAC,GAAG,SAAS;AAAA,EAEtD,CAAC;AAED,SAAS,cAAM,YAAY,SAAUA,OAAM;AACzC,WAAO,MAAM,GAAGA,KAAI;AAAA,EACtB,CAAC;AACH;AAlCS;AAoCT,SAAS,cAAc,GAAG,GAAG,GAAG,GAAG;AACjC,MAAI,IAAI,EAAE;AACV,MAAI,IAAI,EAAE;AACV,IAAE,WAAW,GAAG,CAAC;AACjB,IAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACtB,mBAAiB,CAAC;AAClB,gBAAc,GAAG,CAAC;AAClB,cAAY,GAAG,CAAC;AAClB;AARS;AAUT,SAAS,YAAY,GAAG,GAAG;AACzB,MAAI,OAAS,aAAK,EAAE,MAAM,GAAG,SAAU,GAAG;AACxC,WAAO,CAAC,EAAE,KAAK,CAAC,EAAE;AAAA,EACpB,CAAC;AACD,MAAI,KAAS,SAAS,GAAG,IAAI;AAC7B,OAAK,GAAG,MAAM,CAAC;AACf,EAAE,gBAAQ,IAAI,SAAU,GAAG;AACzB,QAAI,SAAS,EAAE,KAAK,CAAC,EAAE,QACrB,OAAO,EAAE,KAAK,GAAG,MAAM,GACvB,UAAU;AAEZ,QAAI,CAAC,MAAM;AACT,aAAO,EAAE,KAAK,QAAQ,CAAC;AACvB,gBAAU;AAAA,IACZ;AAEA,MAAE,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,MAAM,EAAE,QAAQ,UAAU,KAAK,SAAS,CAAC,KAAK;AAAA,EACxE,CAAC;AACH;AAlBS;AAuBT,SAAS,WAAW,MAAM,GAAG,GAAG;AAC9B,SAAO,KAAK,QAAQ,GAAG,CAAC;AAC1B;AAFS;AAQT,SAAS,aAAa,MAAM,QAAQ,WAAW;AAC7C,SAAO,UAAU,OAAO,OAAO,OAAO,OAAO,OAAO,UAAU;AAChE;AAFS;;;AChNT,SAAS,KAAK,GAAG;AACf,UAAQ,EAAE,MAAM,EAAE,QAAQ;AAAA,IACxB,KAAK;AACH,2BAAqB,CAAC;AACtB;AAAA,IACF,KAAK;AACH,sBAAgB,CAAC;AACjB;AAAA,IACF,KAAK;AACH,wBAAkB,CAAC;AACnB;AAAA,IACF;AACE,2BAAqB,CAAC;AAAA,EAC1B;AACF;AAdS;AAiBT,IAAI,oBAAoB;AAExB,SAAS,gBAAgB,GAAG;AAC1B,cAAY,CAAC;AACb,eAAa,CAAC;AAChB;AAHS;AAKT,SAAS,qBAAqB,GAAG;AAC/B,iBAAe,CAAC;AAClB;AAFS;;;ACrBT,SAASC,KAAI,GAAG;AACd,MAAI,OAAY,aAAa,GAAG,QAAQ,CAAC,GAAG,OAAO;AACnD,MAAI,SAAS,WAAW,CAAC;AACzB,MAAI,SAAW,YAAM,eAAO,MAAM,CAAC,IAAI;AACvC,MAAI,UAAU,IAAI,SAAS;AAE3B,IAAE,MAAM,EAAE,cAAc;AAGxB,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,MAAE,KAAK,CAAC,EAAE,UAAU;AAAA,EACtB,CAAC;AAGD,MAAI,SAAS,WAAW,CAAC,IAAI;AAG7B,EAAE,gBAAQ,EAAE,SAAS,GAAG,SAAU,OAAO;AACvC,IAAAC,KAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,KAAK;AAAA,EACrD,CAAC;AAID,IAAE,MAAM,EAAE,iBAAiB;AAC7B;AAxBS,OAAAD,MAAA;AA0BT,SAASC,KAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,GAAG;AACxD,MAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,MAAI,CAAC,SAAS,QAAQ;AACpB,QAAI,MAAM,MAAM;AACd,QAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,GAAG,QAAQ,QAAQ,CAAC;AAAA,IACnD;AACA;AAAA,EACF;AAEA,MAAI,MAAW,cAAc,GAAG,KAAK;AACrC,MAAI,SAAc,cAAc,GAAG,KAAK;AACxC,MAAI,QAAQ,EAAE,KAAK,CAAC;AAEpB,IAAE,UAAU,KAAK,CAAC;AAClB,QAAM,YAAY;AAClB,IAAE,UAAU,QAAQ,CAAC;AACrB,QAAM,eAAe;AAErB,EAAE,gBAAQ,UAAU,SAAU,OAAO;AACnC,IAAAA,KAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,KAAK;AAEnD,QAAI,YAAY,EAAE,KAAK,KAAK;AAC5B,QAAI,WAAW,UAAU,YAAY,UAAU,YAAY;AAC3D,QAAI,cAAc,UAAU,eAAe,UAAU,eAAe;AACpE,QAAI,aAAa,UAAU,YAAY,SAAS,IAAI;AACpD,QAAI,SAAS,aAAa,cAAc,IAAI,SAAS,OAAO,CAAC,IAAI;AAEjE,MAAE,QAAQ,KAAK,UAAU;AAAA,MACvB,QAAQ;AAAA,MACR;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AAED,MAAE,QAAQ,aAAa,QAAQ;AAAA,MAC7B,QAAQ;AAAA,MACR;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AAAA,EACH,CAAC;AAED,MAAI,CAAC,EAAE,OAAO,CAAC,GAAG;AAChB,MAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,GAAG,QAAQ,SAAS,OAAO,CAAC,EAAE,CAAC;AAAA,EAChE;AACF;AA3CS,OAAAA,MAAA;AA6CT,SAAS,WAAW,GAAG;AACrB,MAAI,SAAS,CAAC;AACd,WAASA,KAAI,GAAG,OAAO;AACrB,QAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,QAAI,YAAY,SAAS,QAAQ;AAC/B,MAAE,gBAAQ,UAAU,SAAU,OAAO;AACnC,QAAAA,KAAI,OAAO,QAAQ,CAAC;AAAA,MACtB,CAAC;AAAA,IACH;AACA,WAAO,CAAC,IAAI;AAAA,EACd;AARS,SAAAA,MAAA;AAST,EAAE,gBAAQ,EAAE,SAAS,GAAG,SAAU,GAAG;AACnC,IAAAA,KAAI,GAAG,CAAC;AAAA,EACV,CAAC;AACD,SAAO;AACT;AAfS;AAiBT,SAAS,WAAW,GAAG;AACrB,SAAS;AAAA,IACP,EAAE,MAAM;AAAA,IACR,SAAU,KAAK,GAAG;AAChB,aAAO,MAAM,EAAE,KAAK,CAAC,EAAE;AAAA,IACzB;AAAA,IACA;AAAA,EACF;AACF;AARS;AAUT,SAAS,QAAQ,GAAG;AAClB,MAAI,aAAa,EAAE,MAAM;AACzB,IAAE,WAAW,WAAW,WAAW;AACnC,SAAO,WAAW;AAClB,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,aAAa;AACpB,QAAE,WAAW,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AAVS;;;AC1HT,SAAS,uBAAuB,GAAG,IAAI,IAAI;AACzC,MAAI,OAAO,CAAC,GACV;AAEF,EAAE,gBAAQ,IAAI,SAAU,GAAG;AACzB,QAAI,QAAQ,EAAE,OAAO,CAAC,GACpB,QACA;AACF,WAAO,OAAO;AACZ,eAAS,EAAE,OAAO,KAAK;AACvB,UAAI,QAAQ;AACV,oBAAY,KAAK,MAAM;AACvB,aAAK,MAAM,IAAI;AAAA,MACjB,OAAO;AACL,oBAAY;AACZ,mBAAW;AAAA,MACb;AACA,UAAI,aAAa,cAAc,OAAO;AACpC,WAAG,QAAQ,WAAW,KAAK;AAC3B;AAAA,MACF;AACA,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AAyBH;AAhDS;;;AC+BT,SAAS,gBAAgB,GAAGC,OAAM,cAAc;AAC9C,MAAI,OAAO,eAAe,CAAC,GACzB,SAAS,IAAI,MAAM,EAAE,UAAU,KAAK,CAAC,EAClC,SAAS,EAAE,KAAW,CAAC,EACvB,oBAAoB,SAAU,GAAG;AAChC,WAAO,EAAE,KAAK,CAAC;AAAA,EACjB,CAAC;AAEL,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC,GACjB,SAAS,EAAE,OAAO,CAAC;AAErB,QAAI,KAAK,SAASA,SAAS,KAAK,WAAWA,SAAQA,SAAQ,KAAK,SAAU;AACxE,aAAO,QAAQ,CAAC;AAChB,aAAO,UAAU,GAAG,UAAU,IAAI;AAGlC,MAAE,gBAAQ,EAAE,YAAY,EAAE,CAAC,GAAG,SAAU,GAAG;AACzC,YAAI,IAAI,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,GAC1B,OAAO,OAAO,KAAK,GAAG,CAAC,GACvB,SAAS,CAAG,oBAAY,IAAI,IAAI,KAAK,SAAS;AAChD,eAAO,QAAQ,GAAG,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,SAAS,OAAO,CAAC;AAAA,MAC5D,CAAC;AAED,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,SAAS,GAAG;AACzD,eAAO,QAAQ,GAAG;AAAA,UAChB,YAAY,KAAK,WAAWA,KAAI;AAAA,UAChC,aAAa,KAAK,YAAYA,KAAI;AAAA,QACpC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAlCS;AAoCT,SAAS,eAAe,GAAG;AACzB,MAAI;AACJ,SAAO,EAAE,QAAS,IAAM,iBAAS,OAAO,CAAE,EAAE;AAC5C,SAAO;AACT;AAJS;;;ACnDT,SAAS,WAAW,GAAG,UAAU;AAC/B,MAAI,KAAK;AACT,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACxC,UAAM,mBAAmB,GAAG,SAAS,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,EAC1D;AACA,SAAO;AACT;AANS;AAQT,SAAS,mBAAmB,GAAG,YAAY,YAAY;AAIrD,MAAI,WAAa;AAAA,IACf;AAAA,IACE,YAAI,YAAY,SAAU,GAAG,GAAG;AAChC,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,eAAiB;AAAA,IACjB,YAAI,YAAY,SAAU,GAAG;AAC7B,aAAS;AAAA,QACL,YAAI,EAAE,SAAS,CAAC,GAAG,SAAU,GAAG;AAChC,iBAAO,EAAE,KAAK,SAAS,EAAE,CAAC,GAAG,QAAQ,EAAE,KAAK,CAAC,EAAE,OAAO;AAAA,QACxD,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAGA,MAAI,aAAa;AACjB,SAAO,aAAa,WAAW,OAAQ,gBAAe;AACtD,MAAI,WAAW,IAAI,aAAa;AAChC,gBAAc;AACd,MAAI,OAAS,YAAI,IAAI,MAAM,QAAQ,GAAG,WAAY;AAChD,WAAO;AAAA,EACT,CAAC;AAGD,MAAI,KAAK;AACT,EAAE;AAAA;AAAA,IAEA,aAAa,QAAQ,SAAU,OAAO;AACpC,UAAI,QAAQ,MAAM,MAAM;AACxB,WAAK,KAAK,KAAK,MAAM;AACrB,UAAI,YAAY;AAEhB,aAAO,QAAQ,GAAG;AAEhB,YAAI,QAAQ,GAAG;AACb,uBAAa,KAAK,QAAQ,CAAC;AAAA,QAC7B;AAEA,gBAAS,QAAQ,KAAM;AACvB,aAAK,KAAK,KAAK,MAAM;AAAA,MACvB;AACA,YAAM,MAAM,SAAS;AAAA,IACvB,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AArDS;;;ACfF,SAAS,UAAU,GAAG;AAC3B,MAAI,UAAU,CAAC;AACf,MAAI,cAAgB,eAAO,EAAE,MAAM,GAAG,SAAU,GAAG;AACjD,WAAO,CAAC,EAAE,SAAS,CAAC,EAAE;AAAA,EACxB,CAAC;AACD,MAAIC,WAAY;AAAA,IACZ,YAAI,aAAa,SAAU,GAAG;AAC9B,aAAO,EAAE,KAAK,CAAC,EAAE;AAAA,IACnB,CAAC;AAAA,EACH;AACA,MAAI,SAAW,YAAM,cAAMA,WAAU,CAAC,GAAG,WAAY;AACnD,WAAO,CAAC;AAAA,EACV,CAAC;AAED,WAASC,KAAI,GAAG;AACd,QAAM,YAAI,SAAS,CAAC,EAAG;AACvB,YAAQ,CAAC,IAAI;AACb,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,WAAO,KAAK,IAAI,EAAE,KAAK,CAAC;AACxB,IAAE,gBAAQ,EAAE,WAAW,CAAC,GAAGA,IAAG;AAAA,EAChC;AANS,SAAAA,MAAA;AAQT,MAAI,YAAc,eAAO,aAAa,SAAU,GAAG;AACjD,WAAO,EAAE,KAAK,CAAC,EAAE;AAAA,EACnB,CAAC;AACD,EAAE,gBAAQ,WAAWA,IAAG;AAExB,SAAO;AACT;AA5BgB;;;ACThB,SAAS,WAAW,GAAG,SAAS;AAC9B,SAAS,YAAI,SAAS,SAAU,GAAG;AACjC,QAAI,MAAM,EAAE,QAAQ,CAAC;AACrB,QAAI,CAAC,IAAI,QAAQ;AACf,aAAO,EAAE,EAAK;AAAA,IAChB,OAAO;AACL,UAAI,SAAW;AAAA,QACb;AAAA,QACA,SAAU,KAAK,GAAG;AAChB,cAAI,OAAO,EAAE,KAAK,CAAC,GACjB,QAAQ,EAAE,KAAK,EAAE,CAAC;AACpB,iBAAO;AAAA,YACL,KAAK,IAAI,MAAM,KAAK,SAAS,MAAM;AAAA,YACnC,QAAQ,IAAI,SAAS,KAAK;AAAA,UAC5B;AAAA,QACF;AAAA,QACA,EAAE,KAAK,GAAG,QAAQ,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,QACL;AAAA,QACA,YAAY,OAAO,MAAM,OAAO;AAAA,QAChC,QAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AA1BS;;;ACyBT,SAAS,iBAAiB,SAAS,IAAI;AACrC,MAAI,gBAAgB,CAAC;AACrB,EAAE,gBAAQ,SAAS,SAAU,OAAO,GAAG;AACrC,QAAI,MAAO,cAAc,MAAM,CAAC,IAAI;AAAA,MAClC,UAAU;AAAA,MACV,IAAI,CAAC;AAAA,MACL,KAAK,CAAC;AAAA,MACN,IAAI,CAAC,MAAM,CAAC;AAAA,MACZ;AAAA,IACF;AACA,QAAI,CAAG,oBAAY,MAAM,UAAU,GAAG;AAEpC,UAAI,aAAa,MAAM;AAEvB,UAAI,SAAS,MAAM;AAAA,IACrB;AAAA,EACF,CAAC;AAED,EAAE,gBAAQ,GAAG,MAAM,GAAG,SAAU,GAAG;AACjC,QAAI,SAAS,cAAc,EAAE,CAAC;AAC9B,QAAI,SAAS,cAAc,EAAE,CAAC;AAC9B,QAAI,CAAG,oBAAY,MAAM,KAAK,CAAG,oBAAY,MAAM,GAAG;AACpD,aAAO;AACP,aAAO,IAAI,KAAK,cAAc,EAAE,CAAC,CAAC;AAAA,IACpC;AAAA,EACF,CAAC;AAED,MAAI,YAAc,eAAO,eAAe,SAAU,OAAO;AAEvD,WAAO,CAAC,MAAM;AAAA,EAChB,CAAC;AAED,SAAO,mBAAmB,SAAS;AACrC;AAjCS;AAmCT,SAAS,mBAAmB,WAAW;AACrC,MAAI,UAAU,CAAC;AAEf,WAAS,SAAS,QAAQ;AACxB,WAAO,SAAU,QAAQ;AACvB,UAAI,OAAO,QAAQ;AACjB;AAAA,MACF;AACA,UACI,oBAAY,OAAO,UAAU,KAC7B,oBAAY,OAAO,UAAU,KAC/B,OAAO,cAAc,OAAO,YAC5B;AACA,qBAAa,QAAQ,MAAM;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAbS;AAeT,WAAS,UAAU,QAAQ;AACzB,WAAO,SAAU,QAAQ;AACvB,aAAO,IAAI,EAAE,KAAK,MAAM;AACxB,UAAI,EAAE,OAAO,aAAa,GAAG;AAC3B,kBAAU,KAAK,MAAM;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAPS;AAST,SAAO,UAAU,QAAQ;AACvB,QAAI,QAAQ,UAAU,IAAI;AAC1B,YAAQ,KAAK,KAAK;AAClB,IAAE,gBAAQ,MAAM,IAAI,EAAE,QAAQ,GAAG,SAAS,KAAK,CAAC;AAChD,IAAE,gBAAQ,MAAM,KAAK,UAAU,KAAK,CAAC;AAAA,EACvC;AAEA,SAAS;AAAA,IACL,eAAO,SAAS,SAAUC,QAAO;AACjC,aAAO,CAACA,OAAM;AAAA,IAChB,CAAC;AAAA,IACD,SAAUA,QAAO;AACf,aAAS,aAAKA,QAAO,CAAC,MAAM,KAAK,cAAc,QAAQ,CAAC;AAAA,IAC1D;AAAA,EACF;AACF;AA1CS;AA4CT,SAAS,aAAa,QAAQ,QAAQ;AACpC,MAAI,MAAM;AACV,MAAI,SAAS;AAEb,MAAI,OAAO,QAAQ;AACjB,WAAO,OAAO,aAAa,OAAO;AAClC,cAAU,OAAO;AAAA,EACnB;AAEA,MAAI,OAAO,QAAQ;AACjB,WAAO,OAAO,aAAa,OAAO;AAClC,cAAU,OAAO;AAAA,EACnB;AAEA,SAAO,KAAK,OAAO,GAAG,OAAO,OAAO,EAAE;AACtC,SAAO,aAAa,MAAM;AAC1B,SAAO,SAAS;AAChB,SAAO,IAAI,KAAK,IAAI,OAAO,GAAG,OAAO,CAAC;AACtC,SAAO,SAAS;AAClB;AAnBS;;;ACvGT,SAAS,KAAK,SAAS,WAAW;AAChC,MAAI,QAAa,UAAU,SAAS,SAAU,OAAO;AACnD,WAAO,OAAO,UAAU,eAAe,KAAK,OAAO,YAAY;AAAA,EACjE,CAAC;AACD,MAAI,WAAW,MAAM,KACnB,aAAe,eAAO,MAAM,KAAK,SAAU,OAAO;AAChD,WAAO,CAAC,MAAM;AAAA,EAChB,CAAC,GACD,KAAK,CAAC,GACN,MAAM,GACN,SAAS,GACT,UAAU;AAEZ,WAAS,KAAK,gBAAgB,CAAC,CAAC,SAAS,CAAC;AAE1C,YAAU,kBAAkB,IAAI,YAAY,OAAO;AAEnD,EAAE,gBAAQ,UAAU,SAAU,OAAO;AACnC,eAAW,MAAM,GAAG;AACpB,OAAG,KAAK,MAAM,EAAE;AAChB,WAAO,MAAM,aAAa,MAAM;AAChC,cAAU,MAAM;AAChB,cAAU,kBAAkB,IAAI,YAAY,OAAO;AAAA,EACrD,CAAC;AAED,MAAI,SAAS,EAAE,IAAM,gBAAQ,EAAE,EAAE;AACjC,MAAI,QAAQ;AACV,WAAO,aAAa,MAAM;AAC1B,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT;AA/BS;AAiCT,SAAS,kBAAkB,IAAI,YAAY,OAAO;AAChD,MAAI;AACJ,SAAO,WAAW,WAAW,OAAS,aAAK,UAAU,GAAG,KAAK,OAAO;AAClE,eAAW,IAAI;AACf,OAAG,KAAK,KAAK,EAAE;AACf;AAAA,EACF;AACA,SAAO;AACT;AARS;AAUT,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAU,QAAQ,QAAQ;AAC/B,QAAI,OAAO,aAAa,OAAO,YAAY;AACzC,aAAO;AAAA,IACT,WAAW,OAAO,aAAa,OAAO,YAAY;AAChD,aAAO;AAAA,IACT;AAEA,WAAO,CAAC,OAAO,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO;AAAA,EACzD;AACF;AAVS;;;ACzCT,SAAS,aAAa,GAAG,GAAG,IAAI,WAAW;AACzC,MAAI,UAAU,EAAE,SAAS,CAAC;AAC1B,MAAI,OAAO,EAAE,KAAK,CAAC;AACnB,MAAI,KAAK,OAAO,KAAK,aAAa;AAClC,MAAI,KAAK,OAAO,KAAK,cAAc;AACnC,MAAI,YAAY,CAAC;AAEjB,MAAI,IAAI;AACN,cAAY,eAAO,SAAS,SAAU,GAAG;AACvC,aAAO,MAAM,MAAM,MAAM;AAAA,IAC3B,CAAC;AAAA,EACH;AAEA,MAAI,cAAc,WAAW,GAAG,OAAO;AACvC,EAAE,gBAAQ,aAAa,SAAU,OAAO;AACtC,QAAI,EAAE,SAAS,MAAM,CAAC,EAAE,QAAQ;AAC9B,UAAI,iBAAiB,aAAa,GAAG,MAAM,GAAG,IAAI,SAAS;AAC3D,gBAAU,MAAM,CAAC,IAAI;AACrB,UAAI,OAAO,UAAU,eAAe,KAAK,gBAAgB,YAAY,GAAG;AACtE,yBAAiB,OAAO,cAAc;AAAA,MACxC;AAAA,IACF;AAAA,EACF,CAAC;AAED,MAAI,UAAU,iBAAiB,aAAa,EAAE;AAC9C,kBAAgB,SAAS,SAAS;AAElC,MAAI,SAAS,KAAK,SAAS,SAAS;AAEpC,MAAI,IAAI;AACN,WAAO,KAAO,gBAAQ,CAAC,IAAI,OAAO,IAAI,EAAE,CAAC;AACzC,QAAI,EAAE,aAAa,EAAE,EAAE,QAAQ;AAC7B,UAAI,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC,GACvC,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;AACvC,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,YAAY,GAAG;AAC/D,eAAO,aAAa;AACpB,eAAO,SAAS;AAAA,MAClB;AACA,aAAO,cACJ,OAAO,aAAa,OAAO,SAAS,OAAO,QAAQ,OAAO,UAAU,OAAO,SAAS;AACvF,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AACT;AA7CS;AA+CT,SAAS,gBAAgB,SAAS,WAAW;AAC3C,EAAE,gBAAQ,SAAS,SAAU,OAAO;AAClC,UAAM,KAAO;AAAA,MACX,MAAM,GAAG,IAAI,SAAU,GAAG;AACxB,YAAI,UAAU,CAAC,GAAG;AAChB,iBAAO,UAAU,CAAC,EAAE;AAAA,QACtB;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAXS;AAaT,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAI,CAAG,oBAAY,OAAO,UAAU,GAAG;AACrC,WAAO,cACJ,OAAO,aAAa,OAAO,SAAS,MAAM,aAAa,MAAM,WAC7D,OAAO,SAAS,MAAM;AACzB,WAAO,UAAU,MAAM;AAAA,EACzB,OAAO;AACL,WAAO,aAAa,MAAM;AAC1B,WAAO,SAAS,MAAM;AAAA,EACxB;AACF;AAVS;;;ACzCT,SAAS,MAAM,GAAG;AAChB,MAAIC,WAAe,QAAQ,CAAC,GAC1B,kBAAkB,iBAAiB,GAAK,cAAM,GAAGA,WAAU,CAAC,GAAG,SAAS,GACxE,gBAAgB,iBAAiB,GAAK,cAAMA,WAAU,GAAG,IAAI,EAAE,GAAG,UAAU;AAE9E,MAAI,WAAW,UAAU,CAAC;AAC1B,cAAY,GAAG,QAAQ;AAEvB,MAAI,SAAS,OAAO,mBAClB;AAEF,WAAS,IAAI,GAAG,WAAW,GAAG,WAAW,GAAG,EAAE,GAAG,EAAE,UAAU;AAC3D,qBAAiB,IAAI,IAAI,kBAAkB,eAAe,IAAI,KAAK,CAAC;AAEpE,eAAgB,iBAAiB,CAAC;AAClC,QAAI,KAAK,WAAW,GAAG,QAAQ;AAC/B,QAAI,KAAK,QAAQ;AACf,iBAAW;AACX,aAAS,kBAAU,QAAQ;AAC3B,eAAS;AAAA,IACX;AAAA,EACF;AAEA,cAAY,GAAG,IAAI;AACrB;AAxBS;AA0BT,SAAS,iBAAiB,GAAG,OAAO,cAAc;AAChD,SAAS,YAAI,OAAO,SAAUC,OAAM;AAClC,WAAO,gBAAgB,GAAGA,OAAM,YAAY;AAAA,EAC9C,CAAC;AACH;AAJS;AAMT,SAAS,iBAAiB,aAAa,WAAW;AAChD,MAAI,KAAK,IAAI,MAAM;AACnB,EAAE,gBAAQ,aAAa,SAAU,IAAI;AACnC,QAAI,OAAO,GAAG,MAAM,EAAE;AACtB,QAAI,SAAS,aAAa,IAAI,MAAM,IAAI,SAAS;AACjD,IAAE,gBAAQ,OAAO,IAAI,SAAU,GAAG,GAAG;AACnC,SAAG,KAAK,CAAC,EAAE,QAAQ;AAAA,IACrB,CAAC;AACD,2BAAuB,IAAI,IAAI,OAAO,EAAE;AAAA,EAC1C,CAAC;AACH;AAVS;AAYT,SAAS,YAAY,GAAG,UAAU;AAChC,EAAE,gBAAQ,UAAU,SAAU,OAAO;AACnC,IAAE,gBAAQ,OAAO,SAAU,GAAG,GAAG;AAC/B,QAAE,KAAK,CAAC,EAAE,QAAQ;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AACH;AANS;;;AClET,SAAS,kBAAkB,GAAG;AAC5B,MAAI,gBAAgBC,WAAU,CAAC;AAE/B,EAAE,gBAAQ,EAAE,MAAM,EAAE,aAAa,SAAU,GAAG;AAC5C,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,UAAU,KAAK;AACnB,QAAI,WAAW,SAAS,GAAG,eAAe,QAAQ,GAAG,QAAQ,CAAC;AAC9D,QAAI,OAAO,SAAS;AACpB,QAAI,MAAM,SAAS;AACnB,QAAI,UAAU;AACd,QAAI,QAAQ,KAAK,OAAO;AACxB,QAAI,YAAY;AAEhB,WAAO,MAAM,QAAQ,GAAG;AACtB,aAAO,EAAE,KAAK,CAAC;AAEf,UAAI,WAAW;AACb,gBAAQ,QAAQ,KAAK,OAAO,OAAO,OAAO,EAAE,KAAK,KAAK,EAAE,UAAU,KAAK,MAAM;AAC3E;AAAA,QACF;AAEA,YAAI,UAAU,KAAK;AACjB,sBAAY;AAAA,QACd;AAAA,MACF;AAEA,UAAI,CAAC,WAAW;AACd,eACE,UAAU,KAAK,SAAS,KACxB,EAAE,KAAM,QAAQ,KAAK,UAAU,CAAC,CAAE,EAAE,WAAW,KAAK,MACpD;AACA;AAAA,QACF;AACA,gBAAQ,KAAK,OAAO;AAAA,MACtB;AAEA,QAAE,UAAU,GAAG,KAAK;AACpB,UAAI,EAAE,WAAW,CAAC,EAAE,CAAC;AAAA,IACvB;AAAA,EACF,CAAC;AACH;AAxCS;AA4CT,SAAS,SAAS,GAAG,eAAe,GAAG,GAAG;AACxC,MAAI,QAAQ,CAAC;AACb,MAAI,QAAQ,CAAC;AACb,MAAI,MAAM,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE,GAAG;AAC7D,MAAI,MAAM,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE,GAAG;AAC7D,MAAI;AACJ,MAAI;AAGJ,WAAS;AACT,KAAG;AACD,aAAS,EAAE,OAAO,MAAM;AACxB,UAAM,KAAK,MAAM;AAAA,EACnB,SAAS,WAAW,cAAc,MAAM,EAAE,MAAM,OAAO,MAAM,cAAc,MAAM,EAAE;AACnF,QAAM;AAGN,WAAS;AACT,UAAQ,SAAS,EAAE,OAAO,MAAM,OAAO,KAAK;AAC1C,UAAM,KAAK,MAAM;AAAA,EACnB;AAEA,SAAO,EAAE,MAAM,MAAM,OAAO,MAAM,QAAQ,CAAC,GAAG,IAAS;AACzD;AAvBS;AAyBT,SAASA,WAAU,GAAG;AACpB,MAAI,SAAS,CAAC;AACd,MAAI,MAAM;AAEV,WAASC,KAAI,GAAG;AACd,QAAI,MAAM;AACV,IAAE,gBAAQ,EAAE,SAAS,CAAC,GAAGA,IAAG;AAC5B,WAAO,CAAC,IAAI,EAAE,KAAU,KAAK,MAAM;AAAA,EACrC;AAJS,SAAAA,MAAA;AAKT,EAAE,gBAAQ,EAAE,SAAS,GAAGA,IAAG;AAE3B,SAAO;AACT;AAZS,OAAAD,YAAA;;;AClCT,SAAS,mBAAmB,GAAG,UAAU;AACvC,MAAI,YAAY,CAAC;AAEjB,WAAS,WAAW,WAAW,OAAO;AACpC,QAEE,KAAK,GAGL,UAAU,GACV,kBAAkB,UAAU,QAC5B,WAAa,aAAK,KAAK;AAEzB,IAAE,gBAAQ,OAAO,SAAU,GAAG,GAAG;AAC/B,UAAI,IAAI,0BAA0B,GAAG,CAAC,GACpC,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,QAAQ;AAE7B,UAAI,KAAK,MAAM,UAAU;AACvB,QAAE,gBAAQ,MAAM,MAAM,SAAS,IAAI,CAAC,GAAG,SAAU,UAAU;AACzD,UAAE,gBAAQ,EAAE,aAAa,QAAQ,GAAG,SAAU,GAAG;AAC/C,gBAAI,SAAS,EAAE,KAAK,CAAC,GACnB,OAAO,OAAO;AAChB,iBAAK,OAAO,MAAM,KAAK,SAAS,EAAE,OAAO,SAAS,EAAE,KAAK,QAAQ,EAAE,QAAQ;AACzE,0BAAY,WAAW,GAAG,QAAQ;AAAA,YACpC;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAED,kBAAU,IAAI;AACd,aAAK;AAAA,MACP;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AA/BS;AAiCT,EAAE,eAAO,UAAU,UAAU;AAC7B,SAAO;AACT;AAtCS;AAwCT,SAAS,mBAAmB,GAAG,UAAU;AACvC,MAAI,YAAY,CAAC;AAEjB,WAAS,KAAK,OAAO,UAAU,UAAU,iBAAiB,iBAAiB;AACzE,QAAI;AACJ,IAAE,gBAAU,cAAM,UAAU,QAAQ,GAAG,SAAU,GAAG;AAClD,UAAI,MAAM,CAAC;AACX,UAAI,EAAE,KAAK,CAAC,EAAE,OAAO;AACnB,QAAE,gBAAQ,EAAE,aAAa,CAAC,GAAG,SAAU,GAAG;AACxC,cAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,cAAI,MAAM,UAAU,MAAM,QAAQ,mBAAmB,MAAM,QAAQ,kBAAkB;AACnF,wBAAY,WAAW,GAAG,CAAC;AAAA,UAC7B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAbS;AAeT,WAAS,WAAW,OAAO,OAAO;AAChC,QAAI,eAAe,IACjB,cACA,WAAW;AAEb,IAAE,gBAAQ,OAAO,SAAU,GAAG,gBAAgB;AAC5C,UAAI,EAAE,KAAK,CAAC,EAAE,UAAU,UAAU;AAChC,YAAI,eAAe,EAAE,aAAa,CAAC;AACnC,YAAI,aAAa,QAAQ;AACvB,yBAAe,EAAE,KAAK,aAAa,CAAC,CAAC,EAAE;AACvC,eAAK,OAAO,UAAU,gBAAgB,cAAc,YAAY;AAEhE,qBAAW;AACX,yBAAe;AAAA,QACjB;AAAA,MACF;AACA,WAAK,OAAO,UAAU,MAAM,QAAQ,cAAc,MAAM,MAAM;AAAA,IAChE,CAAC;AAED,WAAO;AAAA,EACT;AApBS;AAsBT,EAAE,eAAO,UAAU,UAAU;AAC7B,SAAO;AACT;AA1CS;AA4CT,SAAS,0BAA0B,GAAG,GAAG;AACvC,MAAI,EAAE,KAAK,CAAC,EAAE,OAAO;AACnB,WAAS,aAAK,EAAE,aAAa,CAAC,GAAG,SAAU,GAAG;AAC5C,aAAO,EAAE,KAAK,CAAC,EAAE;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AANS;AAQT,SAAS,YAAY,WAAW,GAAG,GAAG;AACpC,MAAI,IAAI,GAAG;AACT,QAAI,MAAM;AACV,QAAI;AACJ,QAAI;AAAA,EACN;AAEA,MAAI,aAAa,UAAU,CAAC;AAC5B,MAAI,CAAC,YAAY;AACf,cAAU,CAAC,IAAI,aAAa,CAAC;AAAA,EAC/B;AACA,aAAW,CAAC,IAAI;AAClB;AAZS;AAcT,SAAS,YAAY,WAAW,GAAG,GAAG;AACpC,MAAI,IAAI,GAAG;AACT,QAAI,MAAM;AACV,QAAI;AACJ,QAAI;AAAA,EACN;AACA,SAAO,CAAC,CAAC,UAAU,CAAC,KAAK,OAAO,UAAU,eAAe,KAAK,UAAU,CAAC,GAAG,CAAC;AAC/E;AAPS;AAiBT,SAAS,kBAAkB,GAAG,UAAU,WAAW,YAAY;AAC7D,MAAI,OAAO,CAAC,GACV,QAAQ,CAAC,GACT,MAAM,CAAC;AAKT,EAAE,gBAAQ,UAAU,SAAU,OAAO;AACnC,IAAE,gBAAQ,OAAO,SAAU,GAAGE,QAAO;AACnC,WAAK,CAAC,IAAI;AACV,YAAM,CAAC,IAAI;AACX,UAAI,CAAC,IAAIA;AAAA,IACX,CAAC;AAAA,EACH,CAAC;AAED,EAAE,gBAAQ,UAAU,SAAU,OAAO;AACnC,QAAI,UAAU;AACd,IAAE,gBAAQ,OAAO,SAAU,GAAG;AAC5B,UAAI,KAAK,WAAW,CAAC;AACrB,UAAI,GAAG,QAAQ;AACb,aAAO,eAAO,IAAI,SAAUC,IAAG;AAC7B,iBAAO,IAAIA,EAAC;AAAA,QACd,CAAC;AACD,YAAI,MAAM,GAAG,SAAS,KAAK;AAC3B,iBAAS,IAAI,KAAK,MAAM,EAAE,GAAG,KAAK,KAAK,KAAK,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG;AAC7D,cAAI,IAAI,GAAG,CAAC;AACZ,cAAI,MAAM,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,KAAK,CAAC,YAAY,WAAW,GAAG,CAAC,GAAG;AACvE,kBAAM,CAAC,IAAI;AACX,kBAAM,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;AAC3B,sBAAU,IAAI,CAAC;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,SAAO,EAAE,MAAY,MAAa;AACpC;AAtCS;AAwCT,SAAS,qBAAqB,GAAG,UAAU,MAAM,OAAO,YAAY;AAMlE,MAAI,KAAK,CAAC,GACR,SAAS,gBAAgB,GAAG,UAAU,MAAM,UAAU,GACtD,aAAa,aAAa,eAAe;AAE3C,WAAS,QAAQ,WAAW,eAAe;AACzC,QAAI,QAAQ,OAAO,MAAM;AACzB,QAAI,OAAO,MAAM,IAAI;AACrB,QAAI,UAAU,CAAC;AACf,WAAO,MAAM;AACX,UAAI,QAAQ,IAAI,GAAG;AACjB,kBAAU,IAAI;AAAA,MAChB,OAAO;AACL,gBAAQ,IAAI,IAAI;AAChB,cAAM,KAAK,IAAI;AACf,gBAAQ,MAAM,OAAO,cAAc,IAAI,CAAC;AAAA,MAC1C;AAEA,aAAO,MAAM,IAAI;AAAA,IACnB;AAAA,EACF;AAfS;AAkBT,WAAS,MAAM,MAAM;AACnB,OAAG,IAAI,IAAI,OAAO,QAAQ,IAAI,EAAE,OAAO,SAAU,KAAK,GAAG;AACvD,aAAO,KAAK,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;AAAA,IAC/C,GAAG,CAAC;AAAA,EACN;AAJS;AAOT,WAAS,MAAM,MAAM;AACnB,QAAI,MAAM,OAAO,SAAS,IAAI,EAAE,OAAO,SAAU,KAAK,GAAG;AACvD,aAAO,KAAK,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;AAAA,IAC/C,GAAG,OAAO,iBAAiB;AAE3B,QAAI,OAAO,EAAE,KAAK,IAAI;AACtB,QAAI,QAAQ,OAAO,qBAAqB,KAAK,eAAe,YAAY;AACtE,SAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG;AAAA,IACnC;AAAA,EACF;AATS;AAWT,UAAQ,OAAO,OAAO,aAAa,KAAK,MAAM,CAAC;AAC/C,UAAQ,OAAO,OAAO,WAAW,KAAK,MAAM,CAAC;AAG7C,EAAE,gBAAQ,OAAO,SAAU,GAAG;AAC5B,OAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,EACpB,CAAC;AAED,SAAO;AACT;AAvDS;AAyDT,SAAS,gBAAgB,GAAG,UAAU,MAAM,YAAY;AACtD,MAAI,aAAa,IAAI,MAAM,GACzB,aAAa,EAAE,MAAM,GACrB,QAAQ,IAAI,WAAW,SAAS,WAAW,SAAS,UAAU;AAEhE,EAAE,gBAAQ,UAAU,SAAU,OAAO;AACnC,QAAI;AACJ,IAAE,gBAAQ,OAAO,SAAU,GAAG;AAC5B,UAAI,QAAQ,KAAK,CAAC;AAClB,iBAAW,QAAQ,KAAK;AACxB,UAAI,GAAG;AACL,YAAI,QAAQ,KAAK,CAAC,GAChB,UAAU,WAAW,KAAK,OAAO,KAAK;AACxC,mBAAW,QAAQ,OAAO,OAAO,KAAK,IAAI,MAAM,GAAG,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,MACzE;AACA,UAAI;AAAA,IACN,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AACT;AApBS;AAyBT,SAAS,2BAA2B,GAAG,KAAK;AAC1C,SAAS,cAAQ,eAAO,GAAG,GAAG,SAAU,IAAI;AAC1C,QAAI,MAAM,OAAO;AACjB,QAAI,MAAM,OAAO;AAEjB,IAAE,cAAM,IAAI,SAAU,GAAG,GAAG;AAC1B,UAAI,YAAY,MAAM,GAAG,CAAC,IAAI;AAE9B,YAAM,KAAK,IAAI,IAAI,WAAW,GAAG;AACjC,YAAM,KAAK,IAAI,IAAI,WAAW,GAAG;AAAA,IACnC,CAAC;AAED,WAAO,MAAM;AAAA,EACf,CAAC;AACH;AAdS;AAuBT,SAAS,iBAAiB,KAAK,SAAS;AACtC,MAAI,cAAgB,eAAO,OAAO,GAChC,aAAe,YAAI,WAAW,GAC9B,aAAe,YAAI,WAAW;AAEhC,EAAE,gBAAQ,CAAC,KAAK,GAAG,GAAG,SAAU,MAAM;AACpC,IAAE,gBAAQ,CAAC,KAAK,GAAG,GAAG,SAAU,OAAO;AACrC,UAAI,YAAY,OAAO,OACrB,KAAK,IAAI,SAAS,GAClB;AACF,UAAI,OAAO,QAAS;AAEpB,UAAI,SAAW,eAAO,EAAE;AACxB,cAAQ,UAAU,MAAM,aAAe,YAAI,MAAM,IAAI,aAAe,YAAI,MAAM;AAE9E,UAAI,OAAO;AACT,YAAI,SAAS,IAAM,kBAAU,IAAI,SAAU,GAAG;AAC5C,iBAAO,IAAI;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAtBS;AAwBT,SAAS,QAAQ,KAAK,OAAO;AAC3B,SAAS,kBAAU,IAAI,IAAI,SAAU,QAAQ,GAAG;AAC9C,QAAI,OAAO;AACT,aAAO,IAAI,MAAM,YAAY,CAAC,EAAE,CAAC;AAAA,IACnC,OAAO;AACL,UAAI,KAAO,eAAS,YAAI,KAAK,CAAC,CAAC;AAC/B,cAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAAA,IAC3B;AAAA,EACF,CAAC;AACH;AATS;AAWT,SAAS,UAAU,GAAG;AACpB,MAAI,WAAgB,iBAAiB,CAAC;AACtC,MAAI,YAAc,cAAM,mBAAmB,GAAG,QAAQ,GAAG,mBAAmB,GAAG,QAAQ,CAAC;AAExF,MAAI,MAAM,CAAC;AACX,MAAI;AACJ,EAAE,gBAAQ,CAAC,KAAK,GAAG,GAAG,SAAU,MAAM;AACpC,uBAAmB,SAAS,MAAM,WAAa,eAAO,QAAQ,EAAE,QAAQ;AACxE,IAAE,gBAAQ,CAAC,KAAK,GAAG,GAAG,SAAU,OAAO;AACrC,UAAI,UAAU,KAAK;AACjB,2BAAqB,YAAI,kBAAkB,SAAU,OAAO;AAC1D,iBAAS,eAAO,KAAK,EAAE,QAAQ;AAAA,QACjC,CAAC;AAAA,MACH;AAEA,UAAI,cAAc,SAAS,MAAM,EAAE,eAAe,EAAE,YAAY,KAAK,CAAC;AACtE,UAAI,QAAQ,kBAAkB,GAAG,kBAAkB,WAAW,UAAU;AACxE,UAAI,KAAK,qBAAqB,GAAG,kBAAkB,MAAM,MAAM,MAAM,OAAO,UAAU,GAAG;AACzF,UAAI,UAAU,KAAK;AACjB,aAAO,kBAAU,IAAI,SAAU,GAAG;AAChC,iBAAO,CAAC;AAAA,QACV,CAAC;AAAA,MACH;AACA,UAAI,OAAO,KAAK,IAAI;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AAED,MAAI,gBAAgB,2BAA2B,GAAG,GAAG;AACrD,mBAAiB,KAAK,aAAa;AACnC,SAAO,QAAQ,KAAK,EAAE,MAAM,EAAE,KAAK;AACrC;AA9BS;AAgCT,SAAS,IAAI,SAAS,SAAS,YAAY;AACzC,SAAO,SAAU,GAAG,GAAG,GAAG;AACxB,QAAI,SAAS,EAAE,KAAK,CAAC;AACrB,QAAI,SAAS,EAAE,KAAK,CAAC;AACrB,QAAI,MAAM;AACV,QAAI;AAEJ,WAAO,OAAO,QAAQ;AACtB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,UAAU,GAAG;AAC5D,cAAQ,OAAO,SAAS,YAAY,GAAG;AAAA,QACrC,KAAK;AACH,kBAAQ,CAAC,OAAO,QAAQ;AACxB;AAAA,QACF,KAAK;AACH,kBAAQ,OAAO,QAAQ;AACvB;AAAA,MACJ;AAAA,IACF;AACA,QAAI,OAAO;AACT,aAAO,aAAa,QAAQ,CAAC;AAAA,IAC/B;AACA,YAAQ;AAER,YAAQ,OAAO,QAAQ,UAAU,WAAW;AAC5C,YAAQ,OAAO,QAAQ,UAAU,WAAW;AAE5C,WAAO,OAAO,QAAQ;AACtB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,UAAU,GAAG;AAC5D,cAAQ,OAAO,SAAS,YAAY,GAAG;AAAA,QACrC,KAAK;AACH,kBAAQ,OAAO,QAAQ;AACvB;AAAA,QACF,KAAK;AACH,kBAAQ,CAAC,OAAO,QAAQ;AACxB;AAAA,MACJ;AAAA,IACF;AACA,QAAI,OAAO;AACT,aAAO,aAAa,QAAQ,CAAC;AAAA,IAC/B;AACA,YAAQ;AAER,WAAO;AAAA,EACT;AACF;AA5CS;AA8CT,SAAS,MAAM,GAAG,GAAG;AACnB,SAAO,EAAE,KAAK,CAAC,EAAE;AACnB;AAFS;;;AC9ZT,SAAS,SAAS,GAAG;AACnB,MAAS,mBAAmB,CAAC;AAE7B,YAAU,CAAC;AACX,EAAE,eAAO,UAAU,CAAC,GAAG,SAAU,GAAG,GAAG;AACrC,MAAE,KAAK,CAAC,EAAE,IAAI;AAAA,EAChB,CAAC;AACH;AAPS;AAST,SAAS,UAAU,GAAG;AACpB,MAAI,WAAgB,iBAAiB,CAAC;AACtC,MAAI,UAAU,EAAE,MAAM,EAAE;AACxB,MAAI,QAAQ;AACZ,EAAE,gBAAQ,UAAU,SAAU,OAAO;AACnC,QAAI,YAAc;AAAA,MACd,YAAI,OAAO,SAAU,GAAG;AACxB,eAAO,EAAE,KAAK,CAAC,EAAE;AAAA,MACnB,CAAC;AAAA,IACH;AACA,IAAE,gBAAQ,OAAO,SAAU,GAAG;AAC5B,QAAE,KAAK,CAAC,EAAE,IAAI,QAAQ,YAAY;AAAA,IACpC,CAAC;AACD,aAAS,YAAY;AAAA,EACvB,CAAC;AACH;AAfS;;;ACAT,SAAS,OAAO,GAAG,MAAM;AACvB,MAAIC,QAAO,QAAQ,KAAK,cAAmB,OAAY;AACvD,EAAAA,MAAK,UAAU,MAAM;AACnB,QAAI,cAAcA,MAAK,sBAAsB,MAAM,iBAAiB,CAAC,CAAC;AACtE,IAAAA,MAAK,eAAe,MAAM,UAAU,aAAaA,KAAI,CAAC;AACtD,IAAAA,MAAK,sBAAsB,MAAM,iBAAiB,GAAG,WAAW,CAAC;AAAA,EACnE,CAAC;AACH;AAPS;AAST,SAAS,UAAU,GAAGA,OAAM;AAC1B,EAAAA,MAAK,8BAA8B,MAAM,uBAAuB,CAAC,CAAC;AAClE,EAAAA,MAAK,uBAAuB,MAAM,gBAAgB,CAAC,CAAC;AACpD,EAAAA,MAAK,eAAe,MAAc,IAAI,CAAC,CAAC;AACxC,EAAAA,MAAK,wBAAwB,MAAmBC,KAAI,CAAC,CAAC;AACtD,EAAAD,MAAK,YAAY,MAAM,KAAU,mBAAmB,CAAC,CAAC,CAAC;AACvD,EAAAA,MAAK,8BAA8B,MAAM,uBAAuB,CAAC,CAAC;AAClE,EAAAA,MAAK,wBAAwB,MAAW,iBAAiB,CAAC,CAAC;AAC3D,EAAAA,MAAK,4BAA4B,MAAmB,QAAQ,CAAC,CAAC;AAC9D,EAAAA,MAAK,sBAAsB,MAAW,eAAe,CAAC,CAAC;AACvD,EAAAA,MAAK,wBAAwB,MAAM,iBAAiB,CAAC,CAAC;AACtD,EAAAA,MAAK,8BAA8B,MAAM,uBAAuB,CAAC,CAAC;AAClE,EAAAA,MAAK,qBAAqB,MAAgBC,KAAI,CAAC,CAAC;AAChD,EAAAD,MAAK,yBAAyB,MAAM,kBAAkB,CAAC,CAAC;AACxD,EAAAA,MAAK,yBAAyB,MAAM,kBAAkB,CAAC,CAAC;AACxD,EAAAA,MAAK,aAAa,MAAM,MAAM,CAAC,CAAC;AAChC,EAAAA,MAAK,uBAAuB,MAAM,gBAAgB,CAAC,CAAC;AACpD,EAAAA,MAAK,8BAA8B,MAAuB,OAAO,CAAC,CAAC;AACnE,EAAAA,MAAK,gBAAgB,MAAM,SAAS,CAAC,CAAC;AACtC,EAAAA,MAAK,yBAAyB,MAAM,kBAAkB,CAAC,CAAC;AACxD,EAAAA,MAAK,yBAAyB,MAAM,kBAAkB,CAAC,CAAC;AACxD,EAAAA,MAAK,sBAAsB,MAAgBE,MAAK,CAAC,CAAC;AAClD,EAAAF,MAAK,4BAA4B,MAAM,qBAAqB,CAAC,CAAC;AAC9D,EAAAA,MAAK,4BAA4B,MAAuB,KAAK,CAAC,CAAC;AAC/D,EAAAA,MAAK,sBAAsB,MAAM,eAAe,CAAC,CAAC;AAClD,EAAAA,MAAK,4BAA4B,MAAM,qBAAqB,CAAC,CAAC;AAC9D,EAAAA,MAAK,qBAAqB,MAAM,8BAA8B,CAAC,CAAC;AAChE,EAAAA,MAAK,oBAAoB,MAAcE,MAAK,CAAC,CAAC;AAChD;AA5BS;AAoCT,SAAS,iBAAiB,YAAY,aAAa;AACjD,EAAE,gBAAQ,WAAW,MAAM,GAAG,SAAU,GAAG;AACzC,QAAI,aAAa,WAAW,KAAK,CAAC;AAClC,QAAI,cAAc,YAAY,KAAK,CAAC;AAEpC,QAAI,YAAY;AACd,iBAAW,IAAI,YAAY;AAC3B,iBAAW,IAAI,YAAY;AAE3B,UAAI,YAAY,SAAS,CAAC,EAAE,QAAQ;AAClC,mBAAW,QAAQ,YAAY;AAC/B,mBAAW,SAAS,YAAY;AAAA,MAClC;AAAA,IACF;AAAA,EACF,CAAC;AAED,EAAE,gBAAQ,WAAW,MAAM,GAAG,SAAU,GAAG;AACzC,QAAI,aAAa,WAAW,KAAK,CAAC;AAClC,QAAI,cAAc,YAAY,KAAK,CAAC;AAEpC,eAAW,SAAS,YAAY;AAChC,QAAI,OAAO,UAAU,eAAe,KAAK,aAAa,GAAG,GAAG;AAC1D,iBAAW,IAAI,YAAY;AAC3B,iBAAW,IAAI,YAAY;AAAA,IAC7B;AAAA,EACF,CAAC;AAED,aAAW,MAAM,EAAE,QAAQ,YAAY,MAAM,EAAE;AAC/C,aAAW,MAAM,EAAE,SAAS,YAAY,MAAM,EAAE;AAClD;AA7BS;AA+BT,IAAI,gBAAgB,CAAC,WAAW,WAAW,WAAW,WAAW,SAAS;AAC1E,IAAI,gBAAgB,EAAE,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,KAAK;AAC3E,IAAI,aAAa,CAAC,aAAa,UAAU,WAAW,OAAO;AAC3D,IAAI,eAAe,CAAC,SAAS,QAAQ;AACrC,IAAI,eAAe,EAAE,OAAO,GAAG,QAAQ,EAAE;AACzC,IAAI,eAAe,CAAC,UAAU,UAAU,SAAS,UAAU,aAAa;AACxE,IAAI,eAAe;AAAA,EACjB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,UAAU;AACZ;AACA,IAAI,YAAY,CAAC,UAAU;AAQ3B,SAAS,iBAAiB,YAAY;AACpC,MAAI,IAAI,IAAI,MAAM,EAAE,YAAY,MAAM,UAAU,KAAK,CAAC;AACtD,MAAI,QAAQ,aAAa,WAAW,MAAM,CAAC;AAE3C,IAAE;AAAA,IACE,cAAM,CAAC,GAAG,eAAe,kBAAkB,OAAO,aAAa,GAAK,aAAK,OAAO,UAAU,CAAC;AAAA,EAC/F;AAEA,EAAE,gBAAQ,WAAW,MAAM,GAAG,SAAU,GAAG;AACzC,QAAI,OAAO,aAAa,WAAW,KAAK,CAAC,CAAC;AAC1C,MAAE,QAAQ,GAAK,iBAAS,kBAAkB,MAAM,YAAY,GAAG,YAAY,CAAC;AAC5E,MAAE,UAAU,GAAG,WAAW,OAAO,CAAC,CAAC;AAAA,EACrC,CAAC;AAED,EAAE,gBAAQ,WAAW,MAAM,GAAG,SAAU,GAAG;AACzC,QAAI,OAAO,aAAa,WAAW,KAAK,CAAC,CAAC;AAC1C,MAAE;AAAA,MACA;AAAA,MACE,cAAM,CAAC,GAAG,cAAc,kBAAkB,MAAM,YAAY,GAAK,aAAK,MAAM,SAAS,CAAC;AAAA,IAC1F;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAvBS;AAiCT,SAAS,uBAAuB,GAAG;AACjC,MAAI,QAAQ,EAAE,MAAM;AACpB,QAAM,WAAW;AACjB,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,SAAK,UAAU;AACf,QAAI,KAAK,SAAS,YAAY,MAAM,KAAK;AACvC,UAAI,MAAM,YAAY,QAAQ,MAAM,YAAY,MAAM;AACpD,aAAK,SAAS,KAAK;AAAA,MACrB,OAAO;AACL,aAAK,UAAU,KAAK;AAAA,MACtB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAdS;AAsBT,SAAS,uBAAuB,GAAG;AACjC,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,UAAI,IAAI,EAAE,KAAK,EAAE,CAAC;AAClB,UAAI,IAAI,EAAE,KAAK,EAAE,CAAC;AAClB,UAAI,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,MAAM,EAAK;AACzD,MAAK,aAAa,GAAG,cAAc,OAAO,KAAK;AAAA,IACjD;AAAA,EACF,CAAC;AACH;AAVS;AAYT,SAAS,iBAAiB,GAAG;AAC3B,MAAIC,WAAU;AACd,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,EAAE,KAAK,KAAK,SAAS,EAAE;AACtC,WAAK,UAAU,EAAE,KAAK,KAAK,YAAY,EAAE;AAEzC,MAAAA,WAAY,YAAIA,UAAS,KAAK,OAAO;AAAA,IACvC;AAAA,EACF,CAAC;AACD,IAAE,MAAM,EAAE,UAAUA;AACtB;AAZS;AAcT,SAAS,uBAAuB,GAAG;AACjC,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,UAAU,cAAc;AAC/B,QAAE,KAAK,KAAK,CAAC,EAAE,YAAY,KAAK;AAChC,QAAE,WAAW,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AARS;AAUT,SAAS,eAAe,GAAG;AACzB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO;AACX,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO;AACX,MAAI,aAAa,EAAE,MAAM;AACzB,MAAI,UAAU,WAAW,WAAW;AACpC,MAAI,UAAU,WAAW,WAAW;AAEpC,WAAS,YAAY,OAAO;AAC1B,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM;AACd,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,EACjC;AATS;AAWT,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,gBAAY,EAAE,KAAK,CAAC,CAAC;AAAA,EACvB,CAAC;AACD,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,kBAAY,IAAI;AAAA,IAClB;AAAA,EACF,CAAC;AAED,UAAQ;AACR,UAAQ;AAER,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,SAAK,KAAK;AACV,SAAK,KAAK;AAAA,EACZ,CAAC;AAED,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,IAAE,gBAAQ,KAAK,QAAQ,SAAU,GAAG;AAClC,QAAE,KAAK;AACP,QAAE,KAAK;AAAA,IACT,CAAC;AACD,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,WAAK,KAAK;AAAA,IACZ;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,WAAK,KAAK;AAAA,IACZ;AAAA,EACF,CAAC;AAED,aAAW,QAAQ,OAAO,OAAO;AACjC,aAAW,SAAS,OAAO,OAAO;AACpC;AAvDS;AAyDT,SAAS,qBAAqB,GAAG;AAC/B,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,QAAQ,EAAE,KAAK,EAAE,CAAC;AACtB,QAAI,QAAQ,EAAE,KAAK,EAAE,CAAC;AACtB,QAAI,IAAI;AACR,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS,CAAC;AACf,WAAK;AACL,WAAK;AAAA,IACP,OAAO;AACL,WAAK,KAAK,OAAO,CAAC;AAClB,WAAK,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAAA,IACzC;AACA,SAAK,OAAO,QAAa,cAAc,OAAO,EAAE,CAAC;AACjD,SAAK,OAAO,KAAU,cAAc,OAAO,EAAE,CAAC;AAAA,EAChD,CAAC;AACH;AAjBS;AAmBT,SAAS,qBAAqB,GAAG;AAC/B,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACnD,UAAI,KAAK,aAAa,OAAO,KAAK,aAAa,KAAK;AAClD,aAAK,SAAS,KAAK;AAAA,MACrB;AACA,cAAQ,KAAK,UAAU;AAAA,QACrB,KAAK;AACH,eAAK,KAAK,KAAK,QAAQ,IAAI,KAAK;AAChC;AAAA,QACF,KAAK;AACH,eAAK,KAAK,KAAK,QAAQ,IAAI,KAAK;AAChC;AAAA,MACJ;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAjBS;AAmBT,SAAS,8BAA8B,GAAG;AACxC,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,UAAU;AACjB,WAAK,OAAO,QAAQ;AAAA,IACtB;AAAA,EACF,CAAC;AACH;AAPS;AAST,SAAS,kBAAkB,GAAG;AAC5B,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,EAAE,SAAS,CAAC,EAAE,QAAQ;AACxB,UAAI,OAAO,EAAE,KAAK,CAAC;AACnB,UAAI,IAAI,EAAE,KAAK,KAAK,SAAS;AAC7B,UAAI,IAAI,EAAE,KAAK,KAAK,YAAY;AAChC,UAAI,IAAI,EAAE,KAAO,aAAK,KAAK,UAAU,CAAC;AACtC,UAAI,IAAI,EAAE,KAAO,aAAK,KAAK,WAAW,CAAC;AAEvC,WAAK,QAAQ,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC;AAC/B,WAAK,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC;AAChC,WAAK,IAAI,EAAE,IAAI,KAAK,QAAQ;AAC5B,WAAK,IAAI,EAAE,IAAI,KAAK,SAAS;AAAA,IAC/B;AAAA,EACF,CAAC;AAED,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,EAAE,KAAK,CAAC,EAAE,UAAU,UAAU;AAChC,QAAE,WAAW,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AArBS;AAuBT,SAAS,gBAAgB,GAAG;AAC1B,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,EAAE,MAAM,EAAE,GAAG;AACf,UAAI,OAAO,EAAE,KAAK,EAAE,CAAC;AACrB,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,YAAY,CAAC;AAAA,MACpB;AACA,WAAK,UAAU,KAAK,EAAE,GAAM,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;AAC9C,QAAE,WAAW,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AAXS;AAaT,SAAS,gBAAgB,GAAG;AAC1B,MAAI,SAAc,iBAAiB,CAAC;AACpC,EAAE,gBAAQ,QAAQ,SAAU,OAAO;AACjC,QAAI,aAAa;AACjB,IAAE,gBAAQ,OAAO,SAAU,GAAG,GAAG;AAC/B,UAAI,OAAO,EAAE,KAAK,CAAC;AACnB,WAAK,QAAQ,IAAI;AACjB,MAAE,gBAAQ,KAAK,WAAW,SAAU,UAAU;AAC5C,QAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO,SAAS,MAAM;AAAA,YACtB,QAAQ,SAAS,MAAM;AAAA,YACvB,MAAM,KAAK;AAAA,YACX,OAAO,IAAI,EAAE;AAAA,YACb,GAAG,SAAS;AAAA,YACZ,OAAO,SAAS;AAAA,UAClB;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AACD,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH,CAAC;AACH;AAzBS;AA2BT,SAAS,kBAAkB,GAAG;AAC5B,EAAE,gBAAQ,EAAE,MAAM,GAAG,SAAU,GAAG;AAChC,QAAI,OAAO,EAAE,KAAK,CAAC;AACnB,QAAI,KAAK,UAAU,YAAY;AAC7B,UAAI,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;AAC9B,UAAI,IAAI,SAAS,IAAI,SAAS,QAAQ;AACtC,UAAI,IAAI,SAAS;AACjB,UAAI,KAAK,KAAK,IAAI;AAClB,UAAI,KAAK,SAAS,SAAS;AAC3B,QAAE,QAAQ,KAAK,GAAG,KAAK,KAAK;AAC5B,QAAE,WAAW,CAAC;AACd,WAAK,MAAM,SAAS;AAAA,QAClB,EAAE,GAAG,IAAK,IAAI,KAAM,GAAG,GAAG,IAAI,GAAG;AAAA,QACjC,EAAE,GAAG,IAAK,IAAI,KAAM,GAAG,GAAG,IAAI,GAAG;AAAA,QACjC,EAAE,GAAG,IAAI,IAAI,EAAK;AAAA,QAClB,EAAE,GAAG,IAAK,IAAI,KAAM,GAAG,GAAG,IAAI,GAAG;AAAA,QACjC,EAAE,GAAG,IAAK,IAAI,KAAM,GAAG,GAAG,IAAI,GAAG;AAAA,MACnC;AACA,WAAK,MAAM,IAAI,KAAK;AACpB,WAAK,MAAM,IAAI,KAAK;AAAA,IACtB;AAAA,EACF,CAAC;AACH;AAtBS;AAwBT,SAAS,kBAAkB,KAAK,OAAO;AACrC,SAAS,kBAAY,aAAK,KAAK,KAAK,GAAG,MAAM;AAC/C;AAFS;AAIT,SAAS,aAAa,OAAO;AAC3B,MAAI,WAAW,CAAC;AAChB,EAAE,gBAAQ,OAAO,SAAU,GAAG,GAAG;AAC/B,aAAS,EAAE,YAAY,CAAC,IAAI;AAAA,EAC9B,CAAC;AACD,SAAO;AACT;AANS;", "names": ["rank", "order", "dfs", "rank", "maxRank", "addBorderNode", "g", "dfs", "undo", "run", "undo", "dfs", "rank", "dfs", "DEFAULT_WEIGHT_FUNC", "order", "postorder", "edge", "run", "dfs", "rank", "maxRank", "dfs", "entry", "maxRank", "rank", "postorder", "dfs", "order", "w", "time", "run", "undo", "maxRank"]}